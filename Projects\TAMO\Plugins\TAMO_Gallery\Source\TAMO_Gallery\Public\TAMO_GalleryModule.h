#pragma once

#include "CoreMinimal.h"
#include "Modules/ModuleManager.h"
#include "Framework/Docking/TabManager.h"
#include "Widgets/Docking/SDockTab.h"

class FTAMO_GalleryModule : public IModuleInterface
{
public:
	/** IModuleInterface implementation */
	virtual void StartupModule() override;
	virtual void ShutdownModule() override;

private:
	/** Register menu extensions */
	void RegisterMenuExtensions();
	
	/** Unregister menu extensions */
	void UnregisterMenuExtensions();
	
	/** Create the TAMO Gallery menu */
	void CreateTAMOGalleryMenu();
	
	/** Spawn CPD tab */
	TSharedRef<SDockTab> SpawnCPDTab(const FSpawnTabArgs& Args);

	/** Open CPD window */
	void OpenCPDWindow();

	/** Spawn Reference Checker tab */
	TSharedRef<SDockTab> SpawnReferenceCheckerTab(const FSpawnTabArgs& Args);

	/** Open Reference Checker window */
	void OpenReferenceCheckerWindow();

private:
	/** Tab identifiers */
	static const FName CPDTabId;
	static const FName ReferenceCheckerTabId;
	
	/** Tab manager for the CPD window */
	TSharedPtr<FTabManager> CPDTabManager;
};
