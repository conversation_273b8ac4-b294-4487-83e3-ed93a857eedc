{"Version": "1.2", "Data": {"Source": "f:\\tamo_streaming\\projects\\tamo\\plugins\\vtstools\\source\\vtstools\\private\\vtstoolsmodule.cpp", "ProvidedModule": "", "PCH": "f:\\tamo_streaming\\engine\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.cpp20.h.pch", "Includes": ["f:\\tamo_streaming\\projects\\tamo\\plugins\\vtstools\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\vtstools\\definitions.vtstools.h", "f:\\tamo_streaming\\projects\\tamo\\plugins\\vtstools\\source\\vtstools\\public\\vtstoolsmodule.h", "f:\\tamo_streaming\\engine\\source\\editor\\leveleditor\\public\\leveleditor.h", "f:\\tamo_streaming\\engine\\source\\editor\\leveleditor\\public\\ileveleditor.h", "f:\\tamo_streaming\\engine\\source\\runtime\\engine\\public\\elements\\framework\\engineelementslibrary.h", "f:\\tamo_streaming\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\engineelementslibrary.generated.h", "f:\\tamo_streaming\\engine\\source\\editor\\leveleditor\\public\\viewporttypedefinition.h", "f:\\tamo_streaming\\engine\\source\\editor\\leveleditor\\public\\leveleditoroutlinersettings.h", "f:\\tamo_streaming\\engine\\source\\developer\\toolwidgets\\public\\filters\\genericfilter.h", "f:\\tamo_streaming\\engine\\source\\developer\\toolwidgets\\public\\filters\\filterbase.h", "f:\\tamo_streaming\\projects\\tamo\\plugins\\vtstools\\source\\vtstools\\public\\scpdwindow.h", "f:\\tamo_streaming\\engine\\source\\runtime\\engine\\classes\\engine\\selection.h", "f:\\tamo_streaming\\engine\\source\\editor\\unrealed\\public\\selection.h", "f:\\tamo_streaming\\engine\\source\\runtime\\typedelementframework\\public\\elements\\framework\\typedelementlist.h", "f:\\tamo_streaming\\engine\\source\\runtime\\typedelementframework\\public\\elements\\framework\\typedelementcounter.h", "f:\\tamo_streaming\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\typedelementframework\\uht\\typedelementcounter.generated.h", "f:\\tamo_streaming\\engine\\source\\runtime\\typedelementruntime\\public\\elements\\framework\\typedelementselectionset.h", "f:\\tamo_streaming\\engine\\source\\runtime\\typedelementruntime\\public\\elements\\framework\\typedelementinterfacecustomization.h", "f:\\tamo_streaming\\engine\\source\\runtime\\typedelementruntime\\public\\elements\\framework\\typedelementlistobjectutil.h", "f:\\tamo_streaming\\engine\\source\\runtime\\typedelementruntime\\public\\elements\\interfaces\\typedelementselectioninterface.h", "f:\\tamo_streaming\\engine\\source\\runtime\\typedelementframework\\public\\elements\\framework\\typedelementlistproxy.h", "f:\\tamo_streaming\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\typedelementframework\\uht\\typedelementlistproxy.generated.h", "f:\\tamo_streaming\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\typedelementruntime\\uht\\typedelementselectioninterface.generated.h", "f:\\tamo_streaming\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\typedelementruntime\\uht\\typedelementselectionset.generated.h", "f:\\tamo_streaming\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealed\\uht\\selection.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}