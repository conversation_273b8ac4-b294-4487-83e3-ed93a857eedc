#pragma once

#include "CoreMinimal.h"
#include "Widgets/SCompoundWidget.h"

/**
 * Simple CPD Window Widget for testing compilation
 */
class VTSTOOLS_API SCPDWindow : public SCompoundWidget
{
public:
	SLATE_BEGIN_ARGS(SCPDWindow) {}
	SLATE_END_ARGS()

	/** Constructs this widget with InArgs */
	void Construct(const FArguments& InArgs);
	
	/** Destructor */
	virtual ~SCPDWindow();

private:
	/** Handle selection change */
	void OnSelectionChanged();
	
	/** Selection change delegate handle */
	FDelegateHandle SelectionChangedHandle;
};
