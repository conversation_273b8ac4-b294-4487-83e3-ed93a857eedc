# VTSTools → TAMO_Gallery 重命名指南

## 🎯 **最终重命名目标**

将整个插件从VTSTools重命名为TAMO_Gallery，包括：
- 文件夹名称
- 文件名称  
- 类名和模块名
- 所有内部引用
- 菜单显示名称

## ✅ **已完成的代码修改**

### 1. 插件描述文件修改
**VTSTools.uplugin**:
```json
// 修改前
"FriendlyName": "TA Toolbar",
"Description": "TA Toolbar plugin with CPD window functionality...",
"CreatedBy": "TA Team",
{"Name": "TAMO_Module", "Type": "Editor", "LoadingPhase": "Default"}

// 修改后  
"FriendlyName": "TAMO Gallery",
"Description": "TAMO Gallery plugin with CPD window functionality...",
"CreatedBy": "TAMO Team",
{"Name": "TAMO_Gallery", "Type": "Editor", "LoadingPhase": "Default"}
```

### 2. Build文件修改
**VTSTools.Build.cs**:
```csharp
// 修改前
public class TAMO_Module : ModuleRules
{
    public TAMO_Module(ReadOnlyTargetRules Target) : base(Target)

// 修改后
public class TAMO_Gallery : ModuleRules  
{
    public TAMO_Gallery(ReadOnlyTargetRules Target) : base(Target)
```

### 3. 模块头文件修改
**VTSToolsModule.h**:
```cpp
// 修改前
class FTAMO_ModuleModule : public IModuleInterface
void CreateTAMOModuleMenu();

// 修改后
class FTAMO_GalleryModule : public IModuleInterface
void CreateTAMOGalleryMenu();
```

### 4. 模块实现文件修改
**VTSToolsModule.cpp**:
```cpp
// 修改前
#include "TAMO_ModuleModule.h"
#define LOCTEXT_NAMESPACE "FTAMO_ModuleModule"
const FName FTAMO_ModuleModule::CPDTabId(TEXT("CPDTab"));
void FTAMO_ModuleModule::StartupModule()
void FTAMO_ModuleModule::CreateTAMOModuleMenu()
IMPLEMENT_MODULE(FTAMO_ModuleModule, TAMO_Module)

// 修改后
#include "TAMO_GalleryModule.h"
#define LOCTEXT_NAMESPACE "FTAMO_GalleryModule"  
const FName FTAMO_GalleryModule::CPDTabId(TEXT("CPDTab"));
void FTAMO_GalleryModule::StartupModule()
void FTAMO_GalleryModule::CreateTAMOGalleryMenu()
IMPLEMENT_MODULE(FTAMO_GalleryModule, TAMO_Gallery)
```

### 5. 菜单显示名称修改
```cpp
// 修改前
LOCTEXT("VTSToolsMenu", "TA Toolbar"),
LOCTEXT("VTSToolsMenuTooltip", "TA Toolbar functionality"),
LOCTEXT("VTSToolsSection", "TA Tools"),

// 修改后
LOCTEXT("VTSToolsMenu", "TAMO Gallery"),
LOCTEXT("VTSToolsMenuTooltip", "TAMO Gallery functionality"),
LOCTEXT("VTSToolsSection", "Gallery Tools"),
```

## 🚀 **需要手动完成的文件重命名**

### 步骤1: 关闭所有程序
```
1. 关闭UE编辑器
2. 关闭Visual Studio
3. 关闭所有相关程序
```

### 步骤2: 重命名文件夹结构
```
原路径: Projects/TAMO/Plugins/VTSTools/
新路径: Projects/TAMO/Plugins/TAMO_Gallery/

原路径: Projects/TAMO/Plugins/TAMO_Gallery/Source/VTSTools/
新路径: Projects/TAMO/Plugins/TAMO_Gallery/Source/TAMO_Gallery/
```

### 步骤3: 重命名文件
```
VTSTools.uplugin → TAMO_Gallery.uplugin
VTSTools.Build.cs → TAMO_Gallery.Build.cs
VTSToolsModule.h → TAMO_GalleryModule.h
VTSToolsModule.cpp → TAMO_GalleryModule.cpp
```

### 步骤4: 更新SCPDWindow.cpp中的include
```cpp
// 需要修改
#include "VTSToolsModule.h"  
// 改为
#include "TAMO_GalleryModule.h"
```

## 📋 **完整的重命名清单**

### 文件夹重命名
- [ ] `VTSTools/` → `TAMO_Gallery/`
- [ ] `Source/VTSTools/` → `Source/TAMO_Gallery/`

### 文件重命名  
- [ ] `VTSTools.uplugin` → `TAMO_Gallery.uplugin`
- [ ] `VTSTools.Build.cs` → `TAMO_Gallery.Build.cs`
- [ ] `VTSToolsModule.h` → `TAMO_GalleryModule.h`
- [ ] `VTSToolsModule.cpp` → `TAMO_GalleryModule.cpp`

### 代码引用更新
- [x] 插件描述文件中的模块名和显示名
- [x] Build.cs中的类名
- [x] 模块头文件中的类名和方法名
- [x] 模块实现文件中的所有引用
- [x] 菜单显示名称和工具提示
- [ ] SCPDWindow.cpp中的include引用

## 🎨 **界面效果对比**

### **菜单显示变化**
```
修改前: 主菜单 -> TA Toolbar -> CPD Manager
修改后: 主菜单 -> TAMO Gallery -> CPD Manager
```

### **插件信息变化**
```
修改前:
插件名称: TA Toolbar
描述: TA Toolbar plugin with CPD window functionality...
创建者: TA Team
模块名: TAMO_Module

修改后:
插件名称: TAMO Gallery
描述: TAMO Gallery plugin with CPD window functionality...
创建者: TAMO Team
模块名: TAMO_Gallery
```

### **菜单section变化**
```
修改前: TA Tools
修改后: Gallery Tools
```

## ⚠️ **重要注意事项**

### 重命名顺序
1. **先完成代码修改**（已完成）
2. **再进行文件重命名**（需要手动完成）
3. **最后更新项目引用**

### 可能的问题和解决方案

#### 问题1: 编译错误
```
解决方案: 确保所有include路径正确更新
检查: #include "TAMO_GalleryModule.h"
```

#### 问题2: 模块加载失败
```
解决方案: 检查.uplugin文件中的模块名是否正确
确认: "Name": "TAMO_Gallery"
```

#### 问题3: 菜单不显示
```
解决方案: 重新生成项目文件
操作: 右键.uproject → Generate Visual Studio project files
```

## 🚀 **重命名后的验证步骤**

### 1. 编译测试
```bash
Build -> Rebuild Solution
```

### 2. 功能测试
```
1. 启动UE编辑器
2. 检查菜单: TAMO Gallery -> CPD Manager
3. 测试CPD功能是否正常
4. 检查插件管理器中的显示名称
```

### 3. 插件信息验证
```
Edit -> Plugins -> 搜索 "TAMO_Gallery"
确认显示: "TAMO Gallery"
```

## 📁 **最终的文件结构**

```
Projects/TAMO/Plugins/TAMO_Gallery/
├── TAMO_Gallery.uplugin
├── Source/
│   └── TAMO_Gallery/
│       ├── TAMO_Gallery.Build.cs
│       ├── Public/
│       │   ├── TAMO_GalleryModule.h
│       │   └── SCPDWindow.h
│       └── Private/
│           ├── TAMO_GalleryModule.cpp
│           └── SCPDWindow.cpp
```

## 🎯 **CPD功能保持不变**

重命名后，所有CPD功能完全保持：
- ✅ **拖动调节**: SSpinBox支持-100到100范围拖动
- ✅ **重置功能**: ↺按钮恢复材质默认值
- ✅ **高亮功能**: 右键切换参数高亮
- ✅ **多选处理**: Multiple Values智能显示

## 状态: 🔄 代码修改完成，等待文件重命名

- ✅ **代码修改**: 所有类名、方法名、引用已更新为TAMO_Gallery
- ✅ **菜单名称**: 更新为"TAMO Gallery"和"Gallery Tools"
- ⏳ **文件重命名**: 需要手动重命名文件和文件夹
- ⏳ **最终测试**: 重命名完成后需要编译测试

完成文件重命名后，TAMO_Gallery插件就可以正常使用了！🎉
