﻿Log file open, 08/05/25 22:03:53
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: TAMO
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Display Control listening on port 1985
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="UE5-CL-0"
LogCsvProfiler: Display: Metadata set : engineversion="5.5.1-0+UE5"
LogCsvProfiler: Display: Metadata set : os="Windows 10 (22H2) [10.0.19045.6093] "
LogCsvProfiler: Display: Metadata set : cpu="GenuineIntel|Intel(R) Xeon(R) W-2255 CPU @ 3.70GHz"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" TAMO""
LogCsvProfiler: Display: Metadata set : loginid="0cf912ee4b52d61c1327948a4429657f"
LogCsvProfiler: Display: Metadata set : llm="0"
LogInit: Display: Project file not found: F:/TAMO_Streaming/TAMO/TAMO.uproject
LogInit: Display: 	Attempting to find via project info helper.
LogInit: Display: 	Found project file ../../../Projects/TAMO/TAMO.uproject.
LogStats: Stats thread started at 0.483053
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetActorFactory id: 0
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetSubObjectFactory id: 1
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: +8:00, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-CBE634D6432066AC1AC0B7877C0AA22B
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [../../../Projects/TAMO/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogPluginManager: Looking for build plugins target receipt
LogConfig: Display: Loading Mac ini files took 0.09 seconds
LogConfig: Display: Loading VulkanPC ini files took 0.10 seconds
LogConfig: Display: Loading GDK ini files took 0.10 seconds
LogConfig: Display: Loading Android ini files took 0.10 seconds
LogConfig: Display: Loading Nintendo ini files took 0.10 seconds
LogPluginManager: Found matching target receipt: ../../../Projects/TAMO/Binaries/Win64/TAMOEditor.target
LogPluginManager: Looking for enabled plugins target receipt
LogConfig: Display: Loading Sony ini files took 0.11 seconds
LogConfig: Display: Loading IOS ini files took 0.11 seconds
LogConfig: Display: Loading Unix ini files took 0.12 seconds
LogConfig: Display: Loading TVOS ini files took 0.13 seconds
LogConfig: Display: Loading Windows ini files took 0.13 seconds
LogConfig: Display: Loading VisionOS ini files took 0.13 seconds
LogAssetRegistry: Display: Asset registry cache read as 61.7 MiB from ../../../Projects/TAMO/Intermediate/CachedAssetRegistry_0.bin
LogConfig: Display: Loading Switch ini files took 0.14 seconds
LogConfig: Display: Loading PS5 ini files took 0.14 seconds
LogConfig: Display: Loading PS4 ini files took 0.14 seconds
LogConfig: Display: Loading LinuxArm64 ini files took 0.15 seconds
LogConfig: Display: Loading Linux ini files took 0.15 seconds
LogPluginManager: Found matching target receipt: ../../../Projects/TAMO/Binaries/Win64/TAMOEditor.target
LogConfig: Display: Loading XboxCommon ini files took 0.06 seconds
LogConfig: Display: Loading XB1 ini files took 0.08 seconds
LogConfig: Display: Loading XSX ini files took 0.08 seconds
LogConfig: Display: Loading WinGDK ini files took 0.09 seconds
LogPluginManager: Mounting Engine plugin Bridge
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin Fab
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin PCG
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin WorldMetrics
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin DatasmithContent
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin GeometryScripting
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin HairStrands
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin USDCore
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin Paper2D
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin ACLPlugin
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin LiveLink
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin BlueprintMaterialTextureNodes
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin Landmass
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin StudioTelemetry
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin Volumetrics
LogPluginManager: Mounting Engine plugin Niagara
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin PCGExternalDataInterop
LogPluginManager: Mounting Engine plugin PCGGeometryScriptInterop
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin ConcertMain
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Engine plugin SQLiteCore
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin ObjectMixer
LogPluginManager: Mounting Engine plugin HoldoutComposite
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin ConcertSyncClient
LogPluginManager: Mounting Engine plugin ConcertSyncCore
LogPluginManager: Mounting Project plugin VTSTools
LogPluginManager: Mounting Project plugin VTS_Tools
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
LogEOSSDK: Initializing EOSSDK Version:1.16.4-36651368
LogInit: Using libcurl 8.4.0
LogInit:  - built for Windows
LogInit:  - supports SSL with OpenSSL/1.1.1t
LogInit:  - supports HTTP deflate (compression) using libz 1.3
LogInit:  - other features:
LogInit:      CURL_VERSION_SSL
LogInit:      CURL_VERSION_LIBZ
LogInit:      CURL_VERSION_IPV6
LogInit:      CURL_VERSION_ASYNCHDNS
LogInit:      CURL_VERSION_LARGEFILE
LogInit:      CURL_VERSION_HTTP2
LogInit:  CurlRequestOptions (configurable via config and command line):
LogInit:  - bVerifyPeer = true  - Libcurl will verify peer certificate
LogInit:  - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
LogInit:  - bDontReuseConnections = false  - Libcurl will reuse connections
LogInit:  - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
LogInit:  - LocalHostAddr = Default
LogInit:  - BufferSize = 65536
LogInit: CreateHttpThread using FCurlMultiPollEventLoopHttpThread
LogInit: Creating http thread with maximum ********** concurrent requests
LogInit: WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
LogOnline: OSS: Created online subsystem instance for: NULL
LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
LogWindows: Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
LogWindows: File 'WinPixGpuCapturer.dll' does not exist
PixWinPlugin: PIX capture plugin failed to initialize! Check that the process is launched from PIX.
LogConfig: Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
LogConfig: Set CVar [[renderdoc.AutoAttach:1]]
LogConfig: Set CVar [[renderdoc.BinaryPath:C:\Program Files\RenderDoc]]
RenderDocPlugin: locating RenderDoc library (renderdoc.dll)...
RenderDocPlugin: a RenderDoc library has been located at: C:\Program Files\RenderDoc/renderdoc.dll
RenderDocPlugin: RenderDoc library has been loaded (RenderDoc API v1.6.0).
RenderDocPlugin: plugin has been loaded successfully.
RenderDocPlugin: RenderDoc plugin is ready!
LogNFORDenoise: NFORDenoise function starting up
LogStudioTelemetry: Display: Starting StudioTelemetry Module
LogStudioTelemetry: Started StudioTelemetry Session
LogInit: ExecutableName: UnrealEditor.exe
LogInit: Build: UE5-CL-0
LogInit: Platform=WindowsEditor
LogInit: MachineId=0cf912ee4b52d61c1327948a4429657f
LogInit: DeviceId=
LogInit: Engine Version: 5.5.1-0+UE5
LogInit: Compatible Engine Version: 5.5.0-0+UE5
LogInit: Net CL: 0
LogInit: OS: Windows 10 (22H2) [10.0.19045.6093] (), CPU: Intel(R) Xeon(R) W-2255 CPU @ 3.70GHz, GPU: Parsec Virtual Display Adapter
LogInit: Compiled (64-bit): Apr 16 2025 17:50:44
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33135.00
LogInit: Build Configuration: Development
LogInit: Branch Name: UE5
LogInit: Command Line: 
LogInit: Base Directory: F:/TAMO_Streaming/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 0
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 36
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 170
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 15
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 10
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 119
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 51
LogDevObjectVersion:   UE5-PrivateFrosty (59DA5D52-1232-4948-B878-597870B8E98B): 8
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogDevObjectVersion:   Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
LogConfig: Branch 'EditorLayout' had been unloaded. Reloading on-demand took 1.28ms
LogConfig: Branch 'Bridge' had been unloaded. Reloading on-demand took 0.16ms
LogConfig: Branch 'ChaosCloth' had been unloaded. Reloading on-demand took 0.19ms
LogConfig: Branch 'CmdLinkServer' had been unloaded. Reloading on-demand took 0.15ms
LogConfig: Branch 'FastBuildController' had been unloaded. Reloading on-demand took 0.15ms
LogConfig: Branch 'MeshPainting' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'RenderGraphInsights' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'UbaController' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'WorldMetrics' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'XGEController' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'OodleNetwork' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'VariantManager' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'AlembicImporter' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'EOSShared' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'OnlineBase' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'OnlineServices' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'OnlineSubsystem' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'OnlineSubsystemNull' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'OnlineSubsystemUtils' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'TcpMessaging' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'UdpMessaging' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'LauncherChunkInstaller' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'ActorLayerUtilities' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'AndroidDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'AndroidFileServer' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'AndroidMoviePlayer' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'AndroidPermission' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'AppleMoviePlayer' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'ArchVisCharacter' had been unloaded. Reloading on-demand took 0.15ms
LogConfig: Branch 'AppleImageUtils' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'AudioCapture' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'AssetTags' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'AudioSynesthesia' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'AudioWidgets' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'ChunkDownloader' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'CableComponent' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'ComputeFramework' had been unloaded. Reloading on-demand took 0.12ms
LogConfig: Branch 'CustomMeshComponent' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'ExampleDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'GeometryCache' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'GeometryProcessing' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'GeometryScripting' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'GoogleCloudMessaging' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'GooglePAD' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'InputDebugging' had been unloaded. Reloading on-demand took 0.15ms
LogConfig: Branch 'IOSDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'LinuxDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'LocationServicesBPLibrary' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'MeshModelingToolset' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'MobilePatchingUtils' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'MsQuic' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'PropertyAccessEditor' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'ProceduralMeshComponent' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'ResonanceAudio' had been unloaded. Reloading on-demand took 0.16ms
LogConfig: Branch 'SignificanceManager' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'SoundFields' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'WaveTable' had been unloaded. Reloading on-demand took 0.16ms
LogConfig: Branch 'WebMMoviePlayer' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'WindowsMoviePlayer' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'WindowsDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'AISupport' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'EnvironmentQueryEditor' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'ACLPlugin' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'AnimationModifierLibrary' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'AnimationData' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'BlendSpaceMotionAnalysis' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'ControlRigModules' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'ControlRigSpline' had been unloaded. Reloading on-demand took 0.12ms
LogConfig: Branch 'EngineCameras' had been unloaded. Reloading on-demand took 0.20ms
LogConfig: Branch 'RigLogic' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'CameraShakePreviewer' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'AnimationSharing' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'CLionSourceCodeAccess' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'CodeLiteSourceCodeAccess' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'DumpGPUServices' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'GitSourceControl' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'KDevelopSourceCodeAccess' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'NullSourceCodeAccess' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'N10XSourceCodeAccess' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'PerforceSourceControl' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'PixWinPlugin' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'PlasticSourceControl' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'RiderSourceCodeAccess' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'PropertyAccessNode' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'SubversionSourceControl' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'RenderDocPlugin' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'PluginUtils' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'TextureFormatOodle' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'VisualStudioCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'UObjectPlugin' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'VisualStudioSourceCodeAccess' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'XCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'AssetManagerEditor' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'BlueprintHeaderView' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'BlueprintMaterialTextureNodes' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'ColorGrading' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'CryptoKeys' had been unloaded. Reloading on-demand took 0.12ms
LogConfig: Branch 'ChangelistReview' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'CurveEditorTools' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'EditorDebugTools' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'DataValidation' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'GameplayTagsEditor' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'EngineAssetDefinitions' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'FacialAnimation' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'GeometryMode' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'MacGraphicsSwitching' had been unloaded. Reloading on-demand took 0.15ms
LogConfig: Branch 'MeshLODToolset' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'MobileLauncherProfileWizard' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'MaterialAnalyzer' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'ModelingToolsEditorMode' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'ProxyLODPlugin' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'PluginBrowser' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'SequencerAnimTools' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'SpeedTreeImporter' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'UMGWidgetPreview' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'StylusInput' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'UVEditor' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'WorldPartitionHLODUtilities' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'AdvancedRenamer' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'AutomationUtils' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'BackChannel' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'ChaosCaching' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'ChaosEditor' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'CharacterAI' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'ChaosUserDataPT' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'ChaosSolverPlugin' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'ChaosNiagara' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'Dataflow' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'EditorDataStorage' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'EditorPerformance' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'EditorTelemetry' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'Fracture' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'GeometryFlow' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'GeometryCollectionPlugin' had been unloaded. Reloading on-demand took 0.21ms
LogConfig: Branch 'Landmass' had been unloaded. Reloading on-demand took 0.15ms
LogConfig: Branch 'LowLevelNetTrace' had been unloaded. Reloading on-demand took 0.15ms
LogConfig: Branch 'MeshModelingToolsetExp' had been unloaded. Reloading on-demand took 0.15ms
LogConfig: Branch 'LocalizableMessage' had been unloaded. Reloading on-demand took 0.16ms
LogConfig: Branch 'NFORDenoise' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'PlanarCut' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'PlatformCrypto' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'PythonScriptPlugin' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'SkeletalReduction' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'StudioTelemetry' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'Volumetrics' had been unloaded. Reloading on-demand took 0.15ms
LogConfig: Branch 'NiagaraSimCaching' had been unloaded. Reloading on-demand took 0.16ms
LogConfig: Branch 'InterchangeEditor' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'AndroidMedia' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'AvfMedia' had been unloaded. Reloading on-demand took 0.16ms
LogConfig: Branch 'MediaCompositing' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'ImgMedia' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'MediaPlate' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'MediaPlayerEditor' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'WebMMedia' had been unloaded. Reloading on-demand took 0.16ms
LogConfig: Branch 'ActorSequence' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'WmfMedia' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'LevelSequenceEditor' had been unloaded. Reloading on-demand took 0.23ms
LogConfig: Branch 'SequencerScripting' had been unloaded. Reloading on-demand took 0.21ms
LogConfig: Branch 'TemplateSequence' had been unloaded. Reloading on-demand took 0.21ms
LogConfig: Branch 'NNEDenoiser' had been unloaded. Reloading on-demand took 0.21ms
LogConfig: Branch 'PCGExternalDataInterop' had been unloaded. Reloading on-demand took 0.22ms
LogConfig: Branch 'NNERuntimeORT' had been unloaded. Reloading on-demand took 0.21ms
LogConfig: Branch 'InterchangeTests' had been unloaded. Reloading on-demand took 0.21ms
LogConfig: Branch 'ConcertMain' had been unloaded. Reloading on-demand took 0.22ms
LogConfig: Branch 'OnlineSubsystemGooglePlay' had been unloaded. Reloading on-demand took 0.23ms
LogConfig: Branch 'PortableObjectFileDataSource' had been unloaded. Reloading on-demand took 0.22ms
LogConfig: Branch 'ContentBrowserClassDataSource' had been unloaded. Reloading on-demand took 0.23ms
LogConfig: Branch 'ContentBrowserFileDataSource' had been unloaded. Reloading on-demand took 0.22ms
LogConfig: Branch 'ContentBrowserAssetDataSource' had been unloaded. Reloading on-demand took 0.22ms
LogConfig: Branch 'BaseCharacterFXEditor' had been unloaded. Reloading on-demand took 0.21ms
LogConfig: Branch 'XInputDevice' had been unloaded. Reloading on-demand took 0.20ms
LogConfig: Branch 'SQLiteCore' had been unloaded. Reloading on-demand took 0.21ms
LogConfig: Branch 'LightMixer' had been unloaded. Reloading on-demand took 0.21ms
LogConfig: Branch 'ObjectMixer' had been unloaded. Reloading on-demand took 0.20ms
LogConfig: Branch 'SkeletalMeshModelingTools' had been unloaded. Reloading on-demand took 0.23ms
LogConfig: Branch 'OnlineSubsystemIOS' had been unloaded. Reloading on-demand took 0.21ms
LogConfig: Branch 'ConcertSyncClient' had been unloaded. Reloading on-demand took 0.22ms
LogConfig: Branch 'VTSTools' had been unloaded. Reloading on-demand took 0.21ms
LogConfig: Branch 'VTS_Tools' had been unloaded. Reloading on-demand took 0.20ms
LogInit: Presizing for max 25165824 objects, including 0 objects not considered by GC.
LogStreaming: Display: AsyncLoading2 - Created: Event Driven Loader: false, Async Loading Thread: false, Async Post Load: false
LogStreaming: Display: AsyncLoading2 - Initialized
LogInit: Object subsystem initialized
LogConfig: Set CVar [[con.DebugEarlyDefault:1]]
LogConfig: CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
LogConfig: CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
LogConfig: CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
LogConfig: Set CVar [[r.setres:1280x720]]
LogConfig: CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
LogConfig: Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
LogConfig: CVar [[QualityLevelMapping:high]] deferred - dummy variable created
LogConfig: CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
LogConfig: Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
LogConfig: Set CVar [[r.Nanite.Streaming.AsyncCompute:0	; Temporary workaround for Nanite geometry corruption (FORT-805141)]]
LogConfig: CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
LogConfig: CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
LogConfig: Set CVar [[r.PSOPrecache.GlobalShaders:1]]
LogConfig: Set CVar [[r.DynamicRes.DynamicFrameTime:1]]
LogConfig: Set CVar [[r.VRS.EnableSoftware:1]]
LogConfig: Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[r.VSync:0]]
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[r.RHICmdBypass:0]]
[2025.08.05-14.03.54:783][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[r.GPUCrashDebugging:0]]
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[r.GenerateMeshDistanceFields:1]]
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[r.ReflectionMethod:1]]
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[r.SkinCache.CompileShaders:1]]
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[r.RayTracing:1]]
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[r.Shadow.Virtual.Enable:0]]
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange:1]]
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.HighlightContrastScale:0.8]]
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.ShadowContrastScale:0.8]]
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[r.VirtualTextures:1]]
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
[2025.08.05-14.03.54:783][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
[2025.08.05-14.03.54:783][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[s.AsyncLoadingThreadEnabled:1]]
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[s.EventDrivenLoaderEnabled:1]]
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[s.WarnIfTimeLimitExceeded:0]]
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[s.TimeLimitExceededMinTime:0.005]]
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[s.UseBackgroundLevelStreaming:1]]
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[s.FlushStreamingOnExit:1]]
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[gc.FlushStreamingOnGC:0]]
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[gc.AllowParallelGC:1]]
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[gc.MaxObjectsInEditor:25165824]]
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[gc.CreateGCClusters:1]]
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[gc.MinGCClusterSize:5]]
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[gc.AssetClustreringEnabled:0]]
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[gc.ActorClusteringEnabled:0]]
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
[2025.08.05-14.03.54:783][  0]LogConfig: Set CVar [[gc.GarbageEliminationEnabled:1]]
[2025.08.05-14.03.54:783][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
[2025.08.05-14.03.54:783][  0]LogConfig: CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
[2025.08.05-14.03.54:798][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
[2025.08.05-14.03.54:798][  0]LogConfig: Set CVar [[r.SkeletalMeshLODBias:0]]
[2025.08.05-14.03.54:798][  0]LogConfig: Set CVar [[r.ViewDistanceScale:1.0]]
[2025.08.05-14.03.54:798][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
[2025.08.05-14.03.54:798][  0]LogConfig: Set CVar [[r.FXAA.Quality:4]]
[2025.08.05-14.03.54:798][  0]LogConfig: Set CVar [[r.TemporalAA.Quality:2]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.TSR.History.R11G11B10:1]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.TSR.History.ScreenPercentage:200]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.TSR.History.UpdateQuality:3]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.TSR.ReprojectionField:1]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.TSR.Resurrection:1]]
[2025.08.05-14.03.54:799][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.LightFunctionQuality:1]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.ShadowQuality:5]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Shadow.MaxResolution:2048]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Shadow.RadiusThreshold:0.01]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Shadow.DistanceScale:1.0]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.DistanceFieldShadowing:1]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.VolumetricFog:1]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.CapsuleShadows:1]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
[2025.08.05-14.03.54:799][  0]LogConfig: Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.DistanceFieldAO:1]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.AOQuality:2]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:300]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:200]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.SkyLight.RealTimeReflectionCapture:1]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.RayTracing.Scene.BuildMode:1]]
[2025.08.05-14.03.54:799][  0]LogConfig: Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.SSR.Quality:3]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.SSR.HalfResSceneColor:0]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Lumen.Reflections.Allow:1]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.TonemapMode:1]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
[2025.08.05-14.03.54:799][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.MotionBlurQuality:4]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.MotionBlur.HalfResGather:0]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.AmbientOcclusionLevels:-1]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.DepthOfFieldQuality:2]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.RenderTargetPoolMin:400]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.LensFlareQuality:2]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.SceneColorFringeQuality:1]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.EyeAdaptationQuality:2]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.BloomQuality:5]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Bloom.ScreenPercentage:50.000]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.FastBlurThreshold:100]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Upscale.Quality:3]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.LightShaftQuality:1]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Filter.SizeScale:1]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Tonemapper.Quality:5]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.08.05-14.03.54:799][  0]LogConfig: Applying CVar settings from Section [TextureQuality@3] File [Scalability]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Streaming.MipBias:0]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Streaming.Boost:1]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.MaxAnisotropy:8]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.VT.MaxAnisotropy:8]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Streaming.PoolSize:1000]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.08.05-14.03.54:799][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.TranslucencyLightingVolumeDim:64]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.RefractionQuality:2]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.SceneColorFormat:4]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.DetailMode:3]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.TranslucencyVolumeBlur:1]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.SSS.Scale:1]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.SSS.SampleSet:2]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.SSS.Quality:1]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.SSS.HalfRes:0]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.SSGI.Quality:3]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.EmitterSpawnRateScale:1.0]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.ParticleLightQuality:2]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[fx.Niagara.QualityLevel:3]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.Refraction.OffsetQuality:1]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:1]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.MaxStepCount:512]]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:512]]
[2025.08.05-14.03.54:799][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
[2025.08.05-14.03.54:799][  0]LogConfig: Set CVar [[foliage.DensityScale:1.0]]
[2025.08.05-14.03.54:800][  0]LogConfig: Set CVar [[grass.DensityScale:1.0]]
[2025.08.05-14.03.54:800][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
[2025.08.05-14.03.54:800][  0]LogConfig: Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.08.05-14.03.54:800][  0]LogConfig: Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.08.05-14.03.54:800][  0]LogConfig: Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.08.05-14.03.54:800][  0]LogConfig: Set CVar [[r.AnisotropicMaterials:1]]
[2025.08.05-14.03.54:800][  0]LogConfig: Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
[2025.08.05-14.03.54:812][  0]LogRHI: Using Default RHI: D3D12
[2025.08.05-14.03.54:812][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.08.05-14.03.54:812][  0]LogRHI: Loading RHI module D3D12RHI
[2025.08.05-14.03.54:836][  0]LogD3D12RHI: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
[2025.08.05-14.03.54:836][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.08.05-14.03.55:368][  0]LogD3D12RHI: Found D3D12 adapter 0: NVIDIA GeForce RTX 4070 (VendorId: 10de, DeviceId: 2786, SubSysId: 40ee1458, Revision: 00a1
[2025.08.05-14.03.55:368][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.08.05-14.03.55:368][  0]LogD3D12RHI:   Adapter has 12012MB of dedicated video memory, 0MB of dedicated system memory, and 65378MB of shared system memory, 2 output[s]
[2025.08.05-14.03.55:369][  0]LogD3D12RHI:   Driver Version: 560.94 (internal:32.0.15.6094, unified:560.94)
[2025.08.05-14.03.55:369][  0]LogD3D12RHI:      Driver Date: 8-14-2024
[2025.08.05-14.03.55:401][  0]LogD3D12RHI: Found D3D12 adapter 1: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.08.05-14.03.55:401][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.2, binding tier 3, wave ops supported, atomic64 unsupported
[2025.08.05-14.03.55:401][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 65378MB of shared system memory, 0 output[s]
[2025.08.05-14.03.55:434][  0]LogD3D12RHI: Found D3D12 adapter 2: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.08.05-14.03.55:434][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.2, binding tier 3, wave ops supported, atomic64 unsupported
[2025.08.05-14.03.55:434][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 65378MB of shared system memory, 0 output[s]
[2025.08.05-14.03.55:697][  0]LogD3D12RHI: Found D3D12 adapter 3: NVIDIA GeForce RTX 4070 (VendorId: 10de, DeviceId: 2786, SubSysId: 40ee1458, Revision: 00a1
[2025.08.05-14.03.55:697][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.08.05-14.03.55:697][  0]LogD3D12RHI:   Adapter has 12012MB of dedicated video memory, 0MB of dedicated system memory, and 65378MB of shared system memory, 0 output[s]
[2025.08.05-14.03.55:698][  0]LogD3D12RHI:   Driver Version: 560.94 (internal:32.0.15.6094, unified:560.94)
[2025.08.05-14.03.55:698][  0]LogD3D12RHI:      Driver Date: 8-14-2024
[2025.08.05-14.03.55:942][  0]LogD3D12RHI: Found D3D12 adapter 4: NVIDIA GeForce RTX 4070 (VendorId: 10de, DeviceId: 2786, SubSysId: 40ee1458, Revision: 00a1
[2025.08.05-14.03.55:943][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.08.05-14.03.55:943][  0]LogD3D12RHI:   Adapter has 12012MB of dedicated video memory, 0MB of dedicated system memory, and 65378MB of shared system memory, 0 output[s]
[2025.08.05-14.03.55:943][  0]LogD3D12RHI:   Driver Version: 560.94 (internal:32.0.15.6094, unified:560.94)
[2025.08.05-14.03.55:943][  0]LogD3D12RHI:      Driver Date: 8-14-2024
[2025.08.05-14.03.56:190][  0]LogD3D12RHI: Found D3D12 adapter 5: NVIDIA GeForce RTX 4070 (VendorId: 10de, DeviceId: 2786, SubSysId: 40ee1458, Revision: 00a1
[2025.08.05-14.03.56:190][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.08.05-14.03.56:190][  0]LogD3D12RHI:   Adapter has 12012MB of dedicated video memory, 0MB of dedicated system memory, and 65378MB of shared system memory, 0 output[s]
[2025.08.05-14.03.56:191][  0]LogD3D12RHI:   Driver Version: 560.94 (internal:32.0.15.6094, unified:560.94)
[2025.08.05-14.03.56:191][  0]LogD3D12RHI:      Driver Date: 8-14-2024
[2025.08.05-14.03.56:191][  0]LogD3D12RHI: DirectX Agility SDK runtime found.
[2025.08.05-14.03.56:191][  0]LogD3D12RHI: Chosen D3D12 Adapter Id = 0
[2025.08.05-14.03.56:191][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.08.05-14.03.56:191][  0]LogInit: Selected Device Profile: [WindowsEditor]
[2025.08.05-14.03.56:191][  0]LogHAL: Display: Platform has ~ 128 GB [137107709952 / 137438953472 / 128], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
[2025.08.05-14.03.56:192][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [Windows]
[2025.08.05-14.03.56:192][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.05-14.03.56:192][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
[2025.08.05-14.03.56:192][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
[2025.08.05-14.03.56:192][  0]LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.08.05-14.03.56:192][  0]LogConfig: Set CVar [[r.DumpShaderDebugInfo:2]]
[2025.08.05-14.03.56:192][  0]LogConfig: Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
[2025.08.05-14.03.56:192][  0]LogConfig: Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
[2025.08.05-14.03.56:192][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.08.05-14.03.56:192][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.08.05-14.03.56:192][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.08.05-14.03.56:192][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.08.05-14.03.56:192][  0]LogConfig: Set CVar [[memory.MemoryPressureCriticalThresholdMB:512]]
[2025.08.05-14.03.56:192][  0]LogConfig: CVar [[r.CEFGPUAcceleration:1]] deferred - dummy variable created
[2025.08.05-14.03.56:192][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [../../../Projects/TAMO/Saved/Config/WindowsEditor/Editor.ini]
[2025.08.05-14.03.56:193][  0]LogInit: Computer: CNCDUW1174
[2025.08.05-14.03.56:193][  0]LogInit: User: zengyanjia
[2025.08.05-14.03.56:193][  0]LogInit: CPU Page size=4096, Cores=10
[2025.08.05-14.03.56:193][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.08.05-14.03.57:103][  0]LogMemory: Memory total: Physical=127.7GB (128GB approx) Virtual=153.3GB
[2025.08.05-14.03.57:104][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.08.05-14.03.57:104][  0]LogMemory: Process Physical Memory: 687.77 MB used, 702.39 MB peak
[2025.08.05-14.03.57:104][  0]LogMemory: Process Virtual Memory: 750.80 MB used, 750.80 MB peak
[2025.08.05-14.03.57:104][  0]LogMemory: Physical Memory: 73402.13 MB used,  57353.97 MB free, 130756.10 MB total
[2025.08.05-14.03.57:104][  0]LogMemory: Virtual Memory: 113651.69 MB used,  43361.20 MB free, 157012.89 MB total
[2025.08.05-14.03.57:104][  0]LogCsvProfiler: Display: Metadata set : extradevelopmentmemorymb="0"
[2025.08.05-14.03.57:117][  0]LogWindows: WindowsPlatformFeatures enabled
[2025.08.05-14.03.57:154][  0]LogChaosDD: Chaos Debug Draw Startup
[2025.08.05-14.03.57:155][  0]LogInit: Physics initialised using underlying interface: Chaos
[2025.08.05-14.03.57:155][  0]LogInit: Using OS detected language (en-US).
[2025.08.05-14.03.57:155][  0]LogInit: Using OS detected locale (en-US).
[2025.08.05-14.03.57:158][  0]LogTextLocalizationManager: No specific localization for 'en-US' exists, so 'en' will be used for the language.
[2025.08.05-14.03.57:158][  0]LogInit: Setting process to per monitor DPI aware
[2025.08.05-14.03.57:750][  0]LogWindowsTextInputMethodSystem: Available input methods:
[2025.08.05-14.03.57:750][  0]LogWindowsTextInputMethodSystem:   - English (United States) - (Keyboard).
[2025.08.05-14.03.57:750][  0]LogWindowsTextInputMethodSystem:   - Chinese (Simplified, China) - 搜狗拼音输入法 (TSF IME).
[2025.08.05-14.03.57:751][  0]LogWindowsTextInputMethodSystem: Activated input method: English (United States) - (Keyboard).
[2025.08.05-14.03.57:809][  0]LogSlate: New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
[2025.08.05-14.03.57:809][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.08.05-14.03.58:362][  0]LogRHI: Using Default RHI: D3D12
[2025.08.05-14.03.58:363][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.08.05-14.03.58:363][  0]LogRHI: Loading RHI module D3D12RHI
[2025.08.05-14.03.58:363][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.08.05-14.03.58:363][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.08.05-14.03.58:363][  0]LogD3D12RHI: Display: Creating D3D12 RHI with Max Feature Level SM6
[2025.08.05-14.03.58:363][  0]LogWindows: Attached monitors:
[2025.08.05-14.03.58:363][  0]LogWindows:     resolution: 2560x1440, work area: (0, 0) -> (2560, 1390), device: '\\.\DISPLAY29' [PRIMARY]
[2025.08.05-14.03.58:363][  0]LogWindows:     resolution: 1920x1080, work area: (2560, 0) -> (4480, 1040), device: '\\.\DISPLAY30'
[2025.08.05-14.03.58:363][  0]LogWindows: Found 2 attached monitors.
[2025.08.05-14.03.58:363][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.08.05-14.03.58:364][  0]LogRHI: RHI Adapter Info:
[2025.08.05-14.03.58:364][  0]LogRHI:             Name: NVIDIA GeForce RTX 4070
[2025.08.05-14.03.58:364][  0]LogRHI:   Driver Version: 560.94 (internal:32.0.15.6094, unified:560.94)
[2025.08.05-14.03.58:364][  0]LogRHI:      Driver Date: 8-14-2024
[2025.08.05-14.03.58:364][  0]LogD3D12RHI:     GPU DeviceId: 0x2786 (for the marketing name, search the web for "GPU Device Id")
[2025.08.05-14.03.58:364][  0]LogD3D12RHI: InitD3DDevice: -D3DDebug = off -D3D12GPUValidation = off
[2025.08.05-14.03.58:569][  0]LogNvidiaAftermath: Aftermath initialized
[2025.08.05-14.03.58:570][  0]LogD3D12RHI: Emitting draw events for PIX profiling.
[2025.08.05-14.03.58:671][  0]LogNvidiaAftermath: Aftermath enabled but failed to initialize (bad0000a).
[2025.08.05-14.03.58:671][  0]LogD3D12RHI: ID3D12Device1 is supported.
[2025.08.05-14.03.58:671][  0]LogD3D12RHI: ID3D12Device2 is supported.
[2025.08.05-14.03.58:671][  0]LogD3D12RHI: ID3D12Device3 is supported.
[2025.08.05-14.03.58:671][  0]LogD3D12RHI: ID3D12Device4 is supported.
[2025.08.05-14.03.58:671][  0]LogD3D12RHI: ID3D12Device5 is supported.
[2025.08.05-14.03.58:671][  0]LogD3D12RHI: ID3D12Device6 is supported.
[2025.08.05-14.03.58:671][  0]LogD3D12RHI: ID3D12Device7 is supported.
[2025.08.05-14.03.58:671][  0]LogD3D12RHI: ID3D12Device8 is supported.
[2025.08.05-14.03.58:671][  0]LogD3D12RHI: ID3D12Device9 is supported.
[2025.08.05-14.03.58:671][  0]LogD3D12RHI: ID3D12Device10 is supported.
[2025.08.05-14.03.58:671][  0]LogD3D12RHI: ID3D12Device11 is supported.
[2025.08.05-14.03.58:671][  0]LogD3D12RHI: ID3D12Device12 is supported.
[2025.08.05-14.03.58:671][  0]LogD3D12RHI: Bindless resources are supported
[2025.08.05-14.03.58:671][  0]LogD3D12RHI: Stencil ref from pixel shader is not supported
[2025.08.05-14.03.58:671][  0]LogD3D12RHI: Raster order views are supported
[2025.08.05-14.03.58:671][  0]LogD3D12RHI: Wave Operations are supported (wave size: min=32 max=32).
[2025.08.05-14.03.58:671][  0]LogD3D12RHI: D3D12 ray tracing tier 1.1 and bindless resources are supported.
[2025.08.05-14.03.58:672][  0]LogD3D12RHI: Mesh shader tier 1.0 is supported
[2025.08.05-14.03.58:672][  0]LogD3D12RHI: AtomicInt64OnTypedResource is supported
[2025.08.05-14.03.58:672][  0]LogD3D12RHI: AtomicInt64OnGroupShared is supported
[2025.08.05-14.03.58:672][  0]LogD3D12RHI: AtomicInt64OnDescriptorHeapResource is supported
[2025.08.05-14.03.58:672][  0]LogD3D12RHI: Shader Model 6.6 atomic64 is supported
[2025.08.05-14.03.59:120][  0]LogRHI: Display: Setting GPU Capture Options: 1
[2025.08.05-14.03.59:120][  0]LogRHI: Display: Toggling showmaterialdrawevents: 1
[2025.08.05-14.03.59:120][  0]LogRHI: Display: Enabling full RDG events (r.RDG.Events 3)
[2025.08.05-14.03.59:168][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x0000044C90A3CB00)
[2025.08.05-14.03.59:168][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x0000044C90A3CD80)
[2025.08.05-14.03.59:169][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x0000044C90A3D000)
[2025.08.05-14.03.59:169][  0]LogD3D12RHI: Display: Not using pipeline state disk cache per r.D3D12.PSO.DiskCache=0
[2025.08.05-14.03.59:169][  0]LogD3D12RHI: Display: Not using driver-optimized pipeline state disk cache per r.D3D12.PSO.DriverOptimizedDiskCache=0
[2025.08.05-14.03.59:189][  0]LogD3D12RHI: Warning: Failed to query NVIDIA driver version
[2025.08.05-14.03.59:189][  0]LogRHI: Texture pool is 7083 MB (70% of 10119 MB)
[2025.08.05-14.03.59:189][  0]LogD3D12RHI: Async texture creation enabled
[2025.08.05-14.03.59:189][  0]LogD3D12RHI: RHI has support for 64 bit atomics
[2025.08.05-14.03.59:200][  0]LogVRS: Current RHI supports per-draw and screenspace Variable Rate Shading
[2025.08.05-14.03.59:202][  0]LogInit: Initializing FReadOnlyCVARCache
[2025.08.05-14.03.59:244][  0]LogTurnkeySupport: Running Turnkey SDK detection: ' -ScriptsForProject="F:/TAMO_Streaming/Projects/TAMO/TAMO.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="F:/TAMO_Streaming/Projects/TAMO/Intermediate/TurnkeyReport_0.log" -log="F:/TAMO_Streaming/Projects/TAMO/Intermediate/TurnkeyLog_0.log" -project="F:/TAMO_Streaming/Projects/TAMO/TAMO.uproject"  -platform=all'
[2025.08.05-14.03.59:244][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""F:/TAMO_Streaming/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="F:/TAMO_Streaming/Projects/TAMO/TAMO.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="F:/TAMO_Streaming/Projects/TAMO/Intermediate/TurnkeyReport_0.log" -log="F:/TAMO_Streaming/Projects/TAMO/Intermediate/TurnkeyLog_0.log" -project="F:/TAMO_Streaming/Projects/TAMO/TAMO.uproject"  -platform=all" ]
[2025.08.05-14.03.59:385][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatASTC
[2025.08.05-14.03.59:385][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatDXT
[2025.08.05-14.03.59:385][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatETC2
[2025.08.05-14.03.59:385][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
[2025.08.05-14.03.59:385][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatUncompressed
[2025.08.05-14.03.59:385][  0]LogTextureFormatOodle: Display: Oodle Texture TFO init; latest sdk version = 2.9.12
[2025.08.05-14.03.59:385][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.12.dll
[2025.08.05-14.03.59:393][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
[2025.08.05-14.03.59:401][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatOodle
[2025.08.05-14.03.59:487][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android'
[2025.08.05-14.03.59:487][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTC'
[2025.08.05-14.03.59:487][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXT'
[2025.08.05-14.03.59:487][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2'
[2025.08.05-14.03.59:487][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AndroidClient'
[2025.08.05-14.03.59:487][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTCClient'
[2025.08.05-14.03.59:487][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXTClient'
[2025.08.05-14.03.59:487][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2Client'
[2025.08.05-14.03.59:487][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_Multi'
[2025.08.05-14.03.59:487][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_MultiClient'
[2025.08.05-14.03.59:585][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Linux'
[2025.08.05-14.03.59:585][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxEditor'
[2025.08.05-14.03.59:585][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxServer'
[2025.08.05-14.03.59:585][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxClient'
[2025.08.05-14.03.59:652][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64'
[2025.08.05-14.03.59:652][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Server'
[2025.08.05-14.03.59:652][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Client'
[2025.08.05-14.03.59:788][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Windows'
[2025.08.05-14.03.59:788][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsEditor'
[2025.08.05-14.03.59:788][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsServer'
[2025.08.05-14.03.59:788][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsClient'
[2025.08.05-14.04.00:049][  0]LogTargetPlatformManager: Display: Building Assets For WindowsEditor
[2025.08.05-14.04.00:302][  0]LogRendererCore: Ray tracing is enabled (dynamic). Reason: r.RayTracing=1 and r.RayTracing.EnableOnDemand=1.
[2025.08.05-14.04.00:302][  0]LogRendererCore: Ray tracing shaders are enabled.
[2025.08.05-14.04.00:306][  0]LogDerivedDataCache: Display: Memory: Max Cache Size: -1 MB
[2025.08.05-14.04.00:307][  0]LogDerivedDataCache: FDerivedDataBackendGraph: Pak pak cache file ../../../Projects/TAMO/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.08.05-14.04.00:307][  0]LogDerivedDataCache: Unable to find inner node Pak for hierarchy Root.
[2025.08.05-14.04.00:307][  0]LogDerivedDataCache: FDerivedDataBackendGraph: EnginePak pak cache file ../../../Engine/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.08.05-14.04.00:307][  0]LogDerivedDataCache: Unable to find inner node EnginePak for hierarchy Root.
[2025.08.05-14.04.00:308][  0]LogZenServiceInstance: Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
[2025.08.05-14.04.00:309][  0]LogZenServiceInstance: InTree version at 'F:/TAMO_Streaming/Engine/Binaries/Win64/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.08.05-14.04.00:310][  0]LogZenServiceInstance: Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.6.6-202504241958-windows-x64-release-f090a03'
[2025.08.05-14.04.00:312][  0]LogZenServiceInstance: Found existing instance running on port 8558 matching our settings, no actions needed
[2025.08.05-14.04.00:327][  0]LogZenServiceInstance: Display: Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
[2025.08.05-14.04.00:327][  0]LogZenServiceInstance: Local ZenServer AutoLaunch initialization completed in 0.018 seconds
[2025.08.05-14.04.00:339][  0]LogDerivedDataCache: Display: ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
[2025.08.05-14.04.00:361][  0]LogDerivedDataCache: ../../../Engine/DerivedDataCache: Speed tests took 0.02 seconds.
[2025.08.05-14.04.00:361][  0]LogDerivedDataCache: Display: ../../../Engine/DerivedDataCache: Performance: Latency=0.02ms. RandomReadSpeed=157.62MBs, RandomWriteSpeed=60.00MBs. Assigned SpeedClass 'Local'
[2025.08.05-14.04.00:363][  0]LogDerivedDataCache: Local: Using data cache path ../../../Engine/DerivedDataCache: DeleteOnly
[2025.08.05-14.04.00:363][  0]LogDerivedDataCache: ZenShared: Disabled because Host is set to 'None'
[2025.08.05-14.04.00:363][  0]LogDerivedDataCache: Unable to find inner node ZenShared for hierarchy Root.
[2025.08.05-14.04.00:363][  0]LogDerivedDataCache: Shared: Disabled because no path is configured.
[2025.08.05-14.04.00:363][  0]LogDerivedDataCache: Unable to find inner node Shared for hierarchy Root.
[2025.08.05-14.04.00:363][  0]LogDerivedDataCache: Cloud: Disabled because Host is set to 'None'
[2025.08.05-14.04.00:363][  0]LogDerivedDataCache: Unable to find inner node Cloud for hierarchy Root.
[2025.08.05-14.04.00:364][  0]LogShaderCompilers: Guid format shader working directory is 28 characters bigger than the processId version (../../../Projects/TAMO/Intermediate/Shaders/WorkingDirectory/89548/).
[2025.08.05-14.04.00:364][  0]LogShaderCompilers: Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/E101ACAC4F7E379458853BAD8D7E56BA/'.
[2025.08.05-14.04.00:415][  0]LogXGEController: Display: Initialized XGE controller. XGE tasks will not be spawned on this machine.
[2025.08.05-14.04.00:415][  0]LogShaderCompilers: Display: Using XGE Controller for Shader Compilation.
[2025.08.05-14.04.00:435][  0]LogShaderCompilers: Display: Compiling shader autogen file: ../../../Projects/TAMO/Intermediate/ShaderAutogen/PCD3D_SM6/AutogenShaderHeaders.ush
[2025.08.05-14.04.00:435][  0]LogShaderCompilers: Display: Autogen file is unchanged, skipping write.
[2025.08.05-14.04.03:452][  0]LogSlate: Using FreeType 2.10.0
[2025.08.05-14.04.03:455][  0]LogSlate: SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
[2025.08.05-14.04.03:457][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.08.05-14.04.03:457][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.08.05-14.04.03:494][  0]LogAssetRegistry: FAssetRegistry took 0.0076 seconds to start up
[2025.08.05-14.04.03:497][  0]LogEditorDomain: Display: EditorDomain is Disabled
[2025.08.05-14.04.03:499][  0]LogAssetRegistry: Display: AssetDataGatherer spent 0.000s loading caches ../../../Projects/TAMO/Intermediate/CachedAssetRegistry_*.bin.
[2025.08.05-14.04.03:768][  0]LogStreaming: Display: FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
[2025.08.05-14.04.03:770][  0]LogTextureEncodingSettings: Display: Texture Encode Speed: FinalIfAvailable (editor).
[2025.08.05-14.04.03:770][  0]LogTextureEncodingSettings: Display: Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
[2025.08.05-14.04.03:770][  0]LogTextureEncodingSettings: Display: Shared linear texture encoding: Disabled
[2025.08.05-14.04.03:836][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64Editor not found.
[2025.08.05-14.04.03:836][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64 not found.
[2025.08.05-14.04.03:885][  0]LogDeviceProfileManager: Active device profile: [0000044CAFD9C600][0000044CB0568000 66] WindowsEditor
[2025.08.05-14.04.03:885][  0]LogCsvProfiler: Display: Metadata set : deviceprofile="WindowsEditor"
[2025.08.05-14.04.03:944][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.05-14.04.03:945][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 18 to allow recursive sync load to finish
[2025.08.05-14.04.03:945][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.08.05-14.04.03:945][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.05-14.04.03:945][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 19 to allow recursive sync load to finish
[2025.08.05-14.04.03:945][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.08.05-14.04.03:945][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.05-14.04.03:946][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 20 to allow recursive sync load to finish
[2025.08.05-14.04.03:946][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.08.05-14.04.03:947][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.05-14.04.03:947][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 21 to allow recursive sync load to finish
[2025.08.05-14.04.03:947][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.08.05-14.04.03:947][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.05-14.04.03:948][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.05-14.04.03:948][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 23 to allow recursive sync load to finish
[2025.08.05-14.04.03:948][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.08.05-14.04.03:948][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.05-14.04.03:948][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 24 to allow recursive sync load to finish
[2025.08.05-14.04.03:948][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.08.05-14.04.03:949][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.05-14.04.03:949][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.05-14.04.03:949][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 26 to allow recursive sync load to finish
[2025.08.05-14.04.03:949][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.08.05-14.04.03:949][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.05-14.04.03:949][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 27 to allow recursive sync load to finish
[2025.08.05-14.04.03:950][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.08.05-14.04.03:950][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.05-14.04.03:951][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDiffuse with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.08.05-14.04.03:952][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.05-14.04.03:952][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 29 to allow recursive sync load to finish
[2025.08.05-14.04.03:952][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.08.05-14.04.03:952][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.05-14.04.03:952][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 30 to allow recursive sync load to finish
[2025.08.05-14.04.03:952][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.08.05-14.04.03:952][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.08.05-14.04.03:952][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 28 to allow recursive sync load to finish
[2025.08.05-14.04.03:952][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.08.05-14.04.03:953][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.05-14.04.03:953][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 31 to allow recursive sync load to finish
[2025.08.05-14.04.03:953][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.08.05-14.04.03:953][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 25 to allow recursive sync load to finish
[2025.08.05-14.04.03:953][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.08.05-14.04.03:953][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.05-14.04.03:953][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 32 to allow recursive sync load to finish
[2025.08.05-14.04.03:953][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.08.05-14.04.03:953][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 22 to allow recursive sync load to finish
[2025.08.05-14.04.03:953][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.08.05-14.04.03:953][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.05-14.04.03:953][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 33 to allow recursive sync load to finish
[2025.08.05-14.04.03:953][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.08.05-14.04.04:577][  0]LogMeshReduction: Display: Using QuadricMeshReduction for automatic static mesh reduction
[2025.08.05-14.04.04:577][  0]LogMeshReduction: Display: Using SkeletalMeshReduction for automatic skeletal mesh reduction
[2025.08.05-14.04.04:577][  0]LogMeshReduction: Display: Using ProxyLODMeshReduction for automatic mesh merging
[2025.08.05-14.04.04:577][  0]LogMeshReduction: Display: No distributed automatic mesh merging module available
[2025.08.05-14.04.04:577][  0]LogMeshMerging: No distributed automatic mesh merging module available
[2025.08.05-14.04.05:336][  0]LogConfig: Branch 'PIEPreviewSettings' had been unloaded. Reloading on-demand took 1.68ms
[2025.08.05-14.04.05:386][  0]LogConfig: Branch 'GameplayTagsList' had been unloaded. Reloading on-demand took 1.74ms
[2025.08.05-14.04.05:419][  0]LogConfig: Branch 'TemplateDefs' had been unloaded. Reloading on-demand took 1.65ms
[2025.08.05-14.04.05:424][  0]LogConfig: Branch 'TemplateCategories' had been unloaded. Reloading on-demand took 1.79ms
[2025.08.05-14.04.05:882][  0]LogVirtualization: Display: VirtualizationSystem name found in ini file: None
[2025.08.05-14.04.05:882][  0]LogVirtualization: Display: FNullVirtualizationSystem mounted, virtualization will be disabled
[2025.08.05-14.04.05:910][  0]LogLiveCoding: Display: Starting LiveCoding
[2025.08.05-14.04.05:910][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: UnrealEditor Win64 Development
[2025.08.05-14.04.05:912][  0]LogLiveCoding: Display: First instance in process group "UE_TAMO_0xb13c2076", spawning console
[2025.08.05-14.04.06:070][  0]LogSlate: Border
[2025.08.05-14.04.06:070][  0]LogSlate: BreadcrumbButton
[2025.08.05-14.04.06:070][  0]LogSlate: Brushes.Title
[2025.08.05-14.04.06:070][  0]LogSlate: Default
[2025.08.05-14.04.06:070][  0]LogSlate: Icons.Save
[2025.08.05-14.04.06:070][  0]LogSlate: Icons.Toolbar.Settings
[2025.08.05-14.04.06:070][  0]LogSlate: ListView
[2025.08.05-14.04.06:070][  0]LogSlate: SoftwareCursor_CardinalCross
[2025.08.05-14.04.06:070][  0]LogSlate: SoftwareCursor_Grab
[2025.08.05-14.04.06:070][  0]LogSlate: TableView.DarkRow
[2025.08.05-14.04.06:070][  0]LogSlate: TableView.Row
[2025.08.05-14.04.06:070][  0]LogSlate: TreeView
[2025.08.05-14.04.06:570][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize started...
[2025.08.05-14.04.06:573][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize took 3.055 ms
[2025.08.05-14.04.06:626][  0]LogConfig: Branch 'Mass' had been unloaded. Reloading on-demand took 1.73ms
[2025.08.05-14.04.06:770][  0]LogInit: XR: Instanced Stereo Rendering is Disabled
[2025.08.05-14.04.06:770][  0]LogInit: XR: MultiViewport is Disabled
[2025.08.05-14.04.06:770][  0]LogInit: XR: Mobile Multiview is Disabled
[2025.08.05-14.04.06:771][  0]LogLiveCoding: Display: Waiting for server
[2025.08.05-14.04.06:988][  0]LogTcpMessaging: Initializing TcpMessaging bridge
[2025.08.05-14.04.07:015][  0]LogUdpMessaging: Display: Work queue size set to 1024.
[2025.08.05-14.04.07:015][  0]LogUdpMessaging: Initializing bridge on interface 0.0.0.0:0 to multicast group 230.0.0.1:6666.
[2025.08.05-14.04.07:015][  0]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:55362'.
[2025.08.05-14.04.07:022][  0]LogUdpMessaging: Display: Added local interface '10.72.90.68' to multicast group '230.0.0.1:6666'
[2025.08.05-14.04.07:749][  0]LogMetaSound: Display: MetaSound Page Target Initialized to 'Default'
[2025.08.05-14.04.07:749][  0]LogAudio: Display: Registering Engine Module Parameter Interfaces...
[2025.08.05-14.04.07:788][  0]LogMetaSound: MetaSound Engine Initialized
[2025.08.05-14.04.09:339][  0]LogConfig: Branch 'TranslationPickerSettings' had been unloaded. Reloading on-demand took 1.79ms
[2025.08.05-14.04.09:849][  0]LogNiagaraDebuggerClient: Niagara Debugger Client Initialized | Session: C819A408F0F4475D8000000000004700 | Instance: 1A0651E94FC3B80F71C3618DDFAD9AA9 (CNCDUW1174-89548).
[2025.08.05-14.04.12:344][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.08.05-14.04.12:344][  0]LogNNERuntimeORT: 0: NVIDIA GeForce RTX 4070 (Compute, Graphics)
[2025.08.05-14.04.12:344][  0]LogNNERuntimeORT: 1: Microsoft Basic Render Driver (Compute, Graphics)
[2025.08.05-14.04.12:344][  0]LogNNERuntimeORT: No NPU adapter found!
[2025.08.05-14.04.13:125][  0]LogConfig: Applying CVar settings from Section [/Script/PCG.PCGEngineSettings] File [Engine]
[2025.08.05-14.04.13:944][  0]LogAndroidPermission: UAndroidPermissionCallbackProxy::GetInstance
[2025.08.05-14.04.14:115][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.08.05-14.04.14:116][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.08.05-14.04.14:945][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdGp/resources/plugInfo.json'
[2025.08.05-14.04.14:948][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShaders/resources/plugInfo.json'
[2025.08.05-14.04.14:950][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hd/resources/plugInfo.json'
[2025.08.05-14.04.14:953][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/glf/resources/plugInfo.json'
[2025.08.05-14.04.14:956][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdHydra/resources/plugInfo.json'
[2025.08.05-14.04.14:958][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShade/resources/plugInfo.json'
[2025.08.05-14.04.14:961][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdUI/resources/plugInfo.json'
[2025.08.05-14.04.14:964][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdSt/resources/plugInfo.json'
[2025.08.05-14.04.14:966][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdStorm/resources/plugInfo.json'
[2025.08.05-14.04.14:969][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImagingGL/resources/plugInfo.json'
[2025.08.05-14.04.14:972][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMtlx/resources/plugInfo.json'
[2025.08.05-14.04.14:974][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkel/resources/plugInfo.json'
[2025.08.05-14.04.14:978][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdGeom/resources/plugInfo.json'
[2025.08.05-14.04.14:981][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hgiGL/resources/plugInfo.json'
[2025.08.05-14.04.14:983][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdx/resources/plugInfo.json'
[2025.08.05-14.04.14:986][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProcImaging/resources/plugInfo.json'
[2025.08.05-14.04.14:988][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRi/resources/plugInfo.json'
[2025.08.05-14.04.14:992][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hio/resources/plugInfo.json'
[2025.08.05-14.04.14:995][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProc/resources/plugInfo.json'
[2025.08.05-14.04.14:997][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ndr/resources/plugInfo.json'
[2025.08.05-14.04.15:001][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdLux/resources/plugInfo.json'
[2025.08.05-14.04.15:003][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdrGlslfx/resources/plugInfo.json'
[2025.08.05-14.04.15:006][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRender/resources/plugInfo.json'
[2025.08.05-14.04.15:010][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVol/resources/plugInfo.json'
[2025.08.05-14.04.15:014][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRiPxrImaging/resources/plugInfo.json'
[2025.08.05-14.04.15:016][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/plugInfo.json'
[2025.08.05-14.04.15:020][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImaging/resources/plugInfo.json'
[2025.08.05-14.04.15:022][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdAbc/resources/plugInfo.json'
[2025.08.05-14.04.15:025][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkelImaging/resources/plugInfo.json'
[2025.08.05-14.04.15:028][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMedia/resources/plugInfo.json'
[2025.08.05-14.04.15:031][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ar/resources/plugInfo.json'
[2025.08.05-14.04.15:033][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdf/resources/plugInfo.json'
[2025.08.05-14.04.15:037][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdPhysics/resources/plugInfo.json'
[2025.08.05-14.04.15:040][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVolImaging/resources/plugInfo.json'
[2025.08.05-14.04.15:042][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/codegenTemplates/plugInfo.json'
[2025.08.05-14.04.16:131][  0]SourceControl: Revision control is disabled
[2025.08.05-14.04.16:252][  0]SourceControl: Revision control is disabled
[2025.08.05-14.04.16:615][  0]LogConfig: Branch 'ObjectMixerSerializedData' had been unloaded. Reloading on-demand took 2.03ms
[2025.08.05-14.04.16:688][  0]LogConfig: Branch 'Crypto' had been unloaded. Reloading on-demand took 1.52ms
[2025.08.05-14.04.17:137][  0]LogLiveCoding: Display: Successfully initialized, removing startup thread
[2025.08.05-14.04.19:114][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.08.05-14.04.19:114][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.08.05-14.04.19:516][  0]LogConfig: Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
[2025.08.05-14.04.19:904][  0]LogCollectionManager: Loaded 0 collections in 0.001778 seconds
[2025.08.05-14.04.19:908][  0]LogFileCache: Scanning file cache for directory 'F:/TAMO_Streaming/Projects/TAMO/Saved/Collections/' took 0.00s
[2025.08.05-14.04.19:914][  0]LogFileCache: Scanning file cache for directory 'F:/TAMO_Streaming/Projects/TAMO/Content/Developers/zengyanjia/Collections/' took 0.01s
[2025.08.05-14.04.19:919][  0]LogFileCache: Scanning file cache for directory 'F:/TAMO_Streaming/Projects/TAMO/Content/Collections/' took 0.01s
[2025.08.05-14.04.20:461][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.08.05-14.04.20:461][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.08.05-14.04.20:572][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.08.05-14.04.20:572][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.08.05-14.04.20:618][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Version 1.16.4-36651368 booting at 2025-08-05T14:04:20.618Z using C
[2025.08.05-14.04.20:619][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Platform Properties [OS=Windows/10.0.19041.5915.64bit, ClientId=xyza7891REBVsEqSJRRNXmlS7EQHM459, ProductId=86f32f1151354e7cb39c12f8ab2c22a3, SandboxId=********************************, DeploymentId=a652a72ea1664dcab3a467891eea5f30, ProductName=TAMO, ProductVersion=UE5-CL-0, IsServer=false, Flags=DisableOverlay]
[2025.08.05-14.04.20:620][  0]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.08.05-14.04.20:620][  0]LogEOSSDK: LogEOSOverlay: Overlay will not load, because it was explicitly disabled when creating the platform
[2025.08.05-14.04.20:628][  0]LogEOSSDK: LogEOSAntiCheat: [AntiCheatClient] Anti-cheat client not available. Verify that the game was started using the anti-cheat bootstrapper if you intend to use it.
[2025.08.05-14.04.20:629][  0]LogEOSSDK: LogEOS: SetApplicationStatus - OldStatus: EOS_AS_Foreground, NewStatus: EOS_AS_Foreground, Current Time: 0001.01.01-00.00.00
[2025.08.05-14.04.20:629][  0]LogEOSSDK: LogEOS: SetNetworkStatus - OldStatus: EOS_NS_Online, NewStatus: EOS_NS_Online
[2025.08.05-14.04.20:629][  0]LogEOSSDK: LogEOS: Updating Platform SDK Config, Time: 0.000154
[2025.08.05-14.04.20:629][  0]LogFab: Display: Logging in using persist
[2025.08.05-14.04.20:630][  0]LogEOSSDK: Warning: LogEOSAuth: No existing persistent auth credentials were found for automatic login.
[2025.08.05-14.04.20:724][  0]LogUObjectArray: 46531 objects as part of root set at end of initial load.
[2025.08.05-14.04.20:724][  0]LogUObjectArray: CloseDisregardForGC: 0/0 objects in disregard for GC pool
[2025.08.05-14.04.20:755][  0]LogStreaming: Display: AsyncLoading2 - NotifyRegistrationComplete: Registered 38645 public script object entries (1039.31 KB)
[2025.08.05-14.04.20:755][  0]LogStreaming: Display: AsyncLoading2 - Thread Started: false, IsInitialLoad: false
[2025.08.05-14.04.20:936][  0]LogClass: Error: BoolProperty FFabAssetMetadata::IsQuixel is not initialized properly. Module:Fab File:Private/FabBrowserApi.h
[2025.08.05-14.04.20:936][  0]LogClass: Display: 1 Uninitialized script struct members found including 0 object properties
[2025.08.05-14.04.21:018][  0]LogAutomationTest: Error: LogClass: BoolProperty FFabAssetMetadata::IsQuixel is not initialized properly. Module:Fab File:Private/FabBrowserApi.h
[2025.08.05-14.04.21:019][  0]LogEngine: Initializing Engine...
[2025.08.05-14.04.21:025][  0]LogStylusInput: Initializing StylusInput subsystem.
[2025.08.05-14.04.21:025][  0]LogStats: UGameplayTagsManager::InitializeManager -  0.000 s
[2025.08.05-14.04.21:269][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.11.dll
[2025.08.05-14.04.21:338][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled
[2025.08.05-14.04.21:393][  0]LogNetVersion: Set ProjectVersion to *******. Version Checksum will be recalculated on next use.
[2025.08.05-14.04.21:393][  0]LogInit: Texture streaming: Enabled
[2025.08.05-14.04.21:508][  0]LogAnalytics: Display: [UEEditor.Perforce.Release] APIServer = https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data. AppVersion = 5.5.1-0+UE5
[2025.08.05-14.04.21:551][  0]LogAudio: Display: Initializing Audio Device Manager...
[2025.08.05-14.04.21:618][  0]LogAudio: Display: Loading Default Audio Settings Objects...
[2025.08.05-14.04.21:619][  0]LogAudio: Display: No default SoundConcurrencyObject specified (or failed to load).
[2025.08.05-14.04.21:621][  0]LogAudio: Display: Audio Device Manager Initialized
[2025.08.05-14.04.21:621][  0]LogAudio: Display: Creating Audio Device:                 Id: 1, Scope: Shared, Realtime: True
[2025.08.05-14.04.21:621][  0]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.08.05-14.04.21:621][  0]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.08.05-14.04.21:621][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.08.05-14.04.21:621][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.08.05-14.04.21:621][  0]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.08.05-14.04.21:621][  0]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.08.05-14.04.21:621][  0]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.08.05-14.04.21:621][  0]LogAudio: Display: AudioDevice MaxSources: 32
[2025.08.05-14.04.21:621][  0]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.08.05-14.04.21:621][  0]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.08.05-14.04.21:621][  0]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.08.05-14.04.21:692][  0]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.08.05-14.04.22:065][  0]LogAudioMixer: Display: Using Audio Hardware Device Speakers/Headphones (Realtek(R) Audio)
[2025.08.05-14.04.22:066][  0]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.08.05-14.04.22:071][  0]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.08.05-14.04.22:071][  0]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.08.05-14.04.22:072][  0]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.08.05-14.04.22:072][  0]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.08.05-14.04.22:076][  0]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.08.05-14.04.22:076][  0]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=1
[2025.08.05-14.04.22:076][  0]LogInit: FAudioDevice initialized with ID 1.
[2025.08.05-14.04.22:076][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled'.
[2025.08.05-14.04.22:077][  0]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 1
[2025.08.05-14.04.22:123][  0]LogCsvProfiler: Display: Metadata set : largeworldcoordinates="1"
[2025.08.05-14.04.22:130][  0]LogInit: Undo buffer set to 256 MB
[2025.08.05-14.04.22:131][  0]LogInit: Transaction tracking system initialized
[2025.08.05-14.04.22:219][  0]LogSourceControl: Display: Uncontrolled Changelist persistency file loaded ../../../Projects/TAMO/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.05-14.04.22:794][  0]LogConfig: Branch 'LocalizationServiceSettings' had been unloaded. Reloading on-demand took 1.94ms
[2025.08.05-14.04.22:801][  0]LocalizationService: Localization service is disabled
[2025.08.05-14.04.22:893][  0]LogTimingProfiler: Initialize
[2025.08.05-14.04.22:893][  0]LogTimingProfiler: OnSessionChanged
[2025.08.05-14.04.22:893][  0]LoadingProfiler: Initialize
[2025.08.05-14.04.22:893][  0]LoadingProfiler: OnSessionChanged
[2025.08.05-14.04.22:893][  0]LogNetworkingProfiler: Initialize
[2025.08.05-14.04.22:893][  0]LogNetworkingProfiler: OnSessionChanged
[2025.08.05-14.04.22:893][  0]LogMemoryProfiler: Initialize
[2025.08.05-14.04.22:894][  0]LogMemoryProfiler: OnSessionChanged
[2025.08.05-14.04.23:757][  0]LogFileCache: Scanning file cache for directory 'F:/TAMO_Streaming/Projects/TAMO/Content/' took 0.01s
[2025.08.05-14.04.23:955][  0]LogPython: Using Python 3.11.8
[2025.08.05-14.04.25:908][  0]LogPython: Display: No enabled plugins with python dependencies found, skipping
[2025.08.05-14.04.25:940][  0]LogRenderer: Requested compilation of Path Tracing RTPSOs (1 permutations).
[2025.08.05-14.04.26:517][  0]LogStreaming: Warning: Failed to read file 'Common/Selector.png' error.
[2025.08.05-14.04.26:517][  0]LogSlate: Could not find file for Slate resource: Common/Selector.png
[2025.08.05-14.04.26:975][  0]LogLevelSequenceEditor: LevelSequenceEditor subsystem initialized.
[2025.08.05-14.04.27:067][  0]LogEditorDataStorage: Initializing
[2025.08.05-14.04.27:068][  0]LogEditorDataStorage: Initialized
[2025.08.05-14.04.27:071][  0]LogAssetRegistry: Display: Triggering cache save on discovery complete
[2025.08.05-14.04.27:136][  0]LogInit: Display: Engine is initialized. Leaving FEngineLoop::Init()
[2025.08.05-14.04.27:139][  0]SourceControl: Revision control is disabled
[2025.08.05-14.04.27:139][  0]LogUnrealEdMisc: Loading editor; pre map load, took 34.560
[2025.08.05-14.04.27:140][  0]Cmd: MAP LOAD FILE="../../../Engine/Content/Maps/Templates/OpenWorld.umap" TEMPLATE=1 SHOWPROGRESS=1 FEATURELEVEL=4
[2025.08.05-14.04.27:144][  0]LogWorld: UWorld::CleanupWorld for Untitled, bSessionEnded=true, bCleanupResources=true
[2025.08.05-14.04.27:144][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.08.05-14.04.27:164][  0]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.08.05-14.04.27:166][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.87ms
[2025.08.05-14.04.27:186][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled_1'.
[2025.08.05-14.04.27:186][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled_1
[2025.08.05-14.04.27:189][  0]LogWorldPartition: ULevel::OnLevelLoaded(Untitled_1)(bIsOwningWorldGameWorld=0, bIsOwningWorldPartitioned=1, InitializeForMainWorld=1, InitializeForEditor=1, InitializeForGame=0)
[2025.08.05-14.04.27:189][  0]LogWorldPartition: Display: WorldPartition initialize started...
[2025.08.05-14.04.27:189][  0]LogWorldPartition: UWorldPartition::Initialize : World = /Temp/Untitled_1.Untitled_1, World Type = Editor, IsMainWorldPartition = 1, Location = V(0), Rotation = R(0), IsEditor = 1, IsGame = 0, IsPIEWorldTravel = 0, IsCooking = 0
[2025.08.05-14.04.27:255][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.05-14.04.27:294][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.05-14.04.27:297][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.05-14.04.27:299][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [AppleTV]
[2025.08.05-14.04.27:299][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [IOS]
[2025.08.05-14.04.27:299][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.05-14.04.27:305][  0]LogDeviceProfileManager: Display: Deviceprofile None not found.
[2025.08.05-14.04.27:730][  0]LogWorldPartition: Display: WorldPartition initialize took 541.030 ms
[2025.08.05-14.04.28:213][  0]LogAssetRegistry: Display: Asset registry cache written as 61.7 MiB to ../../../Projects/TAMO/Intermediate/CachedAssetRegistry_*.bin
[2025.08.05-14.04.28:656][  0]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.08.05-14.04.28:679][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.88ms
[2025.08.05-14.04.28:680][  0]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.08.05-14.04.28:682][  0]MapCheck: Map check complete: 0 Error(s), 0 Warning(s), took 2.099ms to complete.
[2025.08.05-14.04.28:698][  0]LogUnrealEdMisc: Total Editor Startup Time, took 36.119
[2025.08.05-14.04.28:914][  0]LogActorFactory: Loading ActorFactory Class /Script/Engine.LevelInstance
[2025.08.05-14.04.28:963][  0]LogSlate: The tab "TopLeftModeTab" attempted to spawn in layout 'LevelEditor_Layout_v1.8' but failed for some reason. It will not be displayed.
[2025.08.05-14.04.28:979][  0]RenderDocPlugin: Attaching toolbar extension...
[2025.08.05-14.04.29:047][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.08.05-14.04.29:065][  0]RenderDocPlugin: Attaching toolbar extension...
[2025.08.05-14.04.29:119][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.08.05-14.04.29:130][  0]RenderDocPlugin: Attaching toolbar extension...
[2025.08.05-14.04.29:177][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.08.05-14.04.29:191][  0]RenderDocPlugin: Attaching toolbar extension...
[2025.08.05-14.04.29:246][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.08.05-14.04.29:306][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.05-14.04.29:307][  0]LogPakFile: Display: Mounted Pak file 'F:/TAMO_Streaming/FeaturePacks/StarterContent.upack', mount point: 'root:/'
[2025.08.05-14.04.29:307][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.05-14.04.29:307][  0]LogPakFile: Display: Mounted Pak file 'F:/TAMO_Streaming/FeaturePacks/TP_FirstPerson.upack', mount point: 'root:/'
[2025.08.05-14.04.29:308][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.05-14.04.29:308][  0]LogPakFile: Display: Mounted Pak file 'F:/TAMO_Streaming/FeaturePacks/TP_FirstPersonBP.upack', mount point: 'root:/'
[2025.08.05-14.04.29:308][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.05-14.04.29:309][  0]LogPakFile: Display: Mounted Pak file 'F:/TAMO_Streaming/FeaturePacks/TP_Puzzle.upack', mount point: 'root:/'
[2025.08.05-14.04.29:309][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.05-14.04.29:310][  0]LogPakFile: Display: Mounted Pak file 'F:/TAMO_Streaming/FeaturePacks/TP_PuzzleBP.upack', mount point: 'root:/'
[2025.08.05-14.04.29:310][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.05-14.04.29:310][  0]LogPakFile: Display: Mounted Pak file 'F:/TAMO_Streaming/FeaturePacks/TP_ThirdPerson.upack', mount point: 'root:/'
[2025.08.05-14.04.29:311][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.05-14.04.29:311][  0]LogPakFile: Display: Mounted Pak file 'F:/TAMO_Streaming/FeaturePacks/TP_ThirdPersonBP.upack', mount point: 'root:/'
[2025.08.05-14.04.29:311][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.05-14.04.29:312][  0]LogPakFile: Display: Mounted Pak file 'F:/TAMO_Streaming/FeaturePacks/TP_TopDown.upack', mount point: 'root:/'
[2025.08.05-14.04.29:312][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.05-14.04.29:313][  0]LogPakFile: Display: Mounted Pak file 'F:/TAMO_Streaming/FeaturePacks/TP_TopDownBP.upack', mount point: 'root:/'
[2025.08.05-14.04.29:313][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.05-14.04.29:313][  0]LogPakFile: Display: Mounted Pak file 'F:/TAMO_Streaming/FeaturePacks/TP_VehicleAdv.upack', mount point: 'root:/'
[2025.08.05-14.04.29:313][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.05-14.04.29:314][  0]LogPakFile: Display: Mounted Pak file 'F:/TAMO_Streaming/FeaturePacks/TP_VehicleAdvBP.upack', mount point: 'root:/'
[2025.08.05-14.04.29:314][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.05-14.04.29:315][  0]LogPakFile: Display: Mounted Pak file 'F:/TAMO_Streaming/FeaturePacks/TP_VirtualRealityBP.upack', mount point: 'root:/'
[2025.08.05-14.04.29:500][  0]LogSlate: Took 0.000592 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansMono.ttf' (77K)
[2025.08.05-14.04.29:531][  0]LogSlate: Took 0.000547 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.08.05-14.04.29:533][  0]LogSlate: Took 0.002180 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansFallback.ttf' (3848K)
[2025.08.05-14.04.29:636][  0]LogSlate: The tab "AvalancheOutliner1" attempted to spawn in layout 'LevelEditor_Layout_v1.8' but failed for some reason. It will not be displayed.
[2025.08.05-14.04.29:680][  0]LogSceneOutliner: Outliner Column Data Layer Visibility does not have a localizable name, please specify one to FSceneOutlinerColumnInfo
[2025.08.05-14.04.29:680][  0]LogSceneOutliner: Outliner Column Data Layer Loaded In Editor does not have a localizable name, please specify one to FSceneOutlinerColumnInfo
[2025.08.05-14.04.29:680][  0]LogSceneOutliner: Outliner Column Item Label does not have a localizable name, please specify one to FSceneOutlinerColumnInfo
[2025.08.05-14.04.29:680][  0]LogSceneOutliner: Outliner Column Remove Actor does not have a localizable name, please specify one to FSceneOutlinerColumnInfo
[2025.08.05-14.04.29:681][  0]LogSceneOutliner: Outliner Column Initial State does not have a localizable name, please specify one to FSceneOutlinerColumnInfo
[2025.08.05-14.04.29:681][  0]LogSceneOutliner: Outliner Column ID Name does not have a localizable name, please specify one to FSceneOutlinerColumnInfo
[2025.08.05-14.04.29:682][  0]LogSceneOutliner: Outliner Column Debug Color does not have a localizable name, please specify one to FSceneOutlinerColumnInfo
[2025.08.05-14.04.29:683][  0]LogSceneOutliner: Outliner Column Data Layer Has Errors does not have a localizable name, please specify one to FSceneOutlinerColumnInfo
[2025.08.05-14.04.29:929][  0]LogSlate: Took 0.000804 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Bold.ttf' (160K)
[2025.08.05-14.04.29:934][  0]LogSlate: Took 0.000819 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Italic.ttf' (157K)
[2025.08.05-14.04.30:047][  0]LogNNEDenoiser: ApplySettings: bDenoiserEnabled 1
[2025.08.05-14.04.30:054][  0]LogNNEDenoiser: Loaded input mapping from NNEDIM_ColorAlbedoNormal_Alpha
[2025.08.05-14.04.30:055][  0]LogNNEDenoiser: Loaded output mapping from NNEDOM_Output_Alpha
[2025.08.05-14.04.30:055][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.08.05-14.04.30:057][  0]LogNNERuntimeORT: Error: Failed to add DirectML execution provider to OnnxRuntime session options: D:\a\_work\1\s\onnxruntime\core\providers\dml\dml_provider_factory.cc(71)\onnxruntime.dll!0000024CA34B1B44: (caller: 0000024CA34B37FF) Exception(1) tid(17308) 80004002 No such interface supported

[2025.08.05-14.04.30:058][  0]LogNNERuntimeORT: Error: Failed to configure session options for DirectML Execution Provider.
[2025.08.05-14.04.30:058][  0]LogNNEDenoiser: Could not create model instance using NNERuntimeORTDml on RDG
[2025.08.05-14.04.30:058][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeRDGHlsl on RDG...
[2025.08.05-14.04.30:058][  0]LogNNEDenoiser: Could not create model instance. No RDG runtime 'NNERuntimeRDGHlsl' found. Valid RDG runtimes are: 
[2025.08.05-14.04.30:058][  0]LogNNEDenoiser: - NNERuntimeORTDml
[2025.08.05-14.04.30:058][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on GPU...
[2025.08.05-14.04.30:058][  0]LogNNERuntimeORT: Error: Failed to add DirectML execution provider to OnnxRuntime session options: D:\a\_work\1\s\onnxruntime\core\providers\dml\dml_provider_factory.cc(71)\onnxruntime.dll!0000024CA34B1B44: (caller: 0000024CA34B37FF) Exception(2) tid(17308) 80004002 No such interface supported

[2025.08.05-14.04.30:058][  0]LogNNERuntimeORT: Error: InitializedAndConfigureMembers failed.
[2025.08.05-14.04.30:058][  0]LogNNEDenoiser: Could not create model instance using NNERuntimeORTDml on GPU
[2025.08.05-14.04.30:058][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTCpu on CPU...
[2025.08.05-14.04.30:118][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTCpu on CPU
[2025.08.05-14.04.30:118][  0]LogNNEDenoiser: Create denoiser from asset /NNEDenoiser/NNED_Oidn2-3_Balanced_Alpha.NNED_Oidn2-3_Balanced_Alpha...
[2025.08.05-14.04.30:120][  0]LogNNEDenoiser: Loaded input mapping from NNEDTIM_ColorAlbedoNormal_Alpha
[2025.08.05-14.04.30:121][  0]LogNNEDenoiser: Loaded output mapping from NNEDTOM_Output_Alpha
[2025.08.05-14.04.30:121][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.08.05-14.04.30:122][  0]LogNNERuntimeORT: Error: Failed to add DirectML execution provider to OnnxRuntime session options: D:\a\_work\1\s\onnxruntime\core\providers\dml\dml_provider_factory.cc(71)\onnxruntime.dll!0000024CA34B1B44: (caller: 0000024CA34B37FF) Exception(3) tid(17308) 80004002 No such interface supported

[2025.08.05-14.04.30:122][  0]LogNNERuntimeORT: Error: Failed to configure session options for DirectML Execution Provider.
[2025.08.05-14.04.30:122][  0]LogNNEDenoiser: Could not create model instance using NNERuntimeORTDml on RDG
[2025.08.05-14.04.30:122][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeRDGHlsl on RDG...
[2025.08.05-14.04.30:122][  0]LogNNEDenoiser: Could not create model instance. No RDG runtime 'NNERuntimeRDGHlsl' found. Valid RDG runtimes are: 
[2025.08.05-14.04.30:122][  0]LogNNEDenoiser: - NNERuntimeORTDml
[2025.08.05-14.04.30:122][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on GPU...
[2025.08.05-14.04.30:123][  0]LogNNERuntimeORT: Error: Failed to add DirectML execution provider to OnnxRuntime session options: D:\a\_work\1\s\onnxruntime\core\providers\dml\dml_provider_factory.cc(71)\onnxruntime.dll!0000024CA34B1B44: (caller: 0000024CA34B37FF) Exception(4) tid(17308) 80004002 No such interface supported

[2025.08.05-14.04.30:123][  0]LogNNERuntimeORT: Error: InitializedAndConfigureMembers failed.
[2025.08.05-14.04.30:123][  0]LogNNEDenoiser: Could not create model instance using NNERuntimeORTDml on GPU
[2025.08.05-14.04.30:123][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTCpu on CPU...
[2025.08.05-14.04.30:177][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTCpu on CPU
[2025.08.05-14.04.30:177][  0]LogNNEDenoiser: Create temporal denoiser from asset /NNEDenoiser/NNEDT_Oidn2-3_Balanced_Alpha.NNEDT_Oidn2-3_Balanced_Alpha...
[2025.08.05-14.04.30:237][  0]LogSlate: Took 0.000821 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.08.05-14.04.30:444][  0]LogD3D12RHI: Creating RTPSO with 21 shaders (0 cached, 21 new) took 161.84 ms. Compile time 94.83 ms, link time 66.84 ms.
[2025.08.05-14.04.30:854][  0]LogStall: Startup...
[2025.08.05-14.04.30:859][  0]LogStall: Startup complete.
[2025.08.05-14.04.30:916][  0]LogLoad: (Engine Initialization) Total time: 38.34 seconds
[2025.08.05-14.04.30:931][  0]RenderDocPlugin: Creating dummy input device (for intercepting engine ticks)
[2025.08.05-14.04.31:821][  0]LogContentStreaming: Texture pool size now 1000 MB
[2025.08.05-14.04.31:821][  0]LogCsvProfiler: Display: Metadata set : streamingpoolsizemb="1000"
[2025.08.05-14.04.31:942][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.08.05-14.04.31:947][  0]LogFab: Error: Login failed - error code: EOS_InvalidAuth
[2025.08.05-14.04.31:947][  0]LogFab: Display: Logging in using exchange code
[2025.08.05-14.04.31:947][  0]LogFab: Display: Reading exchange code from commandline
[2025.08.05-14.04.31:947][  0]LogEOSSDK: Error: LogEOSAuth: Invalid parameter EOS_Auth_Credentials.Token reason: must not be null or empty
[2025.08.05-14.04.31:948][  0]LogPython: Display: Running start-up script F:/TAMO_Streaming/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... started...
[2025.08.05-14.04.32:046][  0]LogPython: registering <class 'ControlRigWorkflows.workflow_deformation_rig_preset.provider'>

[2025.08.05-14.04.32:053][  0]LogPython: Display: Running start-up script F:/TAMO_Streaming/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... took 105.312 ms
[2025.08.05-14.04.32:146][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: TAMOEditor Win64 Development
[2025.08.05-14.04.32:380][  1]LogAssetRegistry: AssetRegistryGather time 0.1532s: AssetDataDiscovery 0.0341s, AssetDataGather 0.0212s, StoreResults 0.0979s. Wall time 28.8930s.
	NumCachedDirectories 0. NumUncachedDirectories 1779. NumCachedFiles 8430. NumUncachedFiles 0.
	BackgroundTickInterruptions 0.
[2025.08.05-14.04.32:417][  1]LogSourceControl: Uncontrolled asset enumeration started...
[2025.08.05-14.04.32:417][  1]LogCollectionManager: Fixed up redirectors for 0 collections in 0.000001 seconds (updated 0 objects)
[2025.08.05-14.04.32:491][  1]LogFab: Error: Login failed - error code: EOS_InvalidParameters
[2025.08.05-14.04.32:714][  4]LogSourceControl: Uncontrolled asset enumeration finished in 0.297127 seconds (Found 74 uncontrolled assets)
[2025.08.05-14.04.40:877][313]LogChaosDD: Creating Chaos Debug Draw Scene for world World_0
[2025.08.05-14.04.41:017][314]LogActorFactory: Actor Factory attempting to spawn StaticMesh /Engine/BasicShapes/Cube.Cube
[2025.08.05-14.04.41:017][314]LogActorFactory: Actor Factory attempting to spawn StaticMesh /Engine/BasicShapes/Cube.Cube
[2025.08.05-14.04.41:018][314]LogActorFactory: Actor Factory spawned StaticMesh /Engine/BasicShapes/Cube.Cube as actor: StaticMeshActor /Temp/Untitled_1.Untitled_1:PersistentLevel.StaticMeshActor_0
[2025.08.05-14.04.41:019][314]LogActorFactory: Actor Factory spawned StaticMesh /Engine/BasicShapes/Cube.Cube as actor: StaticMeshActor /Temp/Untitled_1.Untitled_1:PersistentLevel.StaticMeshActor_0
[2025.08.05-14.04.41:500][321]LogUObjectHash: Compacting FUObjectHashTables data took   1.14ms
[2025.08.05-14.04.41:503][321]LogEditorActor: Deleted 0 Actors (0.139 secs)
[2025.08.05-14.04.41:513][321]LogActorFactory: Actor Factory attempting to spawn StaticMesh /Engine/BasicShapes/Cube.Cube
[2025.08.05-14.04.41:513][321]LogActorFactory: Actor Factory attempting to spawn StaticMesh /Engine/BasicShapes/Cube.Cube
[2025.08.05-14.04.41:518][321]LogActorFactory: Actor Factory spawned StaticMesh /Engine/BasicShapes/Cube.Cube as actor: StaticMeshActor /Temp/Untitled_1.Untitled_1:PersistentLevel.StaticMeshActor_UAID_B07B250C4A98728202_1732429839
[2025.08.05-14.04.41:519][321]LogActorFactory: Actor Factory spawned StaticMesh /Engine/BasicShapes/Cube.Cube as actor: StaticMeshActor /Temp/Untitled_1.Untitled_1:PersistentLevel.StaticMeshActor_UAID_B07B250C4A98728202_1732429839
[2025.08.05-14.04.41:920][324]LogChaosDD: Creating Chaos Debug Draw Scene for world World_1
[2025.08.05-14.04.44:511][430]LogSlateStyle: Warning: Missing Resource from 'CoreStyle' Style: 'Unable to find Brush 'EditableTextBox.Background'.'
[2025.08.05-14.04.52:893][702]LogStreaming: Display: FlushAsyncLoading(490): 1 QueuedPackages, 0 AsyncPackages
[2025.08.05-14.04.52:998][704]LogActorFactory: Loading ActorFactory Class /Script/Engine.LevelInstance
[2025.08.05-14.04.56:732][880]LogTemp: Warning: Highlighted CPD Parameter: TestCPD [0]
[2025.08.05-14.04.57:198][906]LogEOSSDK: LogEOS: SDK Config Platform Update Request Successful, Time: 36.550087
[2025.08.05-14.04.57:200][906]LogEOSSDK: LogEOSAnalytics: EOS SDK Analytics disabled for route [1].
[2025.08.05-14.04.57:202][906]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 36.569817
[2025.08.05-14.04.57:787][936]LogTemp: Warning: Highlighted CPD Parameter: testCPD_2 [1]
[2025.08.05-14.04.58:816][995]LogTemp: Warning: Highlighted CPD Parameter: TestCPD [0]
[2025.08.05-14.04.59:656][ 42]LogTemp: Warning: Highlighted CPD Parameter: TestCPD [0]
[2025.08.05-14.05.00:321][ 79]LogTemp: Warning: Highlighted CPD Parameter: TestCPD [0]
[2025.08.05-14.05.01:064][120]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.08.05-14.05.01:269][130]LogTemp: Warning: Highlighted CPD Parameter: TestCPD [0]
[2025.08.05-14.05.01:737][153]LogTemp: Warning: Highlighted CPD Parameter: TestCPD [0]
[2025.08.05-14.05.07:543][450]LogEOSSDK: LogEOS: SDK Config Product Update Request Successful, Time: 46.896259
[2025.08.05-14.05.07:546][450]LogEOSSDK: LogEOS: SDK Config Data - Watermark: 1327463752
[2025.08.05-14.05.07:546][450]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 46.896259, Update Interval: 333.509338
[2025.08.05-14.05.24:595][321]LogTurnkeySupport: Completed SDK detection: ExitCode = 0
[2025.08.05-14.05.24:606][321]LogTurnkeySupport: Turnkey Platform: Android: (Status=Valid, MinAllowed_Sdk=r25b, MaxAllowed_Sdk=r27, Current_Sdk=r25b, Allowed_AutoSdk=r25b, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists, Support_FullSdk, Sdk_HasBestVersion")
[2025.08.05-14.05.24:606][321]LogTurnkeySupport: Turnkey Platform: IOS: (Status=Invalid, MinAllowed_Sdk=1100.0.0.0, MaxAllowed_Sdk=8999.0, Current_Sdk=, Allowed_AutoSdk=15.2, Current_AutoSdk=, Flags="Platform_ValidHostPrerequisites")
[2025.08.05-14.05.24:606][321]LogTurnkeySupport: Turnkey Platform: Linux: (Status=Invalid, Allowed_Sdk=v23_clang-18.1.0-rockylinux8, Current_Sdk=, Allowed_AutoSdk=v23_clang-18.1.0-rockylinux8, Current_AutoSdk=, Flags="Platform_ValidHostPrerequisites")
[2025.08.05-14.05.24:606][321]LogTurnkeySupport: Turnkey Platform: LinuxArm64: (Status=Invalid, Allowed_Sdk=v23_clang-18.1.0-rockylinux8, Current_Sdk=, Allowed_AutoSdk=v23_clang-18.1.0-rockylinux8, Current_AutoSdk=, Flags="Platform_ValidHostPrerequisites")
[2025.08.05-14.05.24:606][321]LogTurnkeySupport: Turnkey Platform: Mac: (Status=Invalid, MinAllowed_Sdk=1100.0.0.0, MaxAllowed_Sdk=8999.0, Current_Sdk=, Allowed_AutoSdk=15.2, Current_AutoSdk=, Flags="Platform_ValidHostPrerequisites")
[2025.08.05-14.05.24:606][321]LogTurnkeySupport: Turnkey Platform: PS4: (Status=Invalid, MinAllowed_Sdk=12.008.011, MaxAllowed_Sdk=12.999.999, Current_Sdk=9.508.001, Allowed_AutoSdk=12.008.011, Current_AutoSdk=, Flags="InstalledSdk_InvalidVersionExists, Platform_ValidHostPrerequisites")
[2025.08.05-14.05.24:606][321]LogTurnkeySupport: Turnkey Platform: PS5: (Status=Invalid, MinAllowed_Sdk=***********, MaxAllowed_Sdk=***********, Current_Sdk=**********, Allowed_AutoSdk=***********, Current_AutoSdk=, Flags="InstalledSdk_InvalidVersionExists, Platform_ValidHostPrerequisites")
[2025.08.05-14.05.24:606][321]LogTurnkeySupport: Turnkey Platform: Switch: (Status=Invalid, MinAllowed_Sdk=16.2.3, MaxAllowed_Sdk=18.3.1, Current_Sdk=, Allowed_AutoSdk=18.3.1, Current_AutoSdk=, Flags="Platform_ValidHostPrerequisites")
[2025.08.05-14.05.24:606][321]LogTurnkeySupport: Turnkey Platform: TVOS: (Status=Invalid, MinAllowed_Sdk=1100.0.0.0, MaxAllowed_Sdk=8999.0, Current_Sdk=, Allowed_AutoSdk=15.2, Current_AutoSdk=, Flags="Platform_ValidHostPrerequisites")
[2025.08.05-14.05.24:606][321]LogTurnkeySupport: Turnkey Platform: VisionOS: (Status=Invalid, , Flags="Host_Unsupported")
[2025.08.05-14.05.24:606][321]LogTurnkeySupport: Turnkey Platform: Win64: (Status=Valid, MinAllowed_Sdk=10.0.19041.0, MaxAllowed_Sdk=10.9.99999.0, Current_Sdk=10.0.22621.0, Allowed_AutoSdk=10.0.22621.0, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists, Sdk_HasBestVersion")
[2025.08.05-14.05.24:607][321]LogTurnkeySupport: Turnkey Platform: WinGDK: (Status=Invalid, MinAllowed_Sdk=240302, MaxAllowed_Sdk=240602, Current_Sdk=, Allowed_AutoSdk=240602, Current_AutoSdk=, Flags="Platform_ValidHostPrerequisites")
[2025.08.05-14.05.24:607][321]LogTurnkeySupport: Turnkey Platform: XB1: (Status=Invalid, MinAllowed_Sdk=240302, MaxAllowed_Sdk=240602, Current_Sdk=, Allowed_AutoSdk=240602, Current_AutoSdk=, Flags="Platform_ValidHostPrerequisites")
[2025.08.05-14.05.24:607][321]LogTurnkeySupport: Turnkey Platform: XSX: (Status=Invalid, MinAllowed_Sdk=240302, MaxAllowed_Sdk=240602, Current_Sdk=, Allowed_AutoSdk=240602, Current_AutoSdk=, Flags="Platform_ValidHostPrerequisites")
[2025.08.05-14.05.24:607][321]LogTurnkeySupport: Running Turnkey device detection: ' -ScriptsForProject="F:/TAMO_Streaming/Projects/TAMO/TAMO.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="F:/TAMO_Streaming/Projects/TAMO/Intermediate/TurnkeyReport_1.log" -log="F:/TAMO_Streaming/Projects/TAMO/Intermediate/TurnkeyLog_1.log" -project="F:/TAMO_Streaming/Projects/TAMO/TAMO.uproject"  -Device=Win64@CNCDUW1174'
[2025.08.05-14.05.24:607][321]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""F:/TAMO_Streaming/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="F:/TAMO_Streaming/Projects/TAMO/TAMO.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="F:/TAMO_Streaming/Projects/TAMO/Intermediate/TurnkeyReport_1.log" -log="F:/TAMO_Streaming/Projects/TAMO/Intermediate/TurnkeyLog_1.log" -project="F:/TAMO_Streaming/Projects/TAMO/TAMO.uproject"  -Device=Win64@CNCDUW1174" -nocompile -nocompileuat ]
[2025.08.05-14.05.27:210][453]LogTurnkeySupport: Completed device detection: Code = 0
[2025.08.05-14.05.27:211][454]LogTurnkeySupport: Turnkey Device: Win64@CNCDUW1174: (Name=CNCDUW1174, Type=Computer, Status=Valid, MinAllowed=10.0.19041.0, MaxAllowed=, Current=10.0.19045.0, Flags="Device_InstallSoftwareValid")
[2025.08.05-14.06.00:365][797]LogDerivedDataCache: ../../../Engine/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.08.05-14.10.41:605][640]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 380.972931
[2025.08.05-14.10.45:273][651]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-14.10.45:273][651]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 384.307556, Update Interval: 327.536255
[2025.08.05-14.14.32:478][332]LogUObjectHash: Compacting FUObjectHashTables data took   0.77ms
[2025.08.05-14.14.32:481][332]Cmd: OBJ SAVEPACKAGE PACKAGE="/Temp/Untitled_1" FILE="../../../Projects/TAMO/Saved/Autosaves/Temp/Untitled_1_Auto1.umap" SILENT=true AUTOSAVING=true KEEPDIRTY=false
[2025.08.05-14.14.32:498][332]LogSavePackage: Moving output files for package: /Temp/Autosaves/Temp/Untitled_1_Auto1
[2025.08.05-14.14.32:498][332]LogSavePackage: Moving '../../../Projects/TAMO/Saved/Untitled_1_Auto1D786136C4B409EFAEC700C8DB09F5E27.tmp' to '../../../Projects/TAMO/Saved/Autosaves/Temp/Untitled_1_Auto1.umap'
[2025.08.05-14.14.32:501][332]LogFileHelpers: Editor autosave (incl. external actors) for '/Temp/Untitled_1' took 0.044
[2025.08.05-14.14.32:501][332]LogFileHelpers: Editor autosave (incl. sublevels & external actors) for all levels took 0.044
[2025.08.05-14.16.16:465][644]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 715.828796
[2025.08.05-14.16.17:465][647]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-14.16.17:465][647]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 716.495483, Update Interval: 350.985443
[2025.08.05-14.22.16:286][723]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1075.648438
[2025.08.05-14.22.17:954][728]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-14.22.17:954][728]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1076.982300, Update Interval: 309.300201
[2025.08.05-14.27.39:783][693]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1399.142334
[2025.08.05-14.27.41:451][698]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-14.27.41:451][698]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1400.477051, Update Interval: 351.388275
[2025.08.05-14.33.56:278][822]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 1775.634644
[2025.08.05-14.33.57:280][825]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-14.33.57:280][825]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 1776.302979, Update Interval: 331.588501
[2025.08.05-14.36.32:364][290]LogEOSSDK: Warning: LogHttp: 0000044CF08B5A00: invalid HTTP response code received. URL: https://api.epicgames.dev/telemetry/data/datarouter/api/v1/public/data?SessionID=%7B483B2BCE-4F0A-1596-25EB-BFADD0111D3C%7D&AppID=EOSSDK.PhaseRelease.ReleaseBuild&AppVersion=1.16.4-36651368%20-%20%2B%2BEOSSDK%2BRelease-1.16.4-***********&UserID=&AppEnvironment=Production&UploadType=sdkevents, HTTP code: 0, content length: 0, actual payload size: 0
[2025.08.05-14.36.32:364][290]LogEOSSDK: Warning: LogHttp: 0000044CF08B5A00: request failed, libcurl error: 35 (SSL connect error)
[2025.08.05-14.36.32:364][290]LogEOSSDK: Warning: LogHttp: 0000044CF08B5A00: libcurl info message cache 0 (Found bundle for host: 0x44d0e080540 [serially])
[2025.08.05-14.36.32:364][290]LogEOSSDK: Warning: LogHttp: 0000044CF08B5A00: libcurl info message cache 1 (Re-using existing connection with host api.epicgames.dev)
[2025.08.05-14.36.32:364][290]LogEOSSDK: Warning: LogHttp: 0000044CF08B5A00: libcurl info message cache 2 (We are completely uploaded and fine)
[2025.08.05-14.36.32:364][290]LogEOSSDK: Warning: LogHttp: 0000044CF08B5A00: libcurl info message cache 3 (TLSv1.3 (IN), TLS alert, close notify (256):)
[2025.08.05-14.36.32:364][290]LogEOSSDK: Warning: LogHttp: 0000044CF08B5A00: libcurl info message cache 4 (Connection died, retrying a fresh connect (retry count: 1))
[2025.08.05-14.36.32:364][290]LogEOSSDK: Warning: LogHttp: 0000044CF08B5A00: libcurl info message cache 5 (state.rewindbeforesend = TRUE)
[2025.08.05-14.36.32:364][290]LogEOSSDK: Warning: LogHttp: 0000044CF08B5A00: libcurl info message cache 6 (Closing connection)
[2025.08.05-14.36.32:364][290]LogEOSSDK: Warning: LogHttp: 0000044CF08B5A00: libcurl info message cache 7 (TLSv1.3 (OUT), TLS alert, close notify (256):)
[2025.08.05-14.36.32:364][290]LogEOSSDK: Warning: LogHttp: 0000044CF08B5A00: libcurl info message cache 8 (Issue another request to this URL: 'https://api.epicgames.dev/telemetry/data/datarouter/api/v1/public/data?SessionID=%7B483B2BCE-4F0A-1596-25EB-BFADD0111D3C%7D&AppID=EOSSDK.PhaseRelease.ReleaseBuild&AppVersion=1.16.4-36651368%20-%20%2B%2BEOSSDK%2BRelease-1.16.4-***********&UserID=&AppEnvironment=Production&UploadType=sdkevents')
[2025.08.05-14.36.32:364][290]LogEOSSDK: Warning: LogHttp: 0000044CF08B5A00: libcurl info message cache 9 (  Trying *************:443...)
[2025.08.05-14.36.32:364][290]LogEOSSDK: Warning: LogHttp: 0000044CF08B5A00: libcurl info message cache 10 (Connected to api.epicgames.dev (*************) port 443)
[2025.08.05-14.36.32:364][290]LogEOSSDK: Warning: LogHttp: 0000044CF08B5A00: libcurl info message cache 11 (ALPN: curl offers http/1.1)
[2025.08.05-14.36.32:364][290]LogEOSSDK: Warning: LogHttp: 0000044CF08B5A00: libcurl info message cache 12 (SSL reusing session ID)
[2025.08.05-14.36.32:364][290]LogEOSSDK: Warning: LogHttp: 0000044CF08B5A00: libcurl info message cache 13 (TLSv1.3 (OUT), TLS handshake, Client hello (1):)
[2025.08.05-14.36.32:364][290]LogEOSSDK: Warning: LogHttp: 0000044CF08B5A00: libcurl info message cache 14 (OpenSSL SSL_connect: SSL_ERROR_SYSCALL in connection to api.epicgames.dev:443 )
[2025.08.05-14.36.32:364][290]LogEOSSDK: Warning: LogHttp: 0000044CF08B5A00: libcurl info message cache 15 (Closing connection)
[2025.08.05-14.36.32:364][291]LogEOSSDK: Warning: LogHttp: 0000044CF08BE400: invalid HTTP response code received. URL: https://api.epicgames.dev/datarouter/api/v1/public/data/clients?AppID=86f32f1151354e7cb39c12f8ab2c22a3&AppVersion=1.16.4-36651368&AppEnvironment=********************************&UploadType=eteventstream&SessionID=5D354248497F779D98A1F4B519E23DEE, HTTP code: 0, content length: 0, actual payload size: 0
[2025.08.05-14.36.32:364][291]LogEOSSDK: Warning: LogHttp: 0000044CF08BE400: request failed, libcurl error: 35 (SSL connect error)
[2025.08.05-14.36.32:364][291]LogEOSSDK: Warning: LogHttp: 0000044CF08BE400: libcurl info message cache 0 (Found bundle for host: 0x44d0e0801b0 [serially])
[2025.08.05-14.36.32:364][291]LogEOSSDK: Warning: LogHttp: 0000044CF08BE400: libcurl info message cache 1 (Connection #17 isn't open enough, can't reuse)
[2025.08.05-14.36.32:364][291]LogEOSSDK: Warning: LogHttp: 0000044CF08BE400: libcurl info message cache 2 (Hostname api.epicgames.dev was found in DNS cache)
[2025.08.05-14.36.32:364][291]LogEOSSDK: Warning: LogHttp: 0000044CF08BE400: libcurl info message cache 3 (  Trying *************:443...)
[2025.08.05-14.36.32:364][291]LogEOSSDK: Warning: LogHttp: 0000044CF08BE400: libcurl info message cache 4 (Connected to api.epicgames.dev (*************) port 443)
[2025.08.05-14.36.32:364][291]LogEOSSDK: Warning: LogHttp: 0000044CF08BE400: libcurl info message cache 5 (ALPN: curl offers http/1.1)
[2025.08.05-14.36.32:364][291]LogEOSSDK: Warning: LogHttp: 0000044CF08BE400: libcurl info message cache 6 (SSL reusing session ID)
[2025.08.05-14.36.32:364][291]LogEOSSDK: Warning: LogHttp: 0000044CF08BE400: libcurl info message cache 7 (TLSv1.3 (OUT), TLS handshake, Client hello (1):)
[2025.08.05-14.36.32:364][291]LogEOSSDK: Warning: LogHttp: 0000044CF08BE400: libcurl info message cache 8 (OpenSSL SSL_connect: SSL_ERROR_SYSCALL in connection to api.epicgames.dev:443 )
[2025.08.05-14.36.32:364][291]LogEOSSDK: Warning: LogHttp: 0000044CF08BE400: libcurl info message cache 9 (Closing connection)
[2025.08.05-14.36.32:364][291]LogEOSSDK: Warning: LogHttp: Retry exhausted on https://api.epicgames.dev/telemetry/data/datarouter/api/v1/public/data?SessionID=%7B483B2BCE-4F0A-1596-25EB-BFADD0111D3C%7D&AppID=EOSSDK.PhaseRelease.ReleaseBuild&AppVersion=1.16.4-36651368%20-%20%2B%2BEOSSDK%2BRelease-1.16.4-***********&UserID=&AppEnvironment=Production&UploadType=sdkevents
[2025.08.05-14.36.32:364][291]LogEOSSDK: Warning: LogHttp: Retry exhausted on https://api.epicgames.dev/datarouter/api/v1/public/data/clients?AppID=86f32f1151354e7cb39c12f8ab2c22a3&AppVersion=1.16.4-36651368&AppEnvironment=********************************&UploadType=eteventstream&SessionID=5D354248497F779D98A1F4B519E23DEE
[2025.08.05-14.36.32:364][291]LogEOSSDK: Warning: LogEOS: Failed to connect to the backend. ServiceName=[Metrics], OperationName=[SendBackendEvent], Url=[<Redacted>]
[2025.08.05-14.40.01:805][918]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2141.158936
[2025.08.05-14.40.03:471][923]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-14.40.03:471][923]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 2142.491211, Update Interval: 341.657776
[2025.08.05-14.46.28:018][ 76]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2527.367432
[2025.08.05-14.46.29:351][ 80]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-14.46.29:351][ 80]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 2528.367920, Update Interval: 346.526672
[2025.08.05-14.53.06:558][271]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 2925.905273
[2025.08.05-14.53.09:224][279]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-14.53.09:224][279]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 2928.237305, Update Interval: 337.916809
[2025.08.05-14.59.38:477][446]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 3317.822021
[2025.08.05-14.59.40:144][451]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-14.59.40:144][451]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 3319.158203, Update Interval: 322.317566
[2025.08.05-15.05.35:018][515]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 3674.362549
[2025.08.05-15.05.47:024][551]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-15.05.47:024][551]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 3686.034180, Update Interval: 321.998962
[2025.08.05-15.06.00:397][592]LogDerivedDataCache: ../../../Engine/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.08.05-15.07.34:091][872]LogHttp: Warning: 0000044D087A3F00: request failed, libcurl error: 35 (SSL connect error)
[2025.08.05-15.07.34:091][872]LogHttp: Warning: 0000044D087A3F00: libcurl info message cache 0 (  Trying 34.194.253.193:443...)
[2025.08.05-15.07.34:091][872]LogHttp: Warning: 0000044D087A3F00: libcurl info message cache 1 (Connected to datarouter.ol.epicgames.com (34.194.253.193) port 443)
[2025.08.05-15.07.34:091][872]LogHttp: Warning: 0000044D087A3F00: libcurl info message cache 2 (ALPN: curl offers http/1.1)
[2025.08.05-15.07.34:091][872]LogHttp: Warning: 0000044D087A3F00: libcurl info message cache 3 (SSL reusing session ID)
[2025.08.05-15.07.34:091][872]LogHttp: Warning: 0000044D087A3F00: libcurl info message cache 4 (TLSv1.3 (OUT), TLS handshake, Client hello (1):)
[2025.08.05-15.07.34:091][872]LogHttp: Warning: 0000044D087A3F00: libcurl info message cache 5 (OpenSSL SSL_connect: SSL_ERROR_SYSCALL in connection to datarouter.ol.epicgames.com:443 )
[2025.08.05-15.07.34:091][873]LogHttp: Warning: 0000044D087A3F00: libcurl info message cache 6 (Closing connection)
[2025.08.05-15.11.40:891][612]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 4040.234619
[2025.08.05-15.11.46:226][628]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-15.11.46:226][628]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 4045.237305, Update Interval: 347.043060
[2025.08.05-15.18.26:447][828]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 4445.782227
[2025.08.05-15.18.36:784][859]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-15.18.36:784][859]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 4455.785645, Update Interval: 350.452576
[2025.08.05-15.24.58:646][  4]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 4837.982422
[2025.08.05-15.25.02:313][ 15]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-15.25.02:313][ 15]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 4841.317383, Update Interval: 304.555817
[2025.08.05-15.30.53:820][ 69]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 5193.165039
[2025.08.05-15.30.55:485][ 74]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-15.30.55:485][ 74]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 5194.497070, Update Interval: 352.847687
[2025.08.05-15.37.29:376][255]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 5588.711914
[2025.08.05-15.37.31:043][260]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-15.37.31:043][260]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 5590.045898, Update Interval: 354.460876
[2025.08.05-15.44.20:949][489]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 6000.286621
[2025.08.05-15.44.24:284][499]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-15.44.24:285][499]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 6003.289551, Update Interval: 326.333191
[2025.08.05-15.50.48:854][652]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 6388.182129
[2025.08.05-15.50.52:521][663]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-15.50.52:521][663]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 6391.516602, Update Interval: 357.938171
[2025.08.05-15.57.21:066][828]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 6780.388672
[2025.08.05-15.57.25:400][841]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-15.57.25:401][841]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 6784.388672, Update Interval: 343.254486
[2025.08.05-16.04.02:292][ 31]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 7181.615723
[2025.08.05-16.04.03:959][ 36]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-16.04.03:959][ 36]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 7182.949219, Update Interval: 321.374542
[2025.08.05-16.06.00:421][386]LogDerivedDataCache: ../../../Engine/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.08.05-16.09.59:834][103]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 7539.155273
[2025.08.05-16.10.01:501][108]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-16.10.01:502][108]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 7540.487793, Update Interval: 358.736542
[2025.08.05-16.14.32:983][922]LogEOSSDK: Warning: LogHttp: 0000044CF0BF1800: invalid HTTP response code received. URL: https://api.epicgames.dev/telemetry/data/datarouter/api/v1/public/data?SessionID=%7B483B2BCE-4F0A-1596-25EB-BFADD0111D3C%7D&AppID=EOSSDK.PhaseRelease.ReleaseBuild&AppVersion=1.16.4-36651368%20-%20%2B%2BEOSSDK%2BRelease-1.16.4-***********&UserID=&AppEnvironment=Production&UploadType=sdkevents, HTTP code: 0, content length: 0, actual payload size: 0
[2025.08.05-16.14.32:984][922]LogEOSSDK: Warning: LogHttp: 0000044CF0BF1800: request failed, libcurl error: 35 (SSL connect error)
[2025.08.05-16.14.32:984][922]LogEOSSDK: Warning: LogHttp: 0000044CF0BF1800: libcurl info message cache 0 (Found bundle for host: 0x44d0e080b40 [serially])
[2025.08.05-16.14.32:984][922]LogEOSSDK: Warning: LogHttp: 0000044CF0BF1800: libcurl info message cache 1 (Re-using existing connection with host api.epicgames.dev)
[2025.08.05-16.14.32:984][922]LogEOSSDK: Warning: LogHttp: 0000044CF0BF1800: libcurl info message cache 2 (We are completely uploaded and fine)
[2025.08.05-16.14.32:984][922]LogEOSSDK: Warning: LogHttp: 0000044CF0BF1800: libcurl info message cache 3 (TLSv1.3 (IN), TLS alert, close notify (256):)
[2025.08.05-16.14.32:984][922]LogEOSSDK: Warning: LogHttp: 0000044CF0BF1800: libcurl info message cache 4 (Connection died, retrying a fresh connect (retry count: 1))
[2025.08.05-16.14.32:984][922]LogEOSSDK: Warning: LogHttp: 0000044CF0BF1800: libcurl info message cache 5 (state.rewindbeforesend = TRUE)
[2025.08.05-16.14.32:984][923]LogEOSSDK: Warning: LogHttp: 0000044CF0BF1800: libcurl info message cache 6 (Closing connection)
[2025.08.05-16.14.32:984][923]LogEOSSDK: Warning: LogHttp: 0000044CF0BF1800: libcurl info message cache 7 (TLSv1.3 (OUT), TLS alert, close notify (256):)
[2025.08.05-16.14.32:984][923]LogEOSSDK: Warning: LogHttp: 0000044CF0BF1800: libcurl info message cache 8 (Issue another request to this URL: 'https://api.epicgames.dev/telemetry/data/datarouter/api/v1/public/data?SessionID=%7B483B2BCE-4F0A-1596-25EB-BFADD0111D3C%7D&AppID=EOSSDK.PhaseRelease.ReleaseBuild&AppVersion=1.16.4-36651368%20-%20%2B%2BEOSSDK%2BRelease-1.16.4-***********&UserID=&AppEnvironment=Production&UploadType=sdkevents')
[2025.08.05-16.14.32:984][923]LogEOSSDK: Warning: LogHttp: 0000044CF0BF1800: libcurl info message cache 9 (  Trying 18.207.41.92:443...)
[2025.08.05-16.14.32:984][923]LogEOSSDK: Warning: LogHttp: 0000044CF0BF1800: libcurl info message cache 10 (Connected to api.epicgames.dev (18.207.41.92) port 443)
[2025.08.05-16.14.32:984][923]LogEOSSDK: Warning: LogHttp: 0000044CF0BF1800: libcurl info message cache 11 (ALPN: curl offers http/1.1)
[2025.08.05-16.14.32:984][923]LogEOSSDK: Warning: LogHttp: 0000044CF0BF1800: libcurl info message cache 12 (SSL reusing session ID)
[2025.08.05-16.14.32:984][923]LogEOSSDK: Warning: LogHttp: 0000044CF0BF1800: libcurl info message cache 13 (TLSv1.3 (OUT), TLS handshake, Client hello (1):)
[2025.08.05-16.14.32:984][923]LogEOSSDK: Warning: LogHttp: 0000044CF0BF1800: libcurl info message cache 14 (OpenSSL SSL_connect: SSL_ERROR_SYSCALL in connection to api.epicgames.dev:443 )
[2025.08.05-16.14.32:984][923]LogEOSSDK: Warning: LogHttp: 0000044CF0BF1800: libcurl info message cache 15 (Closing connection)
[2025.08.05-16.14.32:984][923]LogEOSSDK: Warning: LogHttp: Retry exhausted on https://api.epicgames.dev/telemetry/data/datarouter/api/v1/public/data?SessionID=%7B483B2BCE-4F0A-1596-25EB-BFADD0111D3C%7D&AppID=EOSSDK.PhaseRelease.ReleaseBuild&AppVersion=1.16.4-36651368%20-%20%2B%2BEOSSDK%2BRelease-1.16.4-***********&UserID=&AppEnvironment=Production&UploadType=sdkevents
[2025.08.05-16.16.58:733][359]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 7958.054199
[2025.08.05-16.17.00:398][364]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-16.17.00:398][364]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 7959.386230, Update Interval: 359.633789
[2025.08.05-16.23.54:566][606]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 8373.870117
[2025.08.05-16.23.55:566][609]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-16.23.55:567][610]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 8374.541016, Update Interval: 315.104828
[2025.08.05-16.29.54:728][686]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 8734.032227
[2025.08.05-16.29.55:728][689]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-16.29.55:728][689]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 8734.699219, Update Interval: 330.841400
[2025.08.05-16.36.23:940][853]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 9123.244141
[2025.08.05-16.36.24:944][856]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-16.36.24:944][856]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 9123.912109, Update Interval: 350.031433
[2025.08.05-16.43.08:154][ 65]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 9527.452148
[2025.08.05-16.43.09:154][ 68]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-16.43.09:154][ 68]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 9528.120117, Update Interval: 344.585724
[2025.08.05-16.49.37:372][232]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 9916.676758
[2025.08.05-16.49.38:371][235]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-16.49.38:371][235]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 9917.342773, Update Interval: 345.228424
[2025.08.05-16.56.04:571][393]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 10303.878906
[2025.08.05-16.56.05:575][396]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-16.56.05:575][396]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 10304.545898, Update Interval: 315.370331
[2025.08.05-17.01.57:457][451]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 10656.777344
[2025.08.05-17.02.00:126][459]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-17.02.00:126][459]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 10659.112305, Update Interval: 302.647797
[2025.08.05-17.06.00:440][180]LogDerivedDataCache: ../../../Engine/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.08.05-17.07.40:991][481]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 11000.317383
[2025.08.05-17.07.42:326][485]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-17.07.42:326][485]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 11001.319336, Update Interval: 303.239227
[2025.08.05-17.13.17:843][491]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 11337.167969
[2025.08.05-17.13.18:842][494]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-17.13.18:843][494]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 11337.833984, Update Interval: 304.989777
[2025.08.05-17.19.00:031][517]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 11679.361328
[2025.08.05-17.19.01:365][521]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-17.19.01:365][521]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 11680.361328, Update Interval: 353.407990
[2025.08.05-17.25.26:937][677]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 12066.280273
[2025.08.05-17.25.27:937][680]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-17.25.27:937][680]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 12066.947266, Update Interval: 347.431244
[2025.08.05-17.32.03:808][867]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 12463.152344
[2025.08.05-17.32.04:807][870]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-17.32.04:807][870]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 12463.818359, Update Interval: 316.787628
[2025.08.05-17.37.51:668][910]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 12810.995117
[2025.08.05-17.37.52:670][913]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-17.37.52:670][914]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 12811.662109, Update Interval: 357.425446
[2025.08.05-17.44.45:584][151]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 13224.902344
[2025.08.05-17.44.46:585][154]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-17.44.46:585][154]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 13225.569336, Update Interval: 343.314911
[2025.08.05-17.51.15:502][320]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 13614.830078
[2025.08.05-17.51.16:502][323]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-17.51.16:502][323]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 13615.498047, Update Interval: 329.495544
[2025.08.05-17.57.34:069][455]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 13993.396484
[2025.08.05-17.57.35:071][458]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-17.57.35:071][458]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 13994.064453, Update Interval: 309.888000
[2025.08.05-18.03.29:971][522]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 14349.298828
[2025.08.05-18.03.30:971][525]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-18.03.30:971][525]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 14349.963867, Update Interval: 351.829590
[2025.08.05-18.06.00:454][974]LogDerivedDataCache: ../../../Engine/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.08.05-18.10.11:215][725]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 14750.527344
[2025.08.05-18.10.12:217][728]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-18.10.12:217][728]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 14751.195312, Update Interval: 314.861298
[2025.08.05-18.16.24:107][843]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 15123.422852
[2025.08.05-18.16.25:110][846]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-18.16.25:110][846]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 15124.089844, Update Interval: 331.599487
[2025.08.05-18.22.29:986][940]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 15489.290039
[2025.08.05-18.22.30:988][943]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-18.22.30:989][943]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 15489.956055, Update Interval: 320.911285
[2025.08.05-18.28.38:151][ 44]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 15857.448242
[2025.08.05-18.28.39:153][ 47]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-18.28.39:153][ 48]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 15858.115234, Update Interval: 335.875122
[2025.08.05-18.35.07:018][210]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 16246.309570
[2025.08.05-18.35.08:018][213]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-18.35.08:018][213]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 16246.976562, Update Interval: 358.203674
[2025.08.05-18.41.44:565][402]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 16643.896484
[2025.08.05-18.41.45:565][405]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-18.41.45:565][405]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 16644.562500, Update Interval: 334.985199
[2025.08.05-18.48.07:776][551]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 17027.166016
[2025.08.05-18.48.08:778][554]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-18.48.08:778][554]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 17027.832031, Update Interval: 329.671326
[2025.08.05-18.54.33:684][708]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 17413.128906
[2025.08.05-18.54.34:685][711]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-18.54.34:685][711]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 17413.794922, Update Interval: 325.221710
[2025.08.05-19.00.59:569][865]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 17799.068359
[2025.08.05-19.01.00:567][868]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-19.01.00:567][868]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 17799.736328, Update Interval: 350.366516
[2025.08.05-19.06.00:467][768]LogDerivedDataCache: ../../../Engine/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.08.05-19.07.48:827][ 92]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 18208.431641
[2025.08.05-19.07.49:823][ 95]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-19.07.49:823][ 95]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 18209.093750, Update Interval: 337.753845
[2025.08.05-19.14.20:352][266]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 18600.050781
[2025.08.05-19.14.21:350][269]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-19.14.21:350][269]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 18600.714844, Update Interval: 302.656952
[2025.08.05-19.20.19:518][343]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 18959.296875
[2025.08.05-19.20.20:518][346]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-19.20.20:518][346]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 18959.966797, Update Interval: 350.624725
[2025.08.05-19.26.55:725][531]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 19355.597656
[2025.08.05-19.26.56:725][534]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-19.26.56:725][534]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 19356.263672, Update Interval: 319.237640
[2025.08.05-19.32.46:907][584]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 19706.822266
[2025.08.05-19.32.47:907][587]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-19.32.47:907][587]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 19707.488281, Update Interval: 330.716888
[2025.08.05-19.39.03:442][713]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 20083.431641
[2025.08.05-19.39.04:442][716]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-19.39.04:442][716]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 20084.097656, Update Interval: 335.770752
[2025.08.05-19.45.11:306][816]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 20451.378906
[2025.08.05-19.45.12:305][819]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-19.45.12:305][819]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 20452.044922, Update Interval: 302.543427
[2025.08.05-19.50.58:147][856]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 20798.287109
[2025.08.05-19.50.59:147][859]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-19.50.59:147][859]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 20798.955078, Update Interval: 305.044708
[2025.08.05-19.56.44:333][894]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 21144.515625
[2025.08.05-19.56.45:333][897]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-19.56.45:333][897]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 21145.183594, Update Interval: 310.759613
[2025.08.05-20.02.44:517][974]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 21504.718750
[2025.08.05-20.02.45:517][977]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-20.02.45:517][977]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 21505.386719, Update Interval: 354.559753
[2025.08.05-20.06.00:477][562]LogDerivedDataCache: ../../../Engine/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.08.05-20.09.16:385][149]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 21896.707031
[2025.08.05-20.09.17:386][152]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-20.09.17:386][152]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 21897.375000, Update Interval: 353.252350
[2025.08.05-20.15.48:577][325]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 22288.988281
[2025.08.05-20.15.49:576][328]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-20.15.49:577][328]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 22289.654297, Update Interval: 319.340179
[2025.08.05-20.21.45:400][395]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 22645.902344
[2025.08.05-20.21.46:400][398]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-20.21.46:400][398]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 22646.566406, Update Interval: 331.694702
[2025.08.05-20.27.48:564][484]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 23009.150391
[2025.08.05-20.27.49:564][487]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-20.27.49:564][487]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 23009.818359, Update Interval: 331.951050
[2025.08.05-20.34.18:413][653]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 23399.001953
[2025.08.05-20.34.19:416][656]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-20.34.19:417][657]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 23399.669922, Update Interval: 336.444580
[2025.08.05-20.40.42:959][806]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 23783.595703
[2025.08.05-20.40.44:293][810]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-20.40.44:293][810]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 23784.599609, Update Interval: 341.485657
[2025.08.05-20.47.11:516][971]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 24172.205078
[2025.08.05-20.47.12:512][974]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-20.47.12:512][974]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 24172.869141, Update Interval: 352.629791
[2025.08.05-20.53.59:741][195]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 24580.478516
[2025.08.05-20.54.00:741][198]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-20.54.00:741][198]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 24581.146484, Update Interval: 303.488251
[2025.08.05-20.59.35:264][201]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 24916.046875
[2025.08.05-20.59.36:265][204]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-20.59.36:265][204]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 24916.714844, Update Interval: 304.350708
[2025.08.05-21.05.25:793][252]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 25266.669922
[2025.08.05-21.05.26:794][255]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-21.05.26:795][255]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 25267.337891, Update Interval: 352.638947
[2025.08.05-21.06.00:487][357]LogDerivedDataCache: ../../../Engine/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.08.05-21.12.15:331][480]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 25676.240234
[2025.08.05-21.12.16:332][483]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-21.12.16:332][483]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 25676.906250, Update Interval: 303.817871
[2025.08.05-21.18.11:143][547]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 26032.066406
[2025.08.05-21.18.12:144][550]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-21.18.12:144][550]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 26032.734375, Update Interval: 300.065918
[2025.08.05-21.24.00:966][596]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 26381.968750
[2025.08.05-21.24.01:966][599]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-21.24.01:966][599]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 26382.636719, Update Interval: 342.344421
[2025.08.05-21.30.16:159][721]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 26757.205078
[2025.08.05-21.30.17:160][724]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-21.30.17:160][724]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 26757.873047, Update Interval: 347.231659
[2025.08.05-21.36.57:034][923]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 27158.126953
[2025.08.05-21.36.58:035][926]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-21.36.58:036][926]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 27158.794922, Update Interval: 310.027161
[2025.08.05-21.42.46:229][970]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 27507.375000
[2025.08.05-21.42.47:229][973]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-21.42.47:229][973]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 27508.041016, Update Interval: 318.157288
[2025.08.05-21.43.56:235][180]LogUdpMessaging: Warning: FUdpMessageProcessor::UpdateReassemblers Discarding 2/3 of stale message segements from Sender=10.72.90.88:53155, Sequence=0
[2025.08.05-21.43.56:235][180]LogUdpMessaging: Warning: FUdpMessageProcessor::UpdateReassemblers Discarding 2/3 of stale message segements from Sender=10.72.90.88:53155, Sequence=0
[2025.08.05-21.43.56:235][180]LogUdpMessaging: Warning: FUdpMessageProcessor::UpdateReassemblers Discarding 2/3 of stale message segements from Sender=10.72.90.88:53155, Sequence=0
[2025.08.05-21.43.56:235][180]LogUdpMessaging: Warning: FUdpMessageProcessor::UpdateReassemblers Discarding 2/3 of stale message segements from Sender=10.72.90.88:53155, Sequence=0
[2025.08.05-21.43.56:235][180]LogUdpMessaging: Warning: FUdpMessageProcessor::UpdateReassemblers Discarding 2/3 of stale message segements from Sender=10.72.90.88:53155, Sequence=0
[2025.08.05-21.43.56:235][180]LogUdpMessaging: Warning: FUdpMessageProcessor::UpdateReassemblers Discarding 2/3 of stale message segements from Sender=10.72.90.88:53155, Sequence=0
[2025.08.05-21.43.56:235][180]LogUdpMessaging: Warning: FUdpMessageProcessor::UpdateReassemblers Discarding 2/3 of stale message segements from Sender=10.72.90.88:53155, Sequence=0
[2025.08.05-21.43.56:235][180]LogUdpMessaging: Warning: FUdpMessageProcessor::UpdateReassemblers Discarding 2/3 of stale message segements from Sender=10.72.90.88:53155, Sequence=0
[2025.08.05-21.43.58:321][187]LogUdpMessaging: Warning: FUdpMessageProcessor::UpdateReassemblers Discarding 1/2 of stale message segements from Sender=10.72.90.136:49880, Sequence=0
[2025.08.05-21.43.58:321][187]LogUdpMessaging: Warning: FUdpMessageProcessor::UpdateReassemblers Discarding 1/2 of stale message segements from Sender=10.72.90.136:49880, Sequence=0
[2025.08.05-21.43.58:321][187]LogUdpMessaging: Warning: FUdpMessageProcessor::UpdateReassemblers Discarding 1/2 of stale message segements from Sender=10.72.90.136:49880, Sequence=0
[2025.08.05-21.43.58:322][187]LogUdpMessaging: Warning: FUdpMessageProcessor::UpdateReassemblers Discarding 1/2 of stale message segements from Sender=10.72.90.136:49880, Sequence=0
[2025.08.05-21.43.58:322][187]LogUdpMessaging: Warning: FUdpMessageProcessor::UpdateReassemblers Discarding 1/2 of stale message segements from Sender=10.72.90.136:49880, Sequence=0
[2025.08.05-21.43.58:322][187]LogUdpMessaging: Warning: FUdpMessageProcessor::UpdateReassemblers Discarding 1/2 of stale message segements from Sender=10.72.90.136:49880, Sequence=0
[2025.08.05-21.48.51:769][ 66]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 27872.917969
[2025.08.05-21.48.52:768][ 69]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-21.48.52:769][ 69]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 27873.583984, Update Interval: 301.043732
[2025.08.05-21.54.40:898][113]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 28222.039062
[2025.08.05-21.54.41:898][116]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-21.54.41:898][116]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 28222.703125, Update Interval: 321.966003
[2025.08.05-22.00.37:745][183]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 28578.929688
[2025.08.05-22.00.38:746][186]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-22.00.38:746][186]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 28579.597656, Update Interval: 351.388275
[2025.08.05-22.06.00:530][151]LogDerivedDataCache: ../../../Engine/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.08.05-22.07.21:000][392]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 28982.244141
[2025.08.05-22.07.22:000][395]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-22.07.22:000][395]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 28982.910156, Update Interval: 327.554565
[2025.08.05-22.13.36:542][518]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 29357.857422
[2025.08.05-22.13.37:542][521]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-22.13.37:543][521]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 29358.525391, Update Interval: 345.938904
[2025.08.05-22.20.03:382][678]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 29744.730469
[2025.08.05-22.20.04:382][681]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-22.20.04:382][681]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 29745.398438, Update Interval: 302.287048
[2025.08.05-22.25.59:555][746]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 30100.982422
[2025.08.05-22.26.00:553][749]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-22.26.00:553][749]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 30101.648438, Update Interval: 321.760925
[2025.08.05-22.32.00:054][827]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 30461.501953
[2025.08.05-22.32.01:055][830]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-22.32.01:055][830]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 30462.169922, Update Interval: 316.604523
[2025.08.05-22.37.57:963][900]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 30819.433594
[2025.08.05-22.37.58:961][903]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-22.37.58:961][903]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 30820.099609, Update Interval: 344.477661
[2025.08.05-22.44.22:878][ 54]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 31204.400391
[2025.08.05-22.44.23:881][ 57]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-22.44.23:881][ 57]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 31205.068359, Update Interval: 346.378357
[2025.08.05-22.51.09:752][274]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 31611.328125
[2025.08.05-22.51.10:751][277]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-22.51.10:751][277]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 31611.996094, Update Interval: 304.597931
[2025.08.05-22.56.54:991][309]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 31956.605469
[2025.08.05-22.56.55:992][312]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-22.56.55:992][312]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 31957.273438, Update Interval: 314.938202
[2025.08.05-23.03.02:565][411]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 32324.212891
[2025.08.05-23.03.03:566][414]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-23.03.03:566][414]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 32324.878906, Update Interval: 310.695526
[2025.08.05-23.06.00:579][945]LogDerivedDataCache: ../../../Engine/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.08.05-23.08.47:787][446]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 32669.500000
[2025.08.05-23.08.48:788][449]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-23.08.48:788][449]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 32670.167969, Update Interval: 346.625580
[2025.08.05-23.15.17:677][615]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 33059.027344
[2025.08.05-23.15.18:677][618]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-23.15.18:677][618]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 33059.699219, Update Interval: 346.292297
[2025.08.05-23.21.37:212][753]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 33437.957031
[2025.08.05-23.21.38:213][756]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-23.21.38:213][756]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 33438.621094, Update Interval: 333.712585
[2025.08.05-23.27.42:065][847]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 33802.316406
[2025.08.05-23.27.43:065][850]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-23.27.43:065][850]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 33802.984375, Update Interval: 347.317719
[2025.08.05-23.34.24:613][ 54]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 34204.398438
[2025.08.05-23.34.25:613][ 57]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-23.34.25:613][ 57]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 34205.062500, Update Interval: 348.630646
[2025.08.05-23.41.09:177][267]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 34608.476562
[2025.08.05-23.41.10:179][270]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-23.41.10:179][270]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 34609.148438, Update Interval: 327.045502
[2025.08.05-23.47.28:753][405]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 34987.468750
[2025.08.05-23.47.29:753][408]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-23.47.29:753][408]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 34988.132812, Update Interval: 343.331390
[2025.08.05-23.53.56:308][567]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 35374.601562
[2025.08.05-23.53.57:310][570]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-23.53.57:310][570]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 35375.269531, Update Interval: 325.360870
[2025.08.05-23.59.57:186][649]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 35735.089844
[2025.08.05-23.59.58:187][652]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.05-23.59.58:187][652]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 35735.757812, Update Interval: 307.670532
[2025.08.06-00.05.44:383][690]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 36081.941406
[2025.08.06-00.05.45:382][693]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.06-00.05.45:382][693]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 36082.605469, Update Interval: 349.227570
[2025.08.06-00.06.00:614][739]LogDerivedDataCache: ../../../Engine/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.08.06-00.12.22:608][884]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 36479.523438
[2025.08.06-00.12.23:610][887]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.06-00.12.23:610][887]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 36480.187500, Update Interval: 348.696564
[2025.08.06-00.19.06:162][ 94]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 36882.539062
[2025.08.06-00.19.07:161][ 97]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.06-00.19.07:162][ 97]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 36883.207031, Update Interval: 321.253693
[2025.08.06-00.25.23:691][226]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 37259.519531
[2025.08.06-00.25.24:689][229]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.06-00.25.24:689][229]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 37260.187500, Update Interval: 302.697235
[2025.08.06-00.31.01:174][238]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 37596.761719
[2025.08.06-00.31.02:175][241]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.06-00.31.02:175][241]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 37597.429688, Update Interval: 323.225800
[2025.08.06-00.37.10:048][344]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 37965.132812
[2025.08.06-00.37.11:047][347]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.06-00.37.11:047][347]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 37965.800781, Update Interval: 321.927551
[2025.08.06-00.43.04:565][407]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 38319.207031
[2025.08.06-00.43.05:563][410]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.06-00.43.05:563][410]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 38319.867188, Update Interval: 311.444427
[2025.08.06-00.49.10:083][503]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 38684.191406
[2025.08.06-00.49.11:082][506]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.06-00.49.11:082][506]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 38684.859375, Update Interval: 307.815186
[2025.08.06-00.54.59:270][550]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 39032.863281
[2025.08.06-00.55.00:269][553]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.06-00.55.00:269][553]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 39033.527344, Update Interval: 310.818207
[2025.08.06-01.00.41:743][576]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 39374.816406
[2025.08.06-01.00.42:744][579]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.06-01.00.42:744][579]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 39375.484375, Update Interval: 352.607819
[2025.08.06-01.06.00:640][533]LogDerivedDataCache: ../../../Engine/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.08.06-01.07.12:592][748]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 39765.070312
[2025.08.06-01.07.13:591][751]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.06-01.07.13:591][751]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 39765.734375, Update Interval: 324.503906
[2025.08.06-01.13.27:793][873]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 40139.683594
[2025.08.06-01.13.28:792][876]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.06-01.13.28:792][876]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 40140.351562, Update Interval: 301.261627
[2025.08.06-01.19.29:311][957]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 40500.617188
[2025.08.06-01.19.30:310][960]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.06-01.19.30:310][960]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 40501.281250, Update Interval: 306.028015
[2025.08.06-01.25.13:832][990]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 40844.750000
[2025.08.06-01.25.14:830][993]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.06-01.25.14:830][993]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 40845.414062, Update Interval: 355.510132
[2025.08.06-01.31.53:022][187]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 41243.472656
[2025.08.06-01.31.54:024][190]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.06-01.31.54:024][190]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 41244.136719, Update Interval: 330.740692
[2025.08.06-01.38.09:895][317]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 41619.925781
[2025.08.06-01.38.10:895][320]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.06-01.38.10:895][320]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 41620.593750, Update Interval: 351.908325
[2025.08.06-01.44.49:479][515]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 42018.972656
[2025.08.06-01.44.50:480][518]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.06-01.44.50:480][518]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 42019.636719, Update Interval: 353.475769
[2025.08.06-01.51.24:687][700]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 42413.597656
[2025.08.06-01.51.25:688][703]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.06-01.51.25:688][703]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 42414.265625, Update Interval: 355.834229
[2025.08.06-01.58.13:572][926]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 42821.929688
[2025.08.06-01.58.14:906][930]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.06-01.58.14:906][930]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 42822.925781, Update Interval: 349.306305
[2025.08.06-02.04.49:108][112]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 43216.992188
[2025.08.06-02.04.50:109][115]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.06-02.04.50:109][115]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 43217.656250, Update Interval: 323.218475
[2025.08.06-02.06.00:659][327]LogDerivedDataCache: ../../../Engine/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.08.06-02.11.07:662][247]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 43595.000000
[2025.08.06-02.11.08:662][250]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.06-02.11.08:662][250]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 43595.664062, Update Interval: 344.246948
[2025.08.06-02.17.34:190][406]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 43981.105469
[2025.08.06-02.17.35:190][409]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.06-02.17.35:190][409]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 43981.773438, Update Interval: 331.943726
[2025.08.06-02.22.38:201][318]LogUdpMessaging: Warning: FUdpMessageProcessor::UpdateReassemblers Discarding 1/2 of stale message segements from Sender=10.72.90.97:64634, Sequence=0
[2025.08.06-02.22.38:202][318]LogUdpMessaging: Warning: FUdpMessageProcessor::UpdateReassemblers Discarding 1/2 of stale message segements from Sender=10.72.90.97:64634, Sequence=0
[2025.08.06-02.23.56:346][552]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 44363.039062
[2025.08.06-02.23.57:346][555]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.06-02.23.57:346][555]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 44363.703125, Update Interval: 352.807404
[2025.08.06-02.30.42:192][769]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 44768.546875
[2025.08.06-02.30.43:525][773]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.06-02.30.43:526][773]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 44769.550781, Update Interval: 334.759979
[2025.08.06-02.37.01:054][905]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 45146.968750
[2025.08.06-02.37.02:054][908]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.06-02.37.02:054][908]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 45147.632812, Update Interval: 329.248322
[2025.08.06-02.43.06:585][  1]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 45512.074219
[2025.08.06-02.43.07:919][  5]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.06-02.43.07:919][  5]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 45513.074219, Update Interval: 335.598633
[2025.08.06-02.49.20:143][121]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 45885.156250
[2025.08.06-02.49.21:142][124]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.06-02.49.21:142][124]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 45885.820312, Update Interval: 329.418610
[2025.08.06-02.55.29:986][230]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 46254.710938
[2025.08.06-02.55.30:987][233]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.06-02.55.30:987][233]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 46255.378906, Update Interval: 301.166412
[2025.08.06-03.01.31:151][313]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 46615.722656
[2025.08.06-03.01.32:152][316]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.06-03.01.32:152][316]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 46616.386719, Update Interval: 330.068665
[2025.08.06-03.06.00:675][122]LogDerivedDataCache: ../../../Engine/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.08.06-03.07.46:865][637]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 46990.976562
[2025.08.06-03.07.48:199][641]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.06-03.07.48:199][641]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 46991.976562, Update Interval: 349.687195
[2025.08.06-03.10.26:513][641]LogUdpMessaging: Warning: FUdpMessageProcessor::UpdateReassemblers Discarding 1/2 of stale message segements from Sender=10.72.90.97:64634, Sequence=0
[2025.08.06-03.10.26:513][641]LogUdpMessaging: Warning: FUdpMessageProcessor::UpdateReassemblers Discarding 1/2 of stale message segements from Sender=10.72.90.97:64634, Sequence=0
[2025.08.06-03.14.27:256][362]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 47391.191406
[2025.08.06-03.14.28:257][365]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.06-03.14.28:258][365]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 47391.859375, Update Interval: 322.482361
[2025.08.06-03.20.30:762][452]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 47754.464844
[2025.08.06-03.20.31:762][455]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.06-03.20.31:762][455]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 47755.132812, Update Interval: 348.643463
[2025.08.06-03.25.56:607][430]LogUdpMessaging: Warning: FUdpMessageProcessor::UpdateReassemblers Discarding 2/3 of stale message segements from Sender=10.72.90.86:53651, Sequence=0
[2025.08.06-03.27.13:909][661]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 48157.445312
[2025.08.06-03.27.15:242][665]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.06-03.27.15:242][665]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 48158.445312, Update Interval: 307.335419
[2025.08.06-03.32.56:022][687]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 48499.394531
[2025.08.06-03.32.57:353][691]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.06-03.32.57:353][691]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 48500.390625, Update Interval: 303.460785
[2025.08.06-03.35.00:148][195]LogSlate: Window 'CPD Manager' being destroyed
[2025.08.06-03.35.00:159][195]LogSlate: Window 'CPD Manager' being destroyed
[2025.08.06-03.35.01:069][228]LogUObjectHash: Compacting FUObjectHashTables data took   0.56ms
[2025.08.06-03.35.02:484][228]LogSlate: Window 'Save Content' being destroyed
[2025.08.06-03.35.02:577][228]LogStall: Shutdown...
[2025.08.06-03.35.02:577][228]LogStall: Shutdown complete.
[2025.08.06-03.35.02:663][228]LogSlate: Window 'TAMO - Unreal Editor' being destroyed
[2025.08.06-03.35.02:822][228]Cmd: QUIT_EDITOR
[2025.08.06-03.35.02:822][228]LogCore: Engine exit requested (reason: UUnrealEdEngine::CloseEditor())
[2025.08.06-03.35.02:836][229]LogCore: Engine exit requested (reason: EngineExit() was called; note: exit was already requested)
[2025.08.06-03.35.02:837][229]LogStaticMesh: Abandoning remaining async distance field tasks for shutdown
[2025.08.06-03.35.02:837][229]LogStaticMesh: Abandoning remaining async card representation tasks for shutdown
[2025.08.06-03.35.02:857][229]LogWorld: UWorld::CleanupWorld for Untitled_1, bSessionEnded=true, bCleanupResources=true
[2025.08.06-03.35.02:859][229]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.08.06-03.35.02:859][229]LogWorldPartition: UWorldPartition::Uninitialize : World = /Temp/Untitled_1.Untitled_1
[2025.08.06-03.35.02:895][229]LogStylusInput: Shutting down StylusInput subsystem.
[2025.08.06-03.35.02:902][229]LogLevelSequenceEditor: LevelSequenceEditor subsystem deinitialized.
[2025.08.06-03.35.02:907][229]LogWorld: UWorld::CleanupWorld for World_0, bSessionEnded=true, bCleanupResources=true
[2025.08.06-03.35.02:908][229]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.08.06-03.35.02:909][229]LogWorld: UWorld::CleanupWorld for World_1, bSessionEnded=true, bCleanupResources=true
[2025.08.06-03.35.02:909][229]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.08.06-03.35.02:918][229]LogStudioTelemetry: Ended StudioTelemetry Session
[2025.08.06-03.35.02:945][229]LogAnalyticsSessionSummarySender: Sent summary session report for: AppId=UEEditor.Perforce.Release SessionId={29A92622-4D83-2970-2D5B-7CB98E57ABD9}
[2025.08.06-03.35.02:947][229]LogAnalytics: Display: [UEEditor.Perforce.Release] Destroying ET Analytics provider
[2025.08.06-03.35.02:947][229]LogAnalytics: Display: [UEEditor.Perforce.Release] Ended ET Analytics provider session
[2025.08.06-03.35.02:947][229]LogAnalytics: Display: [UEEditor.Perforce.Release] Destroyed ET Analytics provider
[2025.08.06-03.35.02:951][229]LogAudio: Display: Beginning Audio Device Manager Shutdown (Module: AudioMixerXAudio2)...
[2025.08.06-03.35.02:951][229]LogAudio: Display: Destroying 1 Remaining Audio Device(s)...
[2025.08.06-03.35.02:951][229]LogAudio: Display: Audio Device unregistered from world 'Untitled_1'.
[2025.08.06-03.35.02:951][229]LogAudio: Display: Shutting down audio device while 1 references to it are still alive. For more information, compile with INSTRUMENT_AUDIODEVICE_HANDLES.
[2025.08.06-03.35.02:954][229]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=1
[2025.08.06-03.35.02:959][229]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=1
[2025.08.06-03.35.02:979][229]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID -1
[2025.08.06-03.35.02:979][229]LogAudio: Display: Audio Device Manager Shutdown
[2025.08.06-03.35.02:980][229]LogWindowsTextInputMethodSystem: Activated input method: English (United States) - (Keyboard).
[2025.08.06-03.35.02:985][229]LogSlate: Slate User Destroyed.  User Index 0, Is Virtual User: 0
[2025.08.06-03.35.02:988][229]LogExit: Preparing to exit.
[2025.08.06-03.35.03:055][229]LogUObjectHash: Compacting FUObjectHashTables data took   0.72ms
[2025.08.06-03.35.03:896][229]LogEditorDataStorage: Deinitializing
[2025.08.06-03.35.04:117][229]LogDemo: Cleaned up 0 splitscreen connections, owner deletion: enabled
[2025.08.06-03.35.04:203][229]LogExit: Editor shut down
[2025.08.06-03.35.04:214][229]LogExit: Transaction tracking system shut down
[2025.08.06-03.35.04:818][229]LogExit: Object subsystem successfully closed.
[2025.08.06-03.35.04:838][229]LogShaderCompilers: Display: Shaders left to compile 0
[2025.08.06-03.35.04:911][229]LogShaderCompilers: Display: Shaders left to compile 0
[2025.08.06-03.35.05:411][229]LogMemoryProfiler: Shutdown
[2025.08.06-03.35.05:413][229]LogNetworkingProfiler: Shutdown
[2025.08.06-03.35.05:414][229]LoadingProfiler: Shutdown
[2025.08.06-03.35.05:415][229]LogTimingProfiler: Shutdown
[2025.08.06-03.35.05:996][229]LogChaosDD: Chaos Debug Draw Shutdown
[2025.08.06-03.35.06:009][229]LogStudioTelemetry: Display: Shutdown StudioTelemetry Module
[2025.08.06-03.35.06:009][229]LogNFORDenoise: NFORDenoise function shutting down
[2025.08.06-03.35.06:011][229]RenderDocPlugin: plugin has been unloaded.
[2025.08.06-03.35.06:019][229]LogHttp: Warning: [FHttpManager::Shutdown] Unbinding delegates for 1 outstanding Http Requests:
[2025.08.06-03.35.06:019][229]LogHttp: Warning: 	verb=[POST] url=[https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data?SessionID=%7B29A92622-4D83-2970-2D5B-7CB98E57ABD9%7D&AppID=UEEditor.Perforce.Release&AppVersion=5.5.1-0%2BUE5&UserID=0cf912ee4b52d61c1327948a4429657f%7Cbb5346b2832945159927080db1712c15%7C77aa645b-f6b2-4851-b85d-2c89bbf636d4&AppEnvironment=datacollector-binary&UploadType=eteventstream] refs=[2] status=Processing
[2025.08.06-03.35.08:080][229]LogEOSSDK: FEOSSDKManager::Shutdown EOS_Shutdown Result=[EOS_Success]
[2025.08.06-03.35.08:097][229]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetActorFactory id: 0
[2025.08.06-03.35.08:098][229]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetSubObjectFactory id: 1
[2025.08.06-03.35.08:098][229]LogPakFile: Destroying PakPlatformFile
[2025.08.06-03.35.08:479][229]LogD3D12RHI: ~FD3D12DynamicRHI
[2025.08.06-03.35.08:578][229]LogExit: Exiting.
[2025.08.06-03.35.08:618][229]Log file closed, 08/06/25 11:35:08
