# CPD动态创建和默认值应用

## 🎯 **新功能需求**

用户点击CPD窗口时：
1. **检查材质参数**: 扫描材质中有多少个CPD参数
2. **动态创建CPD**: 根据参数数量自动创建CPD元素
3. **使用默认值**: 应用材质球中设置的默认值
4. **刷新名字**: 如果有新参数，更新参数名称

## ✅ **完整实现方案**

### 1. 增强的刷新逻辑 - ✅ **已实现**

#### **新的RefreshCPDDisplay方法**
```cpp
void SCPDWindow::RefreshCPDDisplay()
{
    if (CurrentComponent.IsValid())
    {
        UPrimitiveComponent* Component = CurrentComponent.Get();
        
        // 1. 扫描材质参数，获取名称和默认值
        TMap<int32, FString> MaterialCPDParams;
        TMap<int32, float> MaterialDefaultValues;
        ScanMaterialParametersWithDefaults(Component, MaterialCPDParams, MaterialDefaultValues);
        
        // 2. 获取当前CPD数据
        const FCustomPrimitiveData& CurrentCPDData = Component->GetDefaultCustomPrimitiveData();
        int32 CurrentCPDSize = CurrentCPDData.Data.Num();
        
        // 3. 找到需要的最大CPD索引
        int32 MaxRequiredIndex = -1;
        for (const auto& Param : MaterialCPDParams)
        {
            MaxRequiredIndex = FMath::Max(MaxRequiredIndex, Param.Key);
        }
        
        // 4. 如果需要更多CPD元素，自动创建
        if (MaxRequiredIndex >= CurrentCPDSize)
        {
            for (int32 i = CurrentCPDSize; i <= MaxRequiredIndex; ++i)
            {
                // 使用材质默认值，如果没有则使用0.0f
                float DefaultValue = MaterialDefaultValues.Contains(i) ? MaterialDefaultValues[i] : 0.0f;
                Component->SetDefaultCustomPrimitiveDataFloat(i, DefaultValue);
            }
        }
        
        // 5. 更新参数名称
        CPDParameterNames = MaterialCPDParams;
        
        // 6. 刷新界面显示
        OnSelectionChanged(nullptr);
    }
}
```

### 2. 材质参数扫描与默认值获取 - ✅ **已实现**

#### **ScanMaterialParametersWithDefaults方法**
```cpp
void SCPDWindow::ScanMaterialParametersWithDefaults(
    UPrimitiveComponent* Component, 
    TMap<int32, FString>& OutParameterNames, 
    TMap<int32, float>& OutDefaultValues)
{
    // 扫描所有材质
    for (UMaterialInterface* Material : Materials)
    {
        // 标量参数处理
        TMap<FMaterialParameterInfo, FMaterialParameterMetadata> Parameters;
        Material->GetAllParametersOfType(EMaterialParameterType::Scalar, Parameters);
        
        for (const auto& Parameter : Parameters)
        {
            const FMaterialParameterMetadata& ParameterMetadata = Parameter.Value;
            if (ParameterMetadata.PrimitiveDataIndex > INDEX_NONE)
            {
                FString ParamName = Parameter.Key.Name.ToString();
                int32 CPDIndex = ParameterMetadata.PrimitiveDataIndex;
                
                // 存储参数名称
                OutParameterNames.Add(CPDIndex, ParamName);
                
                // 获取材质默认值
                float DefaultValue = 0.0f;
                if (Material->GetScalarParameterValue(Parameter.Key, DefaultValue))
                {
                    OutDefaultValues.Add(CPDIndex, DefaultValue);
                }
            }
        }
        
        // 向量参数处理
        Parameters.Reset();
        Material->GetAllParametersOfType(EMaterialParameterType::Vector, Parameters);
        
        for (const auto& Parameter : Parameters)
        {
            const FMaterialParameterMetadata& ParameterMetadata = Parameter.Value;
            if (ParameterMetadata.PrimitiveDataIndex > INDEX_NONE)
            {
                FString ParamName = Parameter.Key.Name.ToString();
                int32 BaseIndex = ParameterMetadata.PrimitiveDataIndex;
                
                // 获取向量默认值
                FLinearColor DefaultVector = FLinearColor::Black;
                Material->GetVectorParameterValue(Parameter.Key, DefaultVector);
                
                // 分别存储X、Y、Z、W分量
                OutParameterNames.Add(BaseIndex + 0, ParamName + TEXT(".X"));
                OutParameterNames.Add(BaseIndex + 1, ParamName + TEXT(".Y"));
                OutParameterNames.Add(BaseIndex + 2, ParamName + TEXT(".Z"));
                OutParameterNames.Add(BaseIndex + 3, ParamName + TEXT(".W"));
                
                OutDefaultValues.Add(BaseIndex + 0, DefaultVector.R);
                OutDefaultValues.Add(BaseIndex + 1, DefaultVector.G);
                OutDefaultValues.Add(BaseIndex + 2, DefaultVector.B);
                OutDefaultValues.Add(BaseIndex + 3, DefaultVector.A);
            }
        }
    }
}
```

## 🚀 **功能特性**

### **智能CPD创建**
- ✅ **自动检测**: 扫描材质中的CPD参数数量
- ✅ **按需创建**: 只创建材质需要的CPD元素
- ✅ **索引精确**: 使用材质中设置的精确CPD索引

### **默认值应用**
- ✅ **标量参数**: 使用`GetScalarParameterValue`获取默认值
- ✅ **向量参数**: 使用`GetVectorParameterValue`获取默认向量值
- ✅ **分量处理**: 向量参数自动分解为X、Y、Z、W分量
- ✅ **安全回退**: 如果无法获取默认值，使用0.0f

### **参数名称更新**
- ✅ **实时更新**: 每次点击都重新扫描参数名称
- ✅ **精确映射**: 使用`PrimitiveDataIndex`精确映射
- ✅ **向量支持**: 向量参数显示为"ParamName.X"格式

## 📋 **使用流程**

### **材质设置流程**
1. **创建材质**: 在材质编辑器中创建材质
2. **添加CPD节点**: 添加Custom Primitive Data节点
3. **设置参数**: 
   - 设置参数名称（如"Metallic"、"Roughness"）
   - 设置默认值（如0.5、1.0等）
   - 设置CPD索引（0、1、2...）
4. **应用材质**: 将材质应用到对象

### **CPD窗口使用流程**
1. **选择对象**: 选择应用了CPD材质的对象
2. **打开CPD窗口**: 菜单 -> VTS Tools -> CPD
3. **点击刷新**: 点击CPD窗口任意位置
4. **自动创建**: 
   - 窗口自动检测材质中的CPD参数
   - 自动创建对应数量的CPD元素
   - 应用材质中设置的默认值
   - 显示正确的参数名称

## 🎯 **预期效果**

### **场景1: 新建CPD**
```
材质设置:
- Metallic (CPD[0]) = 0.5
- Roughness (CPD[1]) = 0.8
- Emissive (CPD[2]) = 0.0

点击CPD窗口后:
0    Metallic                           0.500000
1    Roughness                          0.800000
2    Emissive                           0.000000
```

### **场景2: 向量参数**
```
材质设置:
- BaseColor (CPD[0-3]) = (1.0, 0.5, 0.2, 1.0)

点击CPD窗口后:
0    BaseColor.X                        1.000000
1    BaseColor.Y                        0.500000
2    BaseColor.Z                        0.200000
3    BaseColor.W                        1.000000
```

### **场景3: 混合参数**
```
材质设置:
- Metallic (CPD[0]) = 0.5
- BaseColor (CPD[1-4]) = (1.0, 0.5, 0.2, 1.0)
- Roughness (CPD[5]) = 0.8

点击CPD窗口后:
0    Metallic                           0.500000
1    BaseColor.X                        1.000000
2    BaseColor.Y                        0.500000
3    BaseColor.Z                        0.200000
4    BaseColor.W                        1.000000
5    Roughness                          0.800000
```

## 🚀 **测试验证**

### **编译测试**
```bash
Build -> Rebuild Solution
```

### **功能测试**
1. **创建测试材质**: 
   - 添加Custom Primitive Data节点
   - 设置参数名称和默认值
   - 应用到对象

2. **测试自动创建**:
   - 选择对象，打开CPD窗口
   - 点击窗口，观察是否自动创建CPD元素
   - 检查是否使用了材质的默认值

3. **测试参数名称**:
   - 检查是否显示材质中设置的参数名称
   - 测试向量参数的X、Y、Z、W分量显示

## 状态: ✅ 动态CPD创建和默认值应用完成

现在CPD窗口具有智能的材质参数检测和自动CPD创建功能！🎉
