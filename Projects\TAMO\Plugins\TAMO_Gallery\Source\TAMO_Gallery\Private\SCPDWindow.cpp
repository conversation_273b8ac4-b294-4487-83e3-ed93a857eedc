#include "SCPDWindow.h"
#include "Engine/Selection.h"
#include "Editor.h"
#include "Components/PrimitiveComponent.h"
#include "Widgets/Text/STextBlock.h"
#include "Widgets/Input/SSpinBox.h"
#include "Widgets/Input/SButton.h"
#include "Widgets/SBoxPanel.h"
#include "Widgets/Layout/SBorder.h"
#include "Widgets/Layout/SScrollBox.h"

#define LOCTEXT_NAMESPACE "SCPDWindow"

void SCPDWindow::Construct(const FArguments& InArgs)
{
	// Initialize material slot
	CurrentMaterialSlot = 0;
	// Register for selection changes
	if (GEditor)
	{
		SelectionChangedHandle = USelection::SelectionChangedEvent.AddRaw(this, &SCPDWindow::OnSelectionChanged);
	}

	// No longer using periodic timer - refresh on window focus/click instead

	ChildSlot
	[
		SAssignNew(MainContentBox, SVerticalBox)
		+ SVerticalBox::Slot()
		.AutoHeight()
		.Padding(5.0f)
		[
			SNew(STextBlock)
			.Text(LOCTEXT("CPDWindowTitle", "CPD Manager"))
			.Font(FCoreStyle::GetDefaultFontStyle("Bold", 12))
		]

		+ SVerticalBox::Slot()
		.FillHeight(1.0f)
		.Padding(5.0f)
		[
			SNew(SScrollBox)
			+ SScrollBox::Slot()
			[
				SAssignNew(CPDDisplayBox, SVerticalBox)
				+ SVerticalBox::Slot()
				.AutoHeight()
				[
					SNew(STextBlock)
					.Text(LOCTEXT("NoSelection", "Select an object to view its Custom Primitive Data"))
				]
			]
		]
	];

	// Initial selection update
	OnSelectionChanged(nullptr);
}

SCPDWindow::~SCPDWindow()
{
	// Unregister selection change delegate
	if (SelectionChangedHandle.IsValid() && USelection::SelectionChangedEvent.IsBound())
	{
		USelection::SelectionChangedEvent.Remove(SelectionChangedHandle);
	}

	// No timer to clear anymore
}

void SCPDWindow::RefreshCPDData()
{
	UpdateCPDDisplay();
}

void SCPDWindow::UpdateCPDDisplay()
{
	if (!CPDDisplayBox.IsValid())
	{
		return;
	}

	// Clear existing content
	CPDDisplayBox->ClearChildren();

	if (SelectedComponents.Num() == 0)
	{
		// No selection
		CPDDisplayBox->AddSlot()
		.AutoHeight()
		.Padding(5.0f)
		[
			SNew(STextBlock)
			.Text(LOCTEXT("NoSelection", "Select an object to view its Custom Primitive Data"))
		];
		return;
	}

	// Get the first selected component
	UPrimitiveComponent* FirstComponent = SelectedComponents[0].Get();
	if (!FirstComponent)
	{
		CPDDisplayBox->AddSlot()
		.AutoHeight()
		.Padding(5.0f)
		[
			SNew(STextBlock)
			.Text(LOCTEXT("InvalidSelection", "Selected object has no valid primitive component"))
		];
		return;
	}

	// Display component info
	CPDDisplayBox->AddSlot()
	.AutoHeight()
	.Padding(5.0f)
	[
		SNew(STextBlock)
		.Text(FText::FromString(FString::Printf(TEXT("Component: %s"), *FirstComponent->GetName())))
		.Font(FCoreStyle::GetDefaultFontStyle("Bold", 10))
	];

	// Try to access Custom Primitive Data
	DisplayCustomPrimitiveData(FirstComponent);
}

void SCPDWindow::DisplayCustomPrimitiveData(UPrimitiveComponent* Component)
{
	if (!Component || !CPDDisplayBox.IsValid())
	{
		return;
	}

	// Add separator with click hint
	CPDDisplayBox->AddSlot()
	.AutoHeight()
	.Padding(5.0f, 2.0f)
	[
		SNew(STextBlock)
		.Text(LOCTEXT("CPDSeparator", "Custom Primitive Data Defaults (Click to refresh)"))
		.Font(FCoreStyle::GetDefaultFontStyle("Bold", 10))
		.ColorAndOpacity(FLinearColor(0.8f, 0.8f, 0.8f, 1.0f))
	];

	// For multi-selection, find the common CPD count across all selected components
	TArray<float> CPDData;
	bool bFoundCPDData = false;
	int32 CommonCPDCount = 0;

	if (SelectedComponents.Num() > 1)
	{
		// Multi-selection: find the minimum CPD count across all components
		CommonCPDCount = INT32_MAX;
		for (const TWeakObjectPtr<UPrimitiveComponent>& WeakComponent : SelectedComponents)
		{
			if (WeakComponent.IsValid())
			{
				UPrimitiveComponent* Comp = WeakComponent.Get();
				const FCustomPrimitiveData& CompCPDData = Comp->GetDefaultCustomPrimitiveData();
				CommonCPDCount = FMath::Min(CommonCPDCount, CompCPDData.Data.Num());
			}
		}

		if (CommonCPDCount == INT32_MAX || CommonCPDCount == 0)
		{
			CommonCPDCount = 0;
		}

		// Use the common count
		CPDData.SetNum(CommonCPDCount);
		for (int32 i = 0; i < CommonCPDCount; ++i)
		{
			CPDData[i] = 0.0f; // Will be handled by GetMultiObjectCPDValue
		}
		bFoundCPDData = CommonCPDCount > 0;

		CPDDisplayBox->AddSlot()
		.AutoHeight()
		.Padding(5.0f)
		[
			SNew(STextBlock)
			.Text(FText::FromString(FString::Printf(TEXT("Multi-selection: %d objects, %d common CPD elements"),
				SelectedComponents.Num(), CommonCPDCount)))
			.ColorAndOpacity(FLinearColor::Blue)
		];
	}
	else
	{
		// Single selection: use the component's CPD data
		const FCustomPrimitiveData& ComponentCPDData = Component->GetDefaultCustomPrimitiveData();
		if (ComponentCPDData.Data.Num() > 0)
		{
			CPDData = ComponentCPDData.Data;
			bFoundCPDData = true;

			CPDDisplayBox->AddSlot()
			.AutoHeight()
			.Padding(5.0f)
			[
				SNew(STextBlock)
				.Text(FText::FromString(FString::Printf(TEXT("Found CPD Data Array with %d elements"), CPDData.Num())))
				.ColorAndOpacity(FLinearColor::Green)
			];
		}
		else
		{
			// No CPD data found, create default array
			CPDDisplayBox->AddSlot()
			.AutoHeight()
			.Padding(5.0f)
			[
				SNew(STextBlock)
				.Text(LOCTEXT("CPDNotFound", "No CPD data found, showing default layout"))
				.ColorAndOpacity(FLinearColor::Yellow)
			];

			// Create default CPD array (UE typically uses up to 32 elements)
			CPDData.SetNum(8); // Start with 8 elements like most examples
			for (int32 i = 0; i < CPDData.Num(); ++i)
			{
				CPDData[i] = 0.0f;
			}
		}
	}

	// Display the CPD interface similar to Details panel
	DisplayCPDInterface(Component, CPDData, bFoundCPDData);
}

void SCPDWindow::DisplayCPDInterface(UPrimitiveComponent* Component, const TArray<float>& CPDData, bool bDataFound)
{
	if (!CPDDisplayBox.IsValid())
	{
		return;
	}

	// Store current component for later use
	CurrentComponent = Component;

	// Scan materials for CPD parameters to get real parameter names
	ScanMaterialParameters(Component);

	// Display header similar to Details panel with proper theming
	CPDDisplayBox->AddSlot()
	.AutoHeight()
	.Padding(0.0f, 2.0f)
	[
		SNew(SBorder)
		.BorderImage(FAppStyle::GetBrush("DetailsView.CategoryTop"))
		.BorderBackgroundColor(FAppStyle::GetSlateColor("Colors.Header"))
		.Padding(FMargin(6.0f, 4.0f))
		[
			SNew(SHorizontalBox)
			+ SHorizontalBox::Slot()
			.FillWidth(1.0f)
			.VAlign(VAlign_Center)
			[
				SNew(STextBlock)
				.Text(FText::FromString(FString::Printf(TEXT("%d Array element%s"),
					CPDData.Num(),
					CPDData.Num() == 1 ? TEXT("") : TEXT("s"))))
				.Font(FAppStyle::GetFontStyle("PropertyWindow.NormalFont"))
				.ColorAndOpacity(FAppStyle::GetSlateColor("Colors.ForegroundHover"))
			]
			+ SHorizontalBox::Slot()
			.AutoWidth()
			.VAlign(VAlign_Center)
			.Padding(8.0f, 0.0f, 0.0f, 0.0f)
			[
				SNew(SButton)
				.ButtonStyle(FAppStyle::Get(), "SimpleButton")
				.ToolTipText(LOCTEXT("AddElementTooltip", "Add new element"))
				.OnClicked(this, &SCPDWindow::OnAddCPDElement)
				.ContentPadding(FMargin(3.0f))
				.Content()
				[
					SNew(SImage)
					.Image(FAppStyle::GetBrush("Icons.Plus"))
					.ColorAndOpacity(FAppStyle::GetSlateColor("Colors.AccentGreen"))
				]
			]
			+ SHorizontalBox::Slot()
			.AutoWidth()
			.VAlign(VAlign_Center)
			.Padding(4.0f, 0.0f, 0.0f, 0.0f)
			[
				SNew(SButton)
				.ButtonStyle(FAppStyle::Get(), "SimpleButton")
				.ToolTipText(LOCTEXT("RemoveElementTooltip", "Remove last element"))
				.OnClicked(this, &SCPDWindow::OnRemoveCPDElement)
				.ContentPadding(FMargin(3.0f))
				.Content()
				[
					SNew(SImage)
					.Image(FAppStyle::GetBrush("Icons.Delete"))
					.ColorAndOpacity(FAppStyle::GetSlateColor("Colors.AccentRed"))
				]
			]
		]
	];

	// Display each CPD element similar to Details panel
	// Based on UE source: CreateParameterRow method
	for (int32 i = 0; i < CPDData.Num(); ++i)
	{
		CreateCPDElementRow(i, CPDData[i], bDataFound);
	}

	// Show material parameter info if available
	if (bDataFound)
	{
		DisplayMaterialParameterInfo(Component);
	}
	else
	{
		CPDDisplayBox->AddSlot()
		.AutoHeight()
		.Padding(5.0f)
		[
			SNew(STextBlock)
			.Text(LOCTEXT("CPDDefaultNote", "Note: Using default values. Set CPD values in Details panel to see actual data."))
			.ColorAndOpacity(FLinearColor::Gray)
		];
	}
}

void SCPDWindow::DisplayMaterialParameterInfo(UPrimitiveComponent* Component)
{
	if (!Component || !CPDDisplayBox.IsValid())
	{
		return;
	}

	// Based on UE source: PopulateParameterData method (line 1000+)
	// This scans materials for Custom Primitive Data parameters

	CPDDisplayBox->AddSlot()
	.AutoHeight()
	.Padding(5.0f, 10.0f, 5.0f, 5.0f)
	[
		SNew(STextBlock)
		.Text(LOCTEXT("MaterialParamsHeader", "Material Parameters:"))
		.Font(FCoreStyle::GetDefaultFontStyle("Bold", 9))
	];

	// Try to get materials from the component
	TArray<UMaterialInterface*> Materials;

	// Get materials based on component type
	if (UStaticMeshComponent* StaticMeshComp = Cast<UStaticMeshComponent>(Component))
	{
		for (int32 i = 0; i < StaticMeshComp->GetNumMaterials(); ++i)
		{
			if (UMaterialInterface* Material = StaticMeshComp->GetMaterial(i))
			{
				Materials.Add(Material);
			}
		}
	}
	else if (USkeletalMeshComponent* SkeletalMeshComp = Cast<USkeletalMeshComponent>(Component))
	{
		for (int32 i = 0; i < SkeletalMeshComp->GetNumMaterials(); ++i)
		{
			if (UMaterialInterface* Material = SkeletalMeshComp->GetMaterial(i))
			{
				Materials.Add(Material);
			}
		}
	}

	if (Materials.Num() == 0)
	{
		CPDDisplayBox->AddSlot()
		.AutoHeight()
		.Padding(10.0f, 2.0f)
		[
			SNew(STextBlock)
			.Text(LOCTEXT("NoMaterials", "No materials found"))
			.ColorAndOpacity(FLinearColor::Gray)
		];
		return;
	}

	// Display material info
	for (int32 MatIndex = 0; MatIndex < Materials.Num(); ++MatIndex)
	{
		UMaterialInterface* Material = Materials[MatIndex];
		if (Material)
		{
			CPDDisplayBox->AddSlot()
			.AutoHeight()
			.Padding(10.0f, 2.0f)
			[
				SNew(STextBlock)
				.Text(FText::FromString(FString::Printf(TEXT("Material [%d]: %s"), MatIndex, *Material->GetName())))
				.ColorAndOpacity(FLinearColor(0.0f, 1.0f, 1.0f, 1.0f)) // Cyan color
			];
		}
	}
}

void SCPDWindow::ScanMaterialParameters(UPrimitiveComponent* Component)
{
	// Clear previous parameter names
	CPDParameterNames.Empty();

	if (!Component)
	{
		return;
	}

	// Based on UE source: PopulateParameterData method (line 1000+)
	// Scan materials for Custom Primitive Data parameters
	TArray<UMaterialInterface*> Materials;

	// Get materials based on component type
	if (UStaticMeshComponent* StaticMeshComp = Cast<UStaticMeshComponent>(Component))
	{
		for (int32 i = 0; i < StaticMeshComp->GetNumMaterials(); ++i)
		{
			if (UMaterialInterface* Material = StaticMeshComp->GetMaterial(i))
			{
				Materials.Add(Material);
			}
		}
	}
	else if (USkeletalMeshComponent* SkeletalMeshComp = Cast<USkeletalMeshComponent>(Component))
	{
		for (int32 i = 0; i < SkeletalMeshComp->GetNumMaterials(); ++i)
		{
			if (UMaterialInterface* Material = SkeletalMeshComp->GetMaterial(i))
			{
				Materials.Add(Material);
			}
		}
	}

	// Scan each material for CPD parameters using UE's official method
	// Based on CustomPrimitiveDataCustomization.cpp PopulateParameterData method
	for (UMaterialInterface* Material : Materials)
	{
		if (Material)
		{
			// Use the same method as UE's Details panel
			TMap<FMaterialParameterInfo, FMaterialParameterMetadata> Parameters;

			// Get all scalar parameters with metadata
			Material->GetAllParametersOfType(EMaterialParameterType::Scalar, Parameters);

			for (const TPair<FMaterialParameterInfo, FMaterialParameterMetadata>& Parameter : Parameters)
			{
				const FMaterialParameterInfo& Info = Parameter.Key;
				const FMaterialParameterMetadata& ParameterMetadata = Parameter.Value;

				// Check if this parameter has a valid PrimitiveDataIndex (like UE source)
				if (ParameterMetadata.PrimitiveDataIndex > INDEX_NONE)
				{
					FString ParamName = Info.Name.ToString();
					CPDParameterNames.Add(ParameterMetadata.PrimitiveDataIndex, ParamName);
				}
			}

			// Also get vector parameters
			Parameters.Reset();
			Material->GetAllParametersOfType(EMaterialParameterType::Vector, Parameters);

			for (const TPair<FMaterialParameterInfo, FMaterialParameterMetadata>& Parameter : Parameters)
			{
				const FMaterialParameterInfo& Info = Parameter.Key;
				const FMaterialParameterMetadata& ParameterMetadata = Parameter.Value;

				// Vector parameters can use multiple CPD indices (like UE source)
				if (ParameterMetadata.PrimitiveDataIndex > INDEX_NONE)
				{
					FString ParamName = Info.Name.ToString();

					// Add each component of the vector parameter
					CPDParameterNames.Add(ParameterMetadata.PrimitiveDataIndex + 0, ParamName + TEXT(".X"));
					CPDParameterNames.Add(ParameterMetadata.PrimitiveDataIndex + 1, ParamName + TEXT(".Y"));
					CPDParameterNames.Add(ParameterMetadata.PrimitiveDataIndex + 2, ParamName + TEXT(".Z"));
					CPDParameterNames.Add(ParameterMetadata.PrimitiveDataIndex + 3, ParamName + TEXT(".W"));
				}
			}
		}
	}

	// If no material parameters found, use simple default names
	// (Now we have real material parameter scanning, so this is just fallback)
	if (CPDParameterNames.Num() == 0)
	{
		// Simple fallback names
		for (int32 i = 0; i < 8; ++i)
		{
			CPDParameterNames.Add(i, FString::Printf(TEXT("Element_%d"), i));
		}
	}
}

void SCPDWindow::ScanMaterialParametersWithDefaults(UPrimitiveComponent* Component, TMap<int32, FString>& OutParameterNames, TMap<int32, float>& OutDefaultValues)
{
	// Clear output maps
	OutParameterNames.Empty();
	OutDefaultValues.Empty();

	if (!Component)
	{
		return;
	}

	// Get materials based on component type
	TArray<UMaterialInterface*> Materials;
	if (UStaticMeshComponent* StaticMeshComp = Cast<UStaticMeshComponent>(Component))
	{
		for (int32 i = 0; i < StaticMeshComp->GetNumMaterials(); ++i)
		{
			if (UMaterialInterface* Material = StaticMeshComp->GetMaterial(i))
			{
				Materials.Add(Material);
			}
		}
	}
	else if (USkeletalMeshComponent* SkeletalMeshComp = Cast<USkeletalMeshComponent>(Component))
	{
		for (int32 i = 0; i < SkeletalMeshComp->GetNumMaterials(); ++i)
		{
			if (UMaterialInterface* Material = SkeletalMeshComp->GetMaterial(i))
			{
				Materials.Add(Material);
			}
		}
	}

	// Scan each material for CPD parameters with default values
	for (UMaterialInterface* Material : Materials)
	{
		if (Material)
		{
			// Use the same method as UE's Details panel
			TMap<FMaterialParameterInfo, FMaterialParameterMetadata> Parameters;

			// Get all scalar parameters with metadata
			Material->GetAllParametersOfType(EMaterialParameterType::Scalar, Parameters);

			for (const TPair<FMaterialParameterInfo, FMaterialParameterMetadata>& Parameter : Parameters)
			{
				const FMaterialParameterInfo& Info = Parameter.Key;
				const FMaterialParameterMetadata& ParameterMetadata = Parameter.Value;

				// Check if this parameter has a valid PrimitiveDataIndex
				if (ParameterMetadata.PrimitiveDataIndex > INDEX_NONE)
				{
					FString ParamName = Info.Name.ToString();
					int32 CPDIndex = ParameterMetadata.PrimitiveDataIndex;

					// Store parameter name
					OutParameterNames.Add(CPDIndex, ParamName);

					// Get default value from material
					float DefaultValue = 0.0f;
					if (Material->GetScalarParameterValue(Info, DefaultValue))
					{
						OutDefaultValues.Add(CPDIndex, DefaultValue);
					}
					else
					{
						OutDefaultValues.Add(CPDIndex, 0.0f);
					}
				}
			}

			// Also get vector parameters
			Parameters.Reset();
			Material->GetAllParametersOfType(EMaterialParameterType::Vector, Parameters);

			for (const TPair<FMaterialParameterInfo, FMaterialParameterMetadata>& Parameter : Parameters)
			{
				const FMaterialParameterInfo& Info = Parameter.Key;
				const FMaterialParameterMetadata& ParameterMetadata = Parameter.Value;

				// Vector parameters can use multiple CPD indices
				if (ParameterMetadata.PrimitiveDataIndex > INDEX_NONE)
				{
					FString ParamName = Info.Name.ToString();
					int32 BaseIndex = ParameterMetadata.PrimitiveDataIndex;

					// Get default vector value from material
					FLinearColor DefaultVector = FLinearColor::Black;
					Material->GetVectorParameterValue(Info, DefaultVector);

					// Add each component of the vector parameter
					OutParameterNames.Add(BaseIndex + 0, ParamName + TEXT(".X"));
					OutParameterNames.Add(BaseIndex + 1, ParamName + TEXT(".Y"));
					OutParameterNames.Add(BaseIndex + 2, ParamName + TEXT(".Z"));
					OutParameterNames.Add(BaseIndex + 3, ParamName + TEXT(".W"));

					OutDefaultValues.Add(BaseIndex + 0, DefaultVector.R);
					OutDefaultValues.Add(BaseIndex + 1, DefaultVector.G);
					OutDefaultValues.Add(BaseIndex + 2, DefaultVector.B);
					OutDefaultValues.Add(BaseIndex + 3, DefaultVector.A);
				}
			}
		}
	}
}

void SCPDWindow::ShowNoCPDParametersMessage()
{
	// Clear the display box
	if (CPDDisplayBox.IsValid())
	{
		CPDDisplayBox->ClearChildren();
	}

	// Add warning message when no CPD parameters are found
	CPDDisplayBox->AddSlot()
	.AutoHeight()
	.Padding(10.0f, 20.0f)
	[
		SNew(SBorder)
		.BorderImage(FAppStyle::GetBrush("ToolPanel.GroupBorder"))
		.BorderBackgroundColor(FLinearColor(1.0f, 0.8f, 0.0f, 0.3f)) // Light orange background
		.Padding(FMargin(15.0f, 10.0f))
		[
			SNew(SVerticalBox)
			+ SVerticalBox::Slot()
			.AutoHeight()
			.Padding(0.0f, 0.0f, 0.0f, 5.0f)
			[
				SNew(STextBlock)
				.Text(FText::FromString(TEXT("No CPD Parameters Found")))
				.Font(FAppStyle::GetFontStyle("PropertyWindow.BoldFont"))
				.ColorAndOpacity(FLinearColor(0.8f, 0.4f, 0.0f, 1.0f)) // Orange text
			]
			+ SVerticalBox::Slot()
			.AutoHeight()
			[
				SNew(STextBlock)
				.Text(FText::FromString(TEXT("The selected object's materials do not contain any Custom Primitive Data parameters.\n\nTo use CPD:\n1. Open the Material Editor\n2. Add a 'Custom Primitive Data' node\n3. Set the parameter name and index\n4. Connect it to your material properties")))
				.Font(FAppStyle::GetFontStyle("PropertyWindow.NormalFont"))
				.ColorAndOpacity(FLinearColor(0.6f, 0.6f, 0.6f, 1.0f))
				.AutoWrapText(true)
			]
		]
	];
}

TSharedRef<SWidget> SCPDWindow::CreateMaterialSlotSelector()
{
	return SNew(SVerticalBox)
		+ SVerticalBox::Slot()
		.AutoHeight()
		.Padding(0.0f, 0.0f, 0.0f, 5.0f)
		[
			SNew(STextBlock)
			.Text(LOCTEXT("MaterialSlotLabel", "Material Slots:"))
			.Font(FAppStyle::GetFontStyle("PropertyWindow.BoldFont"))
		]
		+ SVerticalBox::Slot()
		.AutoHeight()
		[
			SAssignNew(MaterialSlotsBox, SVerticalBox)
		];
}

void SCPDWindow::OnMaterialSlotChanged(TSharedPtr<FString> NewSelection, ESelectInfo::Type SelectInfo)
{
	if (NewSelection.IsValid())
	{
		// Find the index of the selected option
		for (int32 i = 0; i < MaterialSlotOptions.Num(); ++i)
		{
			if (MaterialSlotOptions[i] == NewSelection)
			{
				CurrentMaterialSlot = i;
				break;
			}
		}

		// Refresh display with new material slot
		RefreshCPDDisplay();
	}
}

void SCPDWindow::CreateMaterialSlotsDisplay()
{
	if (!MaterialSlotsBox.IsValid() || !CurrentComponent.IsValid())
	{
		return;
	}

	MaterialSlotsBox->ClearChildren();

	UPrimitiveComponent* Component = CurrentComponent.Get();
	int32 NumMaterials = Component->GetNumMaterials();

	for (int32 i = 0; i < NumMaterials; ++i)
	{
		MaterialSlotsBox->AddSlot()
		.AutoHeight()
		.Padding(0.0f, 2.0f)
		[
			CreateMaterialSlotWidget(i)
		];
	}
}

TSharedRef<SWidget> SCPDWindow::CreateMaterialSlotWidget(int32 SlotIndex)
{
	return SNew(SHorizontalBox)
		+ SHorizontalBox::Slot()
		.AutoWidth()
		.VAlign(VAlign_Center)
		.Padding(0.0f, 0.0f, 8.0f, 0.0f)
		[
			SNew(STextBlock)
			.Text(FText::FromString(FString::Printf(TEXT("Slot %d:"), SlotIndex)))
			.Font(FAppStyle::GetFontStyle("PropertyWindow.NormalFont"))
		]
		+ SHorizontalBox::Slot()
		.FillWidth(1.0f)
		.VAlign(VAlign_Center)
		[
			SNew(SBorder)
			.BorderImage(FAppStyle::GetBrush("EditableTextBox.Background"))
			.BorderBackgroundColor(FLinearColor(0.2f, 0.2f, 0.2f, 1.0f)) // 统一灰色背景
			.Padding(FMargin(8.0f, 4.0f))
			.OnMouseButtonDown_Lambda([this, SlotIndex](const FGeometry& Geometry, const FPointerEvent& MouseEvent)
			{
				// Select this material slot
				CurrentMaterialSlot = SlotIndex;
				RefreshCPDDisplay();
				return FReply::Handled();
			})
			[
				SNew(STextBlock)
				.Text_Lambda([this, SlotIndex]()
				{
					UMaterialInterface* Material = GetMaterialForSlot(SlotIndex);
					FString MaterialName = Material ? Material->GetName() : TEXT("None");
					FString SlotText = (SlotIndex == CurrentMaterialSlot) ?
						FString::Printf(TEXT("► %s"), *MaterialName) :  // 当前选择显示箭头
						MaterialName;
					return FText::FromString(SlotText);
				})
				.Font(FAppStyle::GetFontStyle("PropertyWindow.NormalFont"))
				.ColorAndOpacity(FLinearColor(0.9f, 0.9f, 0.9f, 1.0f)) // 统一白色文字
			]
		];
}

UMaterialInterface* SCPDWindow::GetMaterialForSlot(int32 SlotIndex) const
{
	if (CurrentComponent.IsValid())
	{
		UPrimitiveComponent* Component = CurrentComponent.Get();
		if (SlotIndex < Component->GetNumMaterials())
		{
			return Component->GetMaterial(SlotIndex);
		}
	}
	return nullptr;
}

void SCPDWindow::OnMaterialSelected(const FAssetData& AssetData, int32 SlotIndex)
{
	// This would be implemented for full material selection from Content Browser
	// For now, we'll focus on the display and selection functionality
}

FReply SCPDWindow::OnCPDParameterRightClick(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent, int32 Index)
{
	if (MouseEvent.GetEffectingButton() == EKeys::RightMouseButton)
	{
		// 切换高亮状态
		if (HighlightedCPDParameters.Contains(Index))
		{
			// 当前已高亮，移除高亮
			HighlightedCPDParameters.Remove(Index);
			UE_LOG(LogTemp, Warning, TEXT("Removed highlight from CPD Parameter [%d]: %s"), Index, *GetCPDParameterName(Index).ToString());
		}
		else
		{
			// 当前未高亮，添加高亮
			HighlightedCPDParameters.Add(Index);
			UE_LOG(LogTemp, Warning, TEXT("Added highlight to CPD Parameter [%d]: %s"), Index, *GetCPDParameterName(Index).ToString());
		}

		return FReply::Handled();
	}

	return FReply::Unhandled();
}

void SCPDWindow::OnHighlightCPDParameter(int32 Index)
{
	// 切换高亮状态（这个方法现在主要用于其他地方调用）
	if (HighlightedCPDParameters.Contains(Index))
	{
		HighlightedCPDParameters.Remove(Index);
	}
	else
	{
		HighlightedCPDParameters.Add(Index);
	}

	FText ParameterName = GetCPDParameterName(Index);
	bool bIsHighlighted = HighlightedCPDParameters.Contains(Index);
	UE_LOG(LogTemp, Warning, TEXT("CPD Parameter [%d] %s: %s"), Index,
		bIsHighlighted ? TEXT("highlighted") : TEXT("unhighlighted"),
		*ParameterName.ToString());
}

FReply SCPDWindow::OnResetCPDToDefault(int32 Index)
{
	if (CPDDefaultValues.Contains(Index))
	{
		float DefaultValue = CPDDefaultValues[Index];
		SetCPDValue(Index, DefaultValue);

		FText ParameterName = GetCPDParameterName(Index);
		UE_LOG(LogTemp, Warning, TEXT("Reset CPD Parameter [%d] %s to default value: %.3f"),
			Index, *ParameterName.ToString(), DefaultValue);
	}

	return FReply::Handled();
}

void SCPDWindow::GetMaterialSlotOptions(TArray<TSharedPtr<FString>>& OutOptions)
{
	OutOptions.Empty();

	if (CurrentComponent.IsValid())
	{
		UPrimitiveComponent* Component = CurrentComponent.Get();
		int32 NumMaterials = Component->GetNumMaterials();

		for (int32 i = 0; i < NumMaterials; ++i)
		{
			UMaterialInterface* Material = Component->GetMaterial(i);
			FString MaterialName = Material ? Material->GetName() : TEXT("None");
			OutOptions.Add(MakeShareable(new FString(FString::Printf(TEXT("Slot %d: %s"), i, *MaterialName))));
		}

		if (OutOptions.Num() == 0)
		{
			OutOptions.Add(MakeShareable(new FString(TEXT("No Materials"))));
		}
	}
}

bool SCPDWindow::HasDifferentMaterials() const
{
	if (SelectedComponents.Num() <= 1)
	{
		return false; // Single or no selection
	}

	// Get the reference material from the first component
	UMaterialInterface* ReferenceMaterial = nullptr;
	if (SelectedComponents[0].IsValid())
	{
		UPrimitiveComponent* FirstComponent = SelectedComponents[0].Get();
		if (FirstComponent && CurrentMaterialSlot < FirstComponent->GetNumMaterials())
		{
			ReferenceMaterial = FirstComponent->GetMaterial(CurrentMaterialSlot);
		}
	}

	// Compare with other components
	for (int32 i = 1; i < SelectedComponents.Num(); ++i)
	{
		if (SelectedComponents[i].IsValid())
		{
			UPrimitiveComponent* Component = SelectedComponents[i].Get();
			if (Component)
			{
				UMaterialInterface* ComponentMaterial = nullptr;
				if (CurrentMaterialSlot < Component->GetNumMaterials())
				{
					ComponentMaterial = Component->GetMaterial(CurrentMaterialSlot);
				}

				if (ComponentMaterial != ReferenceMaterial)
				{
					return true; // Found different material
				}
			}
		}
	}

	return false; // All materials are the same
}

void SCPDWindow::ShowMultipleMaterialsMessage()
{
	// Clear the display box
	if (CPDDisplayBox.IsValid())
	{
		CPDDisplayBox->ClearChildren();
	}

	// Add warning message for multiple different materials
	CPDDisplayBox->AddSlot()
	.AutoHeight()
	.Padding(10.0f, 20.0f)
	[
		SNew(SBorder)
		.BorderImage(FAppStyle::GetBrush("ToolPanel.GroupBorder"))
		.BorderBackgroundColor(FLinearColor(1.0f, 0.6f, 0.0f, 0.3f)) // Orange background
		.Padding(FMargin(15.0f, 10.0f))
		[
			SNew(SVerticalBox)
			+ SVerticalBox::Slot()
			.AutoHeight()
			.Padding(0.0f, 0.0f, 0.0f, 5.0f)
			[
				SNew(STextBlock)
				.Text(FText::FromString(TEXT("Multiple Different Materials")))
				.Font(FAppStyle::GetFontStyle("PropertyWindow.BoldFont"))
				.ColorAndOpacity(FLinearColor(0.8f, 0.3f, 0.0f, 1.0f)) // Dark orange text
			]
			+ SVerticalBox::Slot()
			.AutoHeight()
			[
				SNew(STextBlock)
				.Text(FText::FromString(TEXT("Selected objects have different materials in the current slot.\n\nCannot display CPD parameters. Please:\n1. Select objects with the same material, or\n2. Switch to a different material slot, or\n3. Select only one object")))
				.Font(FAppStyle::GetFontStyle("PropertyWindow.NormalFont"))
				.ColorAndOpacity(FLinearColor(0.6f, 0.6f, 0.6f, 1.0f))
				.AutoWrapText(true)
			]
		]
	];
}

FString SCPDWindow::GetMultiObjectCPDValue(int32 Index) const
{
	if (SelectedComponents.Num() <= 1)
	{
		// Single object, use normal value
		return FString::Printf(TEXT("%.1f"), GetCPDValue(Index));
	}

	// Check if all objects have the same CPD value
	float ReferenceValue = 0.0f;
	bool bHasReferenceValue = false;
	int32 ValidComponentsCount = 0;

	for (const TWeakObjectPtr<UPrimitiveComponent>& WeakComponent : SelectedComponents)
	{
		if (WeakComponent.IsValid())
		{
			UPrimitiveComponent* Component = WeakComponent.Get();
			const FCustomPrimitiveData& CPDData = Component->GetDefaultCustomPrimitiveData();

			if (CPDData.Data.IsValidIndex(Index))
			{
				ValidComponentsCount++;
				float CurrentValue = CPDData.Data[Index];

				if (!bHasReferenceValue)
				{
					ReferenceValue = CurrentValue;
					bHasReferenceValue = true;
				}
				else if (!FMath::IsNearlyEqual(ReferenceValue, CurrentValue, 0.001f))
				{
					// Debug log
					UE_LOG(LogTemp, Warning, TEXT("CPD[%d]: Different values found - Reference: %.3f, Current: %.3f"),
						Index, ReferenceValue, CurrentValue);
					return TEXT("Multiple Values"); // Different values found
				}
			}
		}
	}

	// Debug log
	UE_LOG(LogTemp, Warning, TEXT("CPD[%d]: %d selected components, %d valid components, same value: %.3f"),
		Index, SelectedComponents.Num(), ValidComponentsCount, ReferenceValue);

	// All values are the same
	return FString::Printf(TEXT("%.1f"), ReferenceValue);
}

void SCPDWindow::DisplayCPDHeader()
{
	if (!CPDDisplayBox.IsValid())
	{
		return;
	}

	// Add header with array info and add/remove buttons
	CPDDisplayBox->AddSlot()
	.AutoHeight()
	.Padding(5.0f, 5.0f)
	[
		SNew(SHorizontalBox)
		+ SHorizontalBox::Slot()
		.FillWidth(1.0f)
		.VAlign(VAlign_Center)
		[
			SNew(STextBlock)
			.Text(LOCTEXT("CPDArrayHeader", "Array elements"))
		]
		+ SHorizontalBox::Slot()
		.AutoWidth()
		.VAlign(VAlign_Center)
		.Padding(5.0f, 0.0f, 0.0f, 0.0f)
		[
			SNew(SButton)
			.Text(LOCTEXT("AddElement", "+"))
			.ToolTipText(LOCTEXT("AddElementTooltip", "Add new element"))
			.OnClicked(this, &SCPDWindow::OnAddCPDElement)
			.ButtonStyle(FCoreStyle::Get(), "NoBorder")
			.ContentPadding(FMargin(8.0f, 4.0f))
		]
		+ SHorizontalBox::Slot()
		.AutoWidth()
		.VAlign(VAlign_Center)
		.Padding(5.0f, 0.0f, 0.0f, 0.0f)
		[
			SNew(SButton)
			.Text(LOCTEXT("RemoveElement", "X"))
			.ToolTipText(LOCTEXT("RemoveElementTooltip", "Remove element"))
			.OnClicked(this, &SCPDWindow::OnRemoveCPDElement)
			.ButtonStyle(FCoreStyle::Get(), "NoBorder")
			.ContentPadding(FMargin(8.0f, 4.0f))
		]
	];
}

void SCPDWindow::DisplayCPDPropertyData(UPrimitiveComponent* Component, FProperty* CPDProperty)
{
	if (!Component || !CPDProperty || !CPDDisplayBox.IsValid())
	{
		return;
	}

	// Show property type info
	CPDDisplayBox->AddSlot()
	.AutoHeight()
	.Padding(5.0f)
	[
		SNew(STextBlock)
		.Text(FText::FromString(FString::Printf(TEXT("Property Type: %s"), *CPDProperty->GetClass()->GetName())))
		.ColorAndOpacity(FLinearColor::Gray)
	];

	// Check if it's an array property (CPD is typically TArray<float>)
	if (FArrayProperty* ArrayProperty = CastField<FArrayProperty>(CPDProperty))
	{
		CPDDisplayBox->AddSlot()
		.AutoHeight()
		.Padding(5.0f)
		[
			SNew(STextBlock)
			.Text(LOCTEXT("FoundArrayProperty", "Found Array Property!"))
			.ColorAndOpacity(FLinearColor::Green)
		];

		try
		{
			// Get the array data
			FScriptArrayHelper ArrayHelper(ArrayProperty, CPDProperty->ContainerPtrToValuePtr<void>(Component));
			int32 ArraySize = ArrayHelper.Num();

			CPDDisplayBox->AddSlot()
			.AutoHeight()
			.Padding(5.0f)
			[
				SNew(STextBlock)
				.Text(FText::FromString(FString::Printf(TEXT("Array Size: %d"), ArraySize)))
				.ColorAndOpacity(FLinearColor::Green)
			];

			if (ArraySize == 0)
			{
				CPDDisplayBox->AddSlot()
				.AutoHeight()
				.Padding(5.0f)
				[
					SNew(STextBlock)
					.Text(LOCTEXT("EmptyCPD", "No elements - array is empty"))
				];
				return;
			}

			// Check if the inner property is float
			if (FFloatProperty* FloatProperty = CastField<FFloatProperty>(ArrayProperty->Inner))
			{
				CPDDisplayBox->AddSlot()
				.AutoHeight()
				.Padding(5.0f)
				[
					SNew(STextBlock)
					.Text(LOCTEXT("FloatArrayConfirmed", "Confirmed: Float Array"))
					.ColorAndOpacity(FLinearColor::Green)
				];

				// Display each CPD value
				for (int32 i = 0; i < ArraySize; ++i)
				{
					float* ElementPtr = (float*)ArrayHelper.GetRawPtr(i);
					if (ElementPtr)
					{
						CreateCPDElementWidget(i, *ElementPtr);
					}
				}
			}
			else
			{
				// Not a float array, show what type it is
				FString InnerTypeName = ArrayProperty->Inner ? ArrayProperty->Inner->GetClass()->GetName() : TEXT("Unknown");
				CPDDisplayBox->AddSlot()
				.AutoHeight()
				.Padding(5.0f)
				[
					SNew(STextBlock)
					.Text(FText::FromString(FString::Printf(TEXT("Array of %s (not float)"), *InnerTypeName)))
					.ColorAndOpacity(FLinearColor::Yellow)
				];
			}
		}
		catch (...)
		{
			CPDDisplayBox->AddSlot()
			.AutoHeight()
			.Padding(5.0f)
			[
				SNew(STextBlock)
				.Text(LOCTEXT("CPDAccessError", "Error accessing Custom Primitive Data"))
				.ColorAndOpacity(FLinearColor::Red)
			];
		}
	}
	else
	{
		CPDDisplayBox->AddSlot()
		.AutoHeight()
		.Padding(5.0f)
		[
			SNew(STextBlock)
			.Text(LOCTEXT("NotArrayCPD", "Custom Primitive Data is not an array"))
			.ColorAndOpacity(FLinearColor::Red)
		];
	}
}

void SCPDWindow::CreateCPDElementWidget(int32 Index, float Value)
{
	CreateCPDElementRow(Index, Value, true);
}

void SCPDWindow::CreateCPDElementRow(int32 Index, float Value, bool bDataFound)
{
	if (!CPDDisplayBox.IsValid())
	{
		return;
	}

	// Create a row exactly like Details panel CPD interface with proper theming
	CPDDisplayBox->AddSlot()
	.AutoHeight()
	.Padding(0.0f, 1.0f)
	[
		SNew(SBorder)
		.BorderImage(FAppStyle::GetBrush("DetailsView.CategoryMiddle"))
		.BorderBackgroundColor(Index % 2 == 0 ?
			FAppStyle::GetSlateColor("Colors.Panel") :
			FAppStyle::GetSlateColor("Colors.Recessed"))
		.Padding(FMargin(0.0f))
		[
			SNew(SHorizontalBox)

			// Name content (Index + Parameter name) - like Details panel
			+ SHorizontalBox::Slot()
			.FillWidth(0.5f)
			.VAlign(VAlign_Center)
			.Padding(12.0f, 6.0f, 8.0f, 6.0f)
			[
				SNew(SHorizontalBox)
				// Index number with proper spacing
				+ SHorizontalBox::Slot()
				.AutoWidth()
				.VAlign(VAlign_Center)
				.Padding(0.0f, 0.0f, 12.0f, 0.0f)
				[
					SNew(STextBlock)
					.Text(FText::AsNumber(Index))
					.Font(FAppStyle::GetFontStyle("PropertyWindow.NormalFont"))
					.ColorAndOpacity(FAppStyle::GetSlateColor("Colors.AccentBlue"))
				]
				// Parameter name
				+ SHorizontalBox::Slot()
				.FillWidth(1.0f)
				.VAlign(VAlign_Center)
				[
					SNew(SBorder)
					.BorderImage(FAppStyle::GetBrush("NoBorder"))
					.BorderBackgroundColor_Lambda([this, Index]()
					{
						// 根据高亮状态动态改变背景色
						return HighlightedCPDParameters.Contains(Index) ?
							FLinearColor(0.0f, 0.7f, 1.0f, 0.2f) :  // 高亮：浅蓝色背景
							FLinearColor(0.0f, 0.0f, 0.0f, 0.0f);    // 不高亮：透明背景
					})
					.Padding(FMargin(4.0f, 2.0f))
					.OnMouseButtonDown(this, &SCPDWindow::OnCPDParameterRightClick, Index)
					[
						SNew(STextBlock)
						.Text(GetCPDParameterName(Index))
						.Font(FAppStyle::GetFontStyle("PropertyWindow.NormalFont"))
						.ColorAndOpacity_Lambda([this, Index]()
						{
							// 根据高亮状态动态改变文字颜色
							return HighlightedCPDParameters.Contains(Index) ?
								FLinearColor(0.0f, 0.6f, 1.0f, 1.0f) :      // 高亮：蓝色文字
								FLinearColor(0.8f, 0.8f, 0.8f, 1.0f);       // 不高亮：灰色文字
						})
						.ToolTipText(FText::FromString(FString::Printf(TEXT("Custom Primitive Data [%d]\nCurrent value: %.6f\nRight-click to toggle highlight"), Index, Value)))
					]
				]
			]

			// Value content - draggable spin box
			+ SHorizontalBox::Slot()
			.FillWidth(0.4f)
			.VAlign(VAlign_Center)
			.Padding(8.0f, 4.0f, 4.0f, 4.0f)
			[
				SNew(SSpinBox<float>)
				.Value_Lambda([this, Index]()
				{
					FString ValueText = GetMultiObjectCPDValue(Index);
					if (ValueText == TEXT("Multiple Values"))
					{
						return 0.0f; // Default value for multiple values
					}
					return FCString::Atof(*ValueText);
				})
				.OnValueChanged_Lambda([this, Index](float NewValue)
				{
					// Update CPD value when dragging
					SetCPDValue(Index, NewValue);
				})
				.OnValueCommitted_Lambda([this, Index](float NewValue, ETextCommit::Type CommitType)
				{
					// Final commit when done editing
					SetCPDValue(Index, NewValue);
				})
				.MinValue(-100.0f)
				.MaxValue(100.0f)
				.Delta(0.1f)
				.MinDesiredWidth(80.0f)
				.IsEnabled_Lambda([this, Index]()
				{
					// Disable editing if showing "Multiple Values"
					FString ValueText = GetMultiObjectCPDValue(Index);
					return ValueText != TEXT("Multiple Values");
				})
			]
			// Reset to default button
			+ SHorizontalBox::Slot()
			.AutoWidth()
			.VAlign(VAlign_Center)
			.Padding(4.0f, 4.0f, 12.0f, 4.0f)
			[
				SNew(SButton)
				.ButtonStyle(FAppStyle::Get(), "SimpleButton")
				.Text(FText::FromString(TEXT("↺")))
				.ToolTipText(FText::FromString(TEXT("Reset to material default value")))
				.OnClicked(this, &SCPDWindow::OnResetCPDToDefault, Index)
				.IsEnabled_Lambda([this, Index]()
				{
					// Enable if we have a default value for this index
					return CPDDefaultValues.Contains(Index);
				})
			]
		]
	];
}

FReply SCPDWindow::OnAddCPDElement()
{
	// Based on UE source: Add new element to CPD array
	if (CurrentComponent.IsValid())
	{
		UPrimitiveComponent* Component = CurrentComponent.Get();

		// Get current CPD data
		const FCustomPrimitiveData& CurrentCPDData = Component->GetDefaultCustomPrimitiveData();
		int32 CurrentSize = CurrentCPDData.Data.Num();

		// Add a new element with default value 0.0f at the end
		// Use the public API SetDefaultCustomPrimitiveDataFloat
		Component->SetDefaultCustomPrimitiveDataFloat(CurrentSize, 0.0f);

		// Refresh the display
		OnSelectionChanged(nullptr);
	}
	return FReply::Handled();
}

FReply SCPDWindow::OnRemoveCPDElement()
{
	// Remove last element by setting it to 0 and logically treating the array as smaller
	if (CurrentComponent.IsValid())
	{
		UPrimitiveComponent* Component = CurrentComponent.Get();

		// Get current CPD data
		const FCustomPrimitiveData& CurrentCPDData = Component->GetDefaultCustomPrimitiveData();
		int32 CurrentSize = CurrentCPDData.Data.Num();

		// Remove the last element if array has more than 1 element
		if (CurrentSize > 1)
		{
			// Set the last element to 0 (effectively removing it)
			Component->SetDefaultCustomPrimitiveDataFloat(CurrentSize - 1, 0.0f);

			// Note: We can't actually shrink the array with public API,
			// but setting to 0 effectively removes the element's influence
		}

		// Refresh the display
		OnSelectionChanged(nullptr);
	}
	return FReply::Handled();
}

FReply SCPDWindow::OnEditCPDValue(int32 Index)
{
	// TODO: Open edit dialog for CPD value
	// Based on UE source: This would open a numeric input dialog
	return FReply::Handled();
}

FText SCPDWindow::GetCPDParameterName(int32 Index) const
{
	// Based on UE source: Try to find parameter name from materials
	// Use scanned parameter names if available
	if (const FString* ParameterName = CPDParameterNames.Find(Index))
	{
		return FText::FromString(*ParameterName);
	}

	// Fallback to default name
	return FText::FromString(FString::Printf(TEXT("Element_%d"), Index));
}

void SCPDWindow::OnCPDValueChanged(float NewValue, int32 Index)
{
	SetCPDValue(Index, NewValue);
}

void SCPDWindow::OnSelectionChanged(UObject* Object)
{
	// Update selected components
	SelectedComponents.Empty();

	TArray<UPrimitiveComponent*> PrimitiveComponents = GetSelectedPrimitiveComponents();
	for (UPrimitiveComponent* Component : PrimitiveComponents)
	{
		if (IsValid(Component))
		{
			SelectedComponents.Add(Component);
		}
	}

	// Update CPD display
	UpdateCPDDisplay();
}

TArray<UPrimitiveComponent*> SCPDWindow::GetSelectedPrimitiveComponents() const
{
	TArray<UPrimitiveComponent*> PrimitiveComponents;

	if (GEditor && GEditor->GetSelectedActors())
	{
		for (FSelectionIterator It(GEditor->GetSelectedActorIterator()); It; ++It)
		{
			if (AActor* Actor = Cast<AActor>(*It))
			{
				// Only get the root component or the first mesh component to avoid duplicates
				if (UPrimitiveComponent* RootPrimitive = Cast<UPrimitiveComponent>(Actor->GetRootComponent()))
				{
					PrimitiveComponents.Add(RootPrimitive);
				}
				else
				{
					// If root is not a primitive, find the first mesh component
					if (UStaticMeshComponent* MeshComp = Actor->FindComponentByClass<UStaticMeshComponent>())
					{
						PrimitiveComponents.Add(MeshComp);
					}
					else if (USkeletalMeshComponent* SkelMeshComp = Actor->FindComponentByClass<USkeletalMeshComponent>())
					{
						PrimitiveComponents.Add(SkelMeshComp);
					}
				}
			}
		}
	}

	return PrimitiveComponents;
}

void SCPDWindow::SetCPDValue(int32 Index, float Value)
{
	// Based on UE source: CustomPrimitiveDataCustomization.cpp line 845
	// Use SetDefaultCustomPrimitiveDataFloat method (confirmed in PrimitiveComponent.h line 1156)
	for (TWeakObjectPtr<UPrimitiveComponent> WeakComponent : SelectedComponents)
	{
		if (UPrimitiveComponent* Component = WeakComponent.Get())
		{
			// This is the correct method from UE source code
			Component->SetDefaultCustomPrimitiveDataFloat(Index, Value);

			// SetDefaultCustomPrimitiveDataFloat already marks render state dirty
		}
	}
}

float SCPDWindow::GetCPDValue(int32 Index) const
{
	// Based on UE source: Get CPD value from component
	// Use GetDefaultCustomPrimitiveData() method (confirmed in PrimitiveComponent.h line 1174)
	for (TWeakObjectPtr<UPrimitiveComponent> WeakComponent : SelectedComponents)
	{
		if (UPrimitiveComponent* Component = WeakComponent.Get())
		{
			// Get the CPD struct and access the Data array
			const FCustomPrimitiveData& CPDData = Component->GetDefaultCustomPrimitiveData();
			if (CPDData.Data.IsValidIndex(Index))
			{
				return CPDData.Data[Index];
			}
		}
	}
	return 0.0f;
}

void SCPDWindow::OnCPDValueCommitted(float NewValue, ETextCommit::Type CommitType, int32 Index)
{
	SetCPDValue(Index, NewValue);
}

void SCPDWindow::OnCPDValueTextChanged(const FText& Text, int32 Index)
{
	// Real-time validation could be added here
	// For now, we'll handle changes on commit only
}

void SCPDWindow::OnCPDValueTextCommitted(const FText& Text, ETextCommit::Type CommitType, int32 Index)
{
	// Parse the text as a float and set the CPD value
	FString TextString = Text.ToString();
	float NewValue = FCString::Atof(*TextString);

	// Set the CPD value using our existing method
	SetCPDValue(Index, NewValue);

	// Refresh display to show the updated value
	RefreshCPDDisplay();
}

FReply SCPDWindow::OnApplyMeshPaintToCPD()
{
	// Minimal implementation
	return FReply::Handled();
}

FReply SCPDWindow::OnClearAllCPD()
{
	// Minimal implementation
	return FReply::Handled();
}

void SCPDWindow::RefreshCPDDisplay()
{
	// Enhanced refresh: check material parameters and create CPD elements as needed
	if (CurrentComponent.IsValid())
	{
		// Check for multiple different materials
		if (HasDifferentMaterials())
		{
			ShowMultipleMaterialsMessage();
			return;
		}

		UPrimitiveComponent* Component = CurrentComponent.Get();

		// First, scan materials to get the required CPD parameters (only current slot)
		TMap<int32, FString> MaterialCPDParams;
		TMap<int32, float> MaterialDefaultValues;
		ScanMaterialParametersWithDefaults(Component, MaterialCPDParams, MaterialDefaultValues);

		// Check if any CPD parameters were found in materials
		if (MaterialCPDParams.Num() == 0)
		{
			// No CPD parameters found in materials, show warning message
			ShowNoCPDParametersMessage();
			return;
		}

		// Get current CPD data
		const FCustomPrimitiveData& CurrentCPDData = Component->GetDefaultCustomPrimitiveData();
		int32 CurrentCPDSize = CurrentCPDData.Data.Num();

		// Find the maximum CPD index needed
		int32 MaxRequiredIndex = -1;
		for (const auto& Param : MaterialCPDParams)
		{
			MaxRequiredIndex = FMath::Max(MaxRequiredIndex, Param.Key);
		}

		// If we need more CPD elements than currently exist, create them (only for single selection)
		if (MaxRequiredIndex >= CurrentCPDSize && SelectedComponents.Num() == 1)
		{
			// Create CPD elements up to the required index
			for (int32 i = CurrentCPDSize; i <= MaxRequiredIndex; ++i)
			{
				// Use material default value if available, otherwise 0.0f
				float DefaultValue = MaterialDefaultValues.Contains(i) ? MaterialDefaultValues[i] : 0.0f;
				Component->SetDefaultCustomPrimitiveDataFloat(i, DefaultValue);
			}
		}

		// Update parameter names and default values
		CPDParameterNames = MaterialCPDParams;
		CPDDefaultValues = MaterialDefaultValues;

		// Trigger a refresh by calling OnSelectionChanged
		OnSelectionChanged(nullptr);
	}
}

FReply SCPDWindow::OnMouseButtonDown(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent)
{
	// Always refresh display when user clicks anywhere on the CPD window
	// This ensures immediate update after any changes in Details panel or materials
	RefreshCPDDisplay();

	// Handle the click and allow event propagation
	return FReply::Handled();
}

FReply SCPDWindow::OnFocusReceived(const FGeometry& MyGeometry, const FFocusEvent& InFocusEvent)
{
	// Also refresh when window receives focus
	RefreshCPDDisplay();
	return SCompoundWidget::OnFocusReceived(MyGeometry, InFocusEvent);
}

void SCPDWindow::Tick(const FGeometry& AllottedGeometry, const double InCurrentTime, const float InDeltaTime)
{
	SCompoundWidget::Tick(AllottedGeometry, InCurrentTime, InDeltaTime);

	// No automatic refresh - only refresh on user interaction for better performance
}

#undef LOCTEXT_NAMESPACE
