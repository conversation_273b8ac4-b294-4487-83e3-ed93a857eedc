{"Version": "1.2", "Data": {"Source": "f:\\tamo_streaming\\projects\\tamo\\plugins\\vtstools\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\vtstools\\singlefile\\vtstools.h.cpp", "ProvidedModule": "", "Includes": ["f:\\tamo_streaming\\projects\\tamo\\plugins\\vtstools\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\vtstools\\definitions.vtstools.h", "f:\\tamo_streaming\\engine\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\unrealed\\shareddefinitions.unrealed.project.valapi.cpp20.h", "f:\\tamo_streaming\\projects\\tamo\\plugins\\vtstools\\source\\vtstools\\public\\vtstools.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\coreminimal.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\coretypes.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\hal\\platform.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\misc\\build.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\misc\\largeworldcoordinates.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\hal\\preprocessorhelpers.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\windows\\windowsplatformcompilerpresetup.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\msvc\\msvcplatformcompilerpresetup.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\genericplatform\\genericplatformcompilerpresetup.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\genericplatform\\genericplatform.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\microsoft\\microsoftplatformcodeanalysis.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\windows\\windowsplatform.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\sal.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\concurrencysal.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\msvc\\msvcplatform.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\windows\\windowsplatformcodeanalysis.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\windows\\windowsplatformcompilersetup.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\profilingdebugging\\umemorydefines.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\misc\\coremiscdefines.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\misc\\coredefines.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\corefwd.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\containers\\containersfwd.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\traits\\iscontiguouscontainer.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\misc\\staticassertcompletetype.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\initializer_list", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\yvals_core.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\vcruntime.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\vadefs.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xkeycheck.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\cstddef", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\stddef.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xtr1common", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\mathfwd.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\uobject\\uobjecthierarchyfwd.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\misc\\varargs.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\logging\\logverbosity.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\misc\\outputdevice.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\isarrayorrefoftypebypredicate.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\isvalidvariadicfunctionarg.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\isenum.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\type_traits", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\cstdint", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\stdint.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\traits\\ischarencodingcompatiblewith.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\traits\\ischartype.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\hal\\platformcrt.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\new", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\exception", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\yvals.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\crtdbg.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\vcruntime_new_debug.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\vcruntime_new.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\crtdefs.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\use_ansi.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\cstdlib", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\math.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_math.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\stdlib.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_malloc.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_search.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_wstdlib.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\limits.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\malloc.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\vcruntime_exception.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\eh.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_terminate.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\wchar.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_memcpy_s.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\errno.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\vcruntime_string.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_wconio.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_stdio_config.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_wctype.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_wdirect.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_wio.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_share.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_wprocess.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_wstdio.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_wstring.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_wtime.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\sys\\stat.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\sys\\types.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\stdio.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\stdarg.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\math.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\float.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\string.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\corecrt_memory.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\hal\\platformmisc.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\genericplatform\\genericplatformmisc.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\containers\\stringfwd.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\traits\\elementtype.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\numericlimits.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\misc\\compressionflags.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\misc\\enumclassflags.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xmmintrin.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\mmintrin.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\windows\\windowsplatformmisc.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\hal\\platformmemory.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\genericplatform\\genericplatformmemory.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\hal\\platformstring.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\windows\\windowsplatformstring.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\genericplatform\\microsoftplatformstring.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\misc\\char.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\traits\\inttype.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\ctype.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\wctype.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\genericplatform\\genericplatformstring.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\genericplatform\\genericplatformstricmp.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\enableif.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\traits\\ischarencodingsimplyconvertibleto.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\traits\\isfixedwidthcharencoding.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\tchar.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\windows\\windowsplatformmemory.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\windows\\windowssystemincludes.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\windows\\minimalwindowsapi.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\microsoft\\minimalwindowsapi.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\intrin.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\intrin0.inl.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\setjmp.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\immintrin.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\wmmintrin.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\nmmintrin.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\smmintrin.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\tmmintrin.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\pmmintrin.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\emmintrin.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\zmmintrin.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\ammintrin.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\hal\\hidetchar.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\hal\\allowtchar.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\intsafe.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\winapifamily.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\winpackagefamily.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\specstrings.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\specstrings_strict.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\specstrings_undef.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\driverspecs.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\sdv_driverspecs.h", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\shared\\strsafe.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\profilingdebugging\\cpuprofilertrace.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\hal\\platformatomics.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\genericplatform\\genericplatformatomics.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\windows\\windowsplatformatomics.h", "f:\\tamo_streaming\\engine\\source\\runtime\\tracelog\\public\\trace\\config.h", "f:\\tamo_streaming\\engine\\source\\runtime\\tracelog\\public\\trace\\detail\\channel.h", "f:\\tamo_streaming\\engine\\source\\runtime\\tracelog\\public\\trace\\trace.h", "f:\\tamo_streaming\\engine\\source\\runtime\\tracelog\\public\\trace\\detail\\trace.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\misc\\launder.h", "f:\\tamo_streaming\\engine\\source\\runtime\\tracelog\\public\\trace\\detail\\channel.inl", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\misc\\assertionmacros.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\string\\formatstringsan.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\atomic", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\cstring", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xatomic.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\intrin0.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xatomic_wait.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xthreads.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\climits", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xtimec.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\ctime", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\time.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\ispointer.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\misc\\exec.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\hal\\memorybase.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\atomic.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\hal\\threadsafecounter.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\hal\\threadsafecounter64.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\isintegral.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\istrivial.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\andornot.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\istriviallycopyconstructible.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\istriviallycopyassignable.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\hal\\unrealmemory.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\profilingdebugging\\memorytrace.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\isarithmetic.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\ispodtype.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\isuecoretype.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\unrealtypetraits.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\models.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\identity.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\removereference.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\integralconstant.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\isclass.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\typecompatiblebytes.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\unrealtemplate.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\copyqualifiersandrefsfromto.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\copyqualifiersfromto.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\requires.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\traits\\usebitwiseswap.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\hal\\platformmath.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\genericplatform\\genericplatformmath.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\decay.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\isfloatingpoint.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\resolvetypeambiguity.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\issigned.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\limits", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\cfloat", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\cwchar", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\cstdio", "c:\\program files (x86)\\windows kits\\10\\include\\10.0.22621.0\\ucrt\\fenv.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\windows\\windowsplatformmath.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\microsoft\\microsoftplatformmath.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\unrealplatformmathsse4.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\unrealplatformmathsse.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\memoryops.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\containers\\containerallocationpolicies.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\containers\\containerhelpers.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\ispolymorphic.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\isenumclass.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\hal\\platformproperties.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\genericplatform\\genericplatformproperties.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\windows\\windowsplatformproperties.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\misc\\engineversionbase.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\internationalization\\textnamespacefwd.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\serialization\\archive.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\serialization\\archivecookdata.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\serialization\\archivesavepackagedata.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\uobject\\objectversion.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\less.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\sorting.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\algo\\binarysearch.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\identityfunctor.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\invoke.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\traits\\memberfunctionptrouter.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\algo\\sort.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\algo\\introsort.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\algo\\impl\\binaryheap.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\reversepredicate.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\unrealmathutility.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\misc\\cstring.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\misc\\crc.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\containers\\unrealstring.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\containers\\unrealstringincludes.h.inl", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\isarray.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\containers\\array.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\misc\\intrusiveunsetoptionalstate.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\misc\\optionalfwd.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\misc\\reverseiterate.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\iterator", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\iosfwd", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xutility", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\__msvc_iter_core.hpp", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\utility", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\concepts", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\compare", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\containers\\allowshrinking.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\containers\\containerelementtypecompatibility.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\serialization\\memoryimagewriter.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\serialization\\memorylayout.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\concepts\\staticclassprovider.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\concepts\\staticstructprovider.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\containers\\enumasbyte.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\typehash.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\misc\\delayedautoregister.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\isabstract.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\algo\\heapify.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\algo\\heapsort.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\algo\\isheap.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\algo\\stablesort.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\algo\\rotate.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\concepts\\gettypehashable.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\losesqualifiersfromto.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\alignmenttemplates.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\autortfm\\autortfm.h", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\algorithm", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\xmemory", "c:\\program files\\microsoft visual studio\\2022\\professional\\vc\\tools\\msvc\\14.38.33130\\include\\tuple", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\containers\\unrealstring.h.inl", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\misc\\stringformatarg.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\misc\\framenumber.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\misc\\timespan.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\interval.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\containers\\stringconv.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\uobject\\unrealnames.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\uobject\\unrealnames.inl", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\uobject\\unrealnames.inl", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\uobject\\nametypes.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\hal\\criticalsection.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\windows\\windowscriticalsection.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\misc\\stringbuilder.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\containers\\stringview.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\string\\find.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\containers\\arrayview.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\misc\\parse.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\function.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\functionfwd.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\misc\\structbuilder.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\pointerisconvertiblefromto.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\containers\\scriptarray.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\containers\\bitarray.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\containers\\sparsearray.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\serialization\\structuredarchive.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\serialization\\formatters\\binaryarchiveformatter.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\serialization\\structuredarchiveformatter.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\serialization\\structuredarchivenamehelpers.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\serialization\\structuredarchiveadapters.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\concepts\\insertable.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\serialization\\archiveproxy.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\serialization\\structuredarchiveslots.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\misc\\optional.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\serialization\\structuredarchivefwd.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\serialization\\structuredarchiveslotbase.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\uniqueobj.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\uniqueptr.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\removeextent.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\serialization\\structuredarchivedefines.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\containers\\set.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\retainedref.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\algo\\reverse.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\containers\\map.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\tuple.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\delegates\\integersequence.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\intpoint.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\misc\\largeworldcoordinatesserializer.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\intvector.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\logging\\logcategory.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\logging\\logmacros.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\logging\\logscopedcategoryandverbosityoverride.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\logging\\logtrace.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\profilingdebugging\\formatargstrace.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\vector2d.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\misc\\enginenetworkcustomversion.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\misc\\guid.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\hash\\cityhash.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\intrect.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\misc\\byteswap.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\hal\\platformtls.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\genericplatform\\genericplatformtls.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\windows\\windowsplatformtls.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\coreglobals.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\sharedpointer.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\sharedpointerinternals.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\sharedpointerfwd.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\sharedpointertesting.inl", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\internationalization\\culturepointer.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\uobject\\weakobjectptrtemplates.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\uobject\\weakobjectptrtemplatesfwd.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\uobject\\strongobjectptrtemplatesfwd.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\delegates\\delegatesettings.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\delegates\\idelegateinstance.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\delegates\\delegatebase.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\delegates\\delegateaccesshandler.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\misc\\mtaccessdetector.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\hal\\platformstackwalk.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\windows\\windowsplatformstackwalk.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\microsoft\\microsoftplatformstackwalk.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\genericplatform\\genericplatformstackwalk.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\misc\\transactionallysafecriticalsection.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\misc\\transactionallysafescopelock.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\misc\\scopelock.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\delegates\\delegateinstancesimplfwd.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\delegates\\multicastdelegatebase.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\uobject\\scriptdelegates.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\delegates\\delegate.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\delegates\\delegateinstanceinterface.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\delegates\\delegateinstancesimpl.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\delegates\\delegatesignatureimpl.inl", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\isconst.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\delegates\\delegatecombinations.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\internationalization\\textlocalizationmanager.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\async\\taskgraphfwd.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\async\\taskgraphdefinitions.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\refcounting.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\internationalization\\lockeyfuncs.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\internationalization\\loctesting.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\internationalization\\localizedtextsourcetypes.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\internationalization\\textkey.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\internationalization\\text.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\containers\\sortedmap.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\internationalization\\textcomparison.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\internationalization\\stringtablecorefwd.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\internationalization\\itextdata.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\templates\\isconstructible.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\internationalization\\internationalization.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\vector.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\misc\\networkversion.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\color.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\axis.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\vectorregister.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\unrealmathsse.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\unrealmathvectorconstants.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\unrealmathvectorcommon.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\vector4.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\twovectors.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\edge.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\capsuleshape.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\rotator.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\misc\\datetime.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\rangebound.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\misc\\automationevent.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\range.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\rangeset.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\box.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\sphere.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\matrix.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\plane.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\matrix.inl", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\transform.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\quat.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\scalarregister.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\transformnonvectorized.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\transformvectorized.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\box2d.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\boxspherebounds.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\orientedbox.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\rotationtranslationmatrix.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\rotationaboutpointmatrix.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\scalerotationtranslationmatrix.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\rotationmatrix.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\quatrotationtranslationmatrix.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\perspectivematrix.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\orthomatrix.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\translationmatrix.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\inverserotationmatrix.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\scalematrix.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\mirrormatrix.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\clipprojectionmatrix.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\float32.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\float16.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\convexhull2d.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\unrealmath.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\colorlist.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\curveedinterface.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\interpcurvepoint.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\float16color.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\interpcurve.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\algo\\minelement.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\algo\\impl\\rangepointertype.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\ray.h", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\math\\vector2dhalf.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}