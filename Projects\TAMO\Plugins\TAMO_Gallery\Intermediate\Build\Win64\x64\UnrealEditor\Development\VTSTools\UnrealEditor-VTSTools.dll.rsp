/MANIFEST:EMBED
/MANIFESTINPUT:"..\Build\Windows\Resources\Default-Win64.manifest"
/NOLOGO
/DEBUG:FULL
/errorReport:prompt
/MACHINE:x64
/SUBSYSTEM:WINDOWS
/FIXED:No
/NXCOMPAT
/STACK:12000000
/DELAY:UNLOAD
/DLL
/PDBALTPATH:%_PDB%
/d2:-ExtendedWarningInfo
/OPT:NOREF
/OPT:NOICF
/INCREMENTAL:NO
/ignore:4199
/ignore:4099
/ALTERNATENAME:__imp___std_init_once_begin_initialize=__imp_InitOnceBeginInitialize
/ALTERNATENAME:__imp___std_init_once_complete=__imp_InitOnceComplete
/DELAYLOAD:"d3d12.dll"
/DELAYLOAD:"DBGHELP.DLL"
/LIBPATH:"C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.38.33130\lib\x64"
/LIBPATH:"C:\Program Files (x86)\Windows Kits\10\lib\10.0.22621.0\ucrt\x64"
/LIBPATH:"C:\Program Files (x86)\Windows Kits\10\lib\10.0.22621.0\um\x64"
/NODEFAULTLIB:"LIBCMT"
/NODEFAULTLIB:"LIBCPMT"
/NODEFAULTLIB:"LIBCMTD"
/NODEFAULTLIB:"LIBCPMTD"
/NODEFAULTLIB:"MSVCRTD"
/NODEFAULTLIB:"MSVCPRTD"
/NODEFAULTLIB:"LIBC"
/NODEFAULTLIB:"LIBCP"
/NODEFAULTLIB:"LIBCD"
/NODEFAULTLIB:"LIBCPD"
/FUNCTIONPADMIN:6
/NOIMPLIB
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\UnrealEd\SharedPCH.UnrealEd.Project.ValApi.Cpp20.h.obj"
"F:\TAMO_Streaming\Projects\TAMO\Plugins\VTSTools\Intermediate\Build\Win64\x64\UnrealEditor\Development\VTSTools\Module.VTSTools.cpp.obj"
"F:\TAMO_Streaming\Projects\TAMO\Plugins\VTSTools\Intermediate\Build\Win64\x64\UnrealEditor\Development\VTSTools\SCPDWindow.cpp.obj"
"F:\TAMO_Streaming\Projects\TAMO\Plugins\VTSTools\Intermediate\Build\Win64\x64\UnrealEditor\Development\VTSTools\VTSTools.cpp.obj"
"F:\TAMO_Streaming\Projects\TAMO\Plugins\VTSTools\Intermediate\Build\Win64\x64\UnrealEditor\Development\VTSTools\VTSToolsModule.cpp.obj"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\Default.rc2.res"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToolMenus\UnrealEditor-ToolMenus.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\EditorStyle\UnrealEditor-EditorStyle.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\EditorWidgets\UnrealEditor-EditorWidgets.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\PropertyEditor\UnrealEditor-PropertyEditor.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\SharedSettingsWidgets\UnrealEditor-SharedSettingsWidgets.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\SettingsEditor\UnrealEditor-SettingsEditor.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\DesktopPlatform\UnrealEditor-DesktopPlatform.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\Core\UnrealEditor-Core.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\CoreUObject\UnrealEditor-CoreUObject.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\Engine\UnrealEditor-Engine.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\UnrealEd\UnrealEditor-UnrealEd.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\Slate\UnrealEditor-Slate.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\SlateCore\UnrealEditor-SlateCore.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\InputCore\UnrealEditor-InputCore.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\EditorSubsystem\UnrealEditor-EditorSubsystem.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\LevelEditor\UnrealEditor-LevelEditor.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\ApplicationCore\UnrealEditor-ApplicationCore.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToolWidgets\UnrealEditor-ToolWidgets.lib"
"delayimp.lib"
"wininet.lib"
"rpcrt4.lib"
"ws2_32.lib"
"dbghelp.lib"
"comctl32.lib"
"Winmm.lib"
"kernel32.lib"
"user32.lib"
"gdi32.lib"
"winspool.lib"
"comdlg32.lib"
"advapi32.lib"
"shell32.lib"
"ole32.lib"
"oleaut32.lib"
"uuid.lib"
"odbc32.lib"
"odbccp32.lib"
"netapi32.lib"
"iphlpapi.lib"
"setupapi.lib"
"synchronization.lib"
"dwmapi.lib"
"imm32.lib"
"uiautomationcore.lib"
"DXGI.lib"
/OUT:"F:\TAMO_Streaming\Projects\TAMO\Plugins\VTSTools\Binaries\Win64\UnrealEditor-VTSTools.dll"
/PDB:"F:\TAMO_Streaming\Projects\TAMO\Plugins\VTSTools\Binaries\Win64\UnrealEditor-VTSTools.pdb"
/ignore:4078