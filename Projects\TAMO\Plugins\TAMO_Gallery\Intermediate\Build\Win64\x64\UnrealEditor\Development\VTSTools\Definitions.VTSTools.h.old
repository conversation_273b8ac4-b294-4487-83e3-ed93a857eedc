// Generated by UnrealBuildTool (UEBuildModuleCPP.cs) : Shared PCH Definitions for VTSTools
#pragma once
#include "../Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealEd/SharedDefinitions.UnrealEd.Project.ValApi.Cpp20.h"
#undef VTSTOOLS_API
#define UE_IS_ENGINE_MODULE 0
#define UE_DEPRECATED_FORGAME UE_DEPRECATED
#define UE_DEPRECATED_FORENGINE UE_DEPRECATED
#define UE_VALIDATE_FORMAT_STRINGS 1
#define UE_VALIDATE_INTERNAL_API 1
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_2 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_3 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_4 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_5 0
#define UE_PROJECT_NAME TAMO
#define UE_TARGET_NAME TAMOEditor
#define UE_MODULE_NAME "VTSTools"
#define UE_PLUGIN_NAME "VTSTools"
#define IMPLEMENT_ENCRYPTION_KEY_REGISTRATION() 
#define IMPLEMENT_SIGNING_KEY_REGISTRATION() 
#define EDITORSTYLE_API DLLIMPORT
#define EDITORWIDGETS_API DLLIMPORT
#define SHAREDSETTINGSWIDGETS_API DLLIMPORT
#define EXTERNALIMAGEPICKER_API DLLIMPORT
#define SETTINGSEDITOR_API DLLIMPORT
#define VTSTOOLS_API DLLEXPORT
#define WORKSPACEMENUSTRUCTURE_API DLLIMPORT
