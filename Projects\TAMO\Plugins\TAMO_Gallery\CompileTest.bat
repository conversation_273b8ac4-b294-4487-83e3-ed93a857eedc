@echo off
echo Testing VTSTools Plugin Compilation...
echo.

echo Step 1: Regenerating project files...
cd /d "F:\TAMO_Streaming\Projects\TAMO"
"C:\Program Files\Epic Games\UE_5.3\Engine\Binaries\DotNET\UnrealBuildTool\UnrealBuildTool.exe" -projectfiles -project="F:\TAMO_Streaming\Projects\TAMO\TAMO.uproject" -game -rocket -progress

echo.
echo Step 2: Building the plugin...
"C:\Program Files\Epic Games\UE_5.3\Engine\Binaries\DotNET\UnrealBuildTool\UnrealBuildTool.exe" TAMOEditor Win64 Development -Project="F:\TAMO_Streaming\Projects\TAMO\TAMO.uproject" -WaitMutex -FromMsBuild

echo.
echo Compilation test complete!
pause
