{"Version": "1.2", "Data": {"Source": "f:\\tamo_streaming\\projects\\tamo\\plugins\\vtstools\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\vtstools\\module.vtstools.cpp", "ProvidedModule": "", "PCH": "f:\\tamo_streaming\\engine\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.cpp20.h.pch", "Includes": ["f:\\tamo_streaming\\projects\\tamo\\plugins\\vtstools\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\vtstools\\definitions.vtstools.h", "f:\\tamo_streaming\\projects\\tamo\\plugins\\vtstools\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\vtstools\\permoduleinline.gen.cpp", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\hal\\permoduleinline.inl"], "ImportedModules": [], "ImportedHeaderUnits": []}}