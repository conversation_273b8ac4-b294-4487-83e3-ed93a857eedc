# CPD检测调试版本

## 🔍 问题分析

用户反馈：
1. 插件显示"custom primitive data is not an array"
2. Details面板中明明有CPD数据
3. 没有看到UI和按钮

## 🛠️ 调试改进

### 1. 增强属性搜索
```cpp
// 扩展搜索范围
TArray<FString> PossibleNames = {
    TEXT("CustomPrimitiveDataDefaults"),
    TEXT("CustomPrimitiveData"), 
    TEXT("PrimitiveDataDefaults"),
    TEXT("CustomData"),
    TEXT("CustomPrimitiveDataDefault")
};

// 遍历所有属性查找相关的
for (TFieldIterator<FProperty> PropIt(Component->GetClass()); PropIt; ++PropIt)
{
    // 查找包含"Custom", "Primitive", "Data"的属性
}
```

### 2. 详细调试信息
现在会显示：
- 组件类型名称
- 找到的属性名称
- 属性类型信息
- 数组大小和元素类型
- 搜索过程的详细信息

### 3. 强制显示UI
```cpp
// 无论是否找到CPD，都显示头部和按钮
DisplayCPDHeader();
```

## 🎯 新的界面效果

### 调试信息显示
```
--- Custom Primitive Data Defaults ---
Component: UStaticMeshComponent
Found property: CustomPrimitiveDataDefaults
Array elements                              [+] [X]
Property Type: FArrayProperty
Found Array Property!
Array Size: 3
Confirmed: Float Array

0    TestCPD                              1.2500
1    TestCPD                              0.5000
2    TestCPD                              2.0000
```

### 如果没找到属性
```
--- Custom Primitive Data Defaults ---
Component: UStaticMeshComponent
Searching all properties...
Related: SomeOtherProperty
Related: AnotherProperty
Total properties: 156
Array elements                              [+] [X]
No Custom Primitive Data property found
```

## 🚀 测试步骤

### 1. 编译插件
```bash
Build -> Rebuild Solution
```

### 2. 测试不同对象
1. **Static Mesh Actor**: 应该有CPD属性
2. **Skeletal Mesh Actor**: 可能有CPD属性
3. **其他Actor**: 查看是否有相关属性

### 3. 观察调试信息
- 查看组件类型
- 查看找到的属性名称
- 查看属性类型和数组信息
- 确认UI按钮是否显示

## 📋 预期结果

### 成功情况
```
--- Custom Primitive Data Defaults ---
Component: UStaticMeshComponent
Found property: CustomPrimitiveDataDefaults
Array elements                              [+] [X]
Property Type: FArrayProperty
Found Array Property!
Array Size: 3
Confirmed: Float Array
0    TestCPD                              1.2500
1    TestCPD                              0.5000
2    TestCPD                              2.0000
```

### 调试情况
```
--- Custom Primitive Data Defaults ---
Component: UStaticMeshComponent
Searching all properties...
Related: CustomPrimitiveDataDefaults
Related: SomeOtherCustomProperty
Total properties: 156
Array elements                              [+] [X]
Property Type: FArrayProperty
Custom Primitive Data is not an array
```

## 🔧 可能的问题和解决方案

### 问题1: 属性名称不匹配
**现象**: 显示"No Custom Primitive Data property found"
**解决**: 查看"Related:"行，找到实际的属性名称

### 问题2: 属性类型不是数组
**现象**: 显示"Custom Primitive Data is not an array"
**解决**: 检查属性类型，可能需要不同的访问方式

### 问题3: 数组元素类型不是float
**现象**: 显示"Array of XXX (not float)"
**解决**: 需要支持其他数据类型

### 问题4: UI按钮不显示
**现象**: 没有看到[+]和[X]按钮
**解决**: 检查DisplayCPDHeader()是否被调用

## 🎯 下一步行动

### 根据调试信息调整
1. **如果找到属性但类型不对**: 调整属性访问方式
2. **如果属性名称不匹配**: 添加正确的属性名称到搜索列表
3. **如果UI不显示**: 检查Slate控件创建

### 常见的CPD属性名称
可能的实际属性名称：
- `CustomPrimitiveDataDefaults`
- `CustomPrimitiveData`
- `PrimitiveDataDefaults`
- `CustomData`
- `DefaultCustomPrimitiveData`

## 📞 反馈需求

请测试后提供以下信息：
1. **组件类型**: 显示的"Component: XXX"
2. **找到的属性**: "Found property: XXX"或"Related: XXX"
3. **属性类型**: "Property Type: XXX"
4. **UI显示**: 是否看到[+]和[X]按钮
5. **错误信息**: 具体的错误提示

这些信息将帮助我们精确定位问题并修复CPD访问逻辑。

## 状态: 🔍 调试版本就绪

现在插件会显示详细的调试信息，帮助我们找到CPD属性访问的具体问题！
