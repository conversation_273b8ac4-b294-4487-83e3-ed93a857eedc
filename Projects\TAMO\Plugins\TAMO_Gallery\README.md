# VTS Tools Plugin

## 概述

VTS Tools 是一个专为TAMO项目开发的Unreal Engine编辑器插件，提供了Custom Primitive Data (CPD) 管理功能，特别针对Mesh Paint Texture Object的合批优化。

## 安装位置

插件位于：`F:\TAMO_Streaming\Projects\TAMO\Plugins\VTSTools\`

## 功能特性

### CPD窗口
- **Custom Primitive Data管理**: 可视化编辑选中对象的CPD值
- **Mesh Paint优化**: 将MeshPaintTextureDescriptor转换为CPD格式以支持合批
- **批量操作**: 支持批量清除CPD数据
- **实时预览**: 选择对象时实时显示CPD值

## 安装方法

1. 插件已经安装在正确的路径：`Projects\TAMO\Plugins\VTSTools\`
2. 重新生成项目文件 (右键TAMO.uproject文件 -> Generate Visual Studio project files)
3. 编译项目
4. 在编辑器中启用插件 (Edit -> Plugins -> 搜索"VTS Tools")

## 使用方法

### 打开CPD窗口

1. 在Unreal编辑器菜单栏中找到"VTS Tools"菜单
2. 点击"CPD"选项打开CPD窗口
3. CPD窗口会作为可停靠的标签页出现，类似于Details面板

### 编辑CPD数据

1. 在关卡中选择一个或多个包含PrimitiveComponent的Actor
2. 在CPD窗口中会显示9个CPD槽位 (CPD[0] 到 CPD[8])
3. 修改数值会实时应用到选中的所有组件上

### Mesh Paint优化

1. 选择使用了Mesh Paint Texture Object的对象
2. 点击"Apply MeshPaint to CPD"按钮
3. 插件会将MeshPaintTextureDescriptor数据转换为CPD格式
4. 这样可以让相同材质的实例进行合批渲染

## 技术原理

### CPD合批优化

传统的Mesh Paint Texture Object使用per-primitive的`MeshPaintTextureDescriptor`，这会阻止实例合批。通过将这些数据转换为Custom Primitive Data，可以实现：

1. **实例合批**: 相同CPD配置的实例可以合批
2. **GPU实例化**: 支持GPU驱动的实例化渲染
3. **内存优化**: 减少Draw Call数量

### 数据结构

```cpp
// 传统方式 (阻止合批)
struct FPrimitiveSceneData {
    uint2 MeshPaintTextureDescriptor;  // 每个实例独特
};

// CPD方式 (支持合批)
struct FPrimitiveSceneData {
    float4 CustomPrimitiveData[9];     // 可以相同，支持合批
};
```

## 项目集成

### TAMO项目特定配置

此插件专为TAMO项目设计，包含以下特性：

1. **路径配置**: 插件位于`F:\TAMO_Streaming\Projects\TAMO\Plugins\VTSTools\`
2. **模块命名**: 使用`VTSTools`命名约定
3. **编辑器集成**: 与TAMO项目的编辑器工作流程集成

### 材质节点支持

可以在材质中使用以下节点来访问CPD数据：

1. **Custom Primitive Data**: 直接访问CPD值
2. **Material Parameter Collection**: 全局参数控制
3. **Custom Material Expression**: 自定义材质表达式

## 开发扩展

### 添加新功能

1. 在`SCPDWindow.h`中添加新的UI控件声明
2. 在`SCPDWindow.cpp`中实现相应的逻辑
3. 在`VTSToolsModule.cpp`中添加新的菜单项

### 自定义材质节点

可以创建新的材质表达式来读取CPD中的Mesh Paint数据：

```cpp
// 新的材质表达式
class UMaterialExpressionMeshPaintTextureCPD : public UMaterialExpression
{
    virtual int32 Compile(FMaterialCompiler* Compiler, int32 OutputIndex) override
    {
        // 从CPD读取Mesh Paint数据
        return Compiler->CustomPrimitiveData(CPDIndex, MCT_Float2);
    }
};
```

## 注意事项

1. **兼容性**: 需要UE5.0或更高版本
2. **性能**: CPD数据会增加GPU内存使用
3. **限制**: 每个Primitive最多支持9个float4的CPD数据
4. **材质**: 需要修改材质来使用CPD而不是Mesh Paint Texture Object节点

## 故障排除

### 编译错误
- 确保所有依赖模块都已正确添加到Build.cs文件中
- 检查头文件包含路径是否正确
- 重新生成项目文件

### 菜单不显示
- 确保插件已启用
- 重启编辑器
- 检查ToolMenus模块是否正常加载

### CPD值不生效
- 确保材质中正确使用了Custom Primitive Data节点
- 检查组件是否支持CPD (大部分PrimitiveComponent都支持)
- 验证CPD索引是否正确

## 许可证

本插件专为TAMO项目开发，遵循项目内部许可证。
