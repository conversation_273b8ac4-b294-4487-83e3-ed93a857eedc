# Custom Primitive Data Defaults 显示功能

## 🎯 功能描述

直接显示Details面板中"Custom Primitive Data Defaults"的内容，与Details面板保持一致。

## ✅ 实现功能

### 1. 直接访问CPD Defaults
- 查找`CustomPrimitiveDataDefaults`属性
- 支持多种可能的属性名称
- 直接读取数组数据

### 2. 数值显示
- 显示每个CPD槽位的值：`[0] 0.000000`
- 支持浮点数精度显示
- 空数据时显示友好提示

### 3. 界面布局
- 标题：`Custom Primitive Data Defaults`
- 滚动支持：处理大量CPD数据
- 简洁布局：每行一个数值

## 🎨 界面效果

```
Custom Primitive Data Manager
─────────────────────────────
--- Custom Primitive Data Defaults ---
[0]    0.000000
[1]    1.000000
[2]    0.500000
[3]    0.000000
[4]    2.500000
...
```

## 🚀 使用方法

### 1. 编译插件
```bash
Build -> Rebuild Solution
```

### 2. 启用插件
```bash
Edit -> Plugins -> "VTS Tools" -> 启用 -> 重启
```

### 3. 使用功能
1. 菜单栏 -> VTS Tools -> CPD
2. 选择场景中的Static Mesh Actor
3. 查看CPD Defaults数值

## 📋 支持的对象类型

### ✅ 支持
- Static Mesh Component
- Skeletal Mesh Component
- 其他有CPD属性的PrimitiveComponent

### 显示状态
- **有数据**: 显示具体的CPD数值
- **空数据**: 显示"Custom Primitive Data is empty"
- **无属性**: 显示"No Custom Primitive Data Defaults found"

## 🔧 技术实现

### 属性查找
```cpp
// 主要属性名
FProperty* CPDProperty = Component->GetClass()->FindPropertyByName(TEXT("CustomPrimitiveDataDefaults"));

// 备选属性名
TArray<FString> PossibleNames = {
    TEXT("CustomPrimitiveData"),
    TEXT("CustomPrimitiveDataDefaults"), 
    TEXT("PrimitiveDataDefaults"),
    TEXT("CustomData")
};
```

### 数据读取
```cpp
// 安全的数组访问
FScriptArrayHelper ArrayHelper(ArrayProperty, CPDProperty->ContainerPtrToValuePtr<void>(Component));
int32 ArraySize = ArrayHelper.Num();

// 浮点数值读取
float* ElementPtr = (float*)ArrayHelper.GetRawPtr(i);
```

### 界面显示
```cpp
// 每个CPD值的显示
SNew(SHorizontalBox)
+ SHorizontalBox::Slot() // 索引
+ SHorizontalBox::Slot() // 数值
```

## 📊 与Details面板对比

### Details面板
```
▼ Rendering
  ▼ Custom Primitive Data Defaults
    [0] 0.000000
    [1] 1.000000
    [2] 0.500000
```

### VTS Tools窗口
```
--- Custom Primitive Data Defaults ---
[0]    0.000000
[1]    1.000000  
[2]    0.500000
```

## 🎯 优势

### 1. 专用窗口
- 独立的CPD管理界面
- 不需要在Details面板中查找
- 可以停靠到任意位置

### 2. 实时更新
- 选择对象时自动更新
- 与Details面板数据同步
- 无需手动刷新

### 3. 简洁显示
- 专注于CPD数据
- 无多余的调试信息
- 清晰的数值格式

## 🔮 后续扩展

### 短期计划
1. **编辑功能**: 添加SpinBox编辑CPD值
2. **批量操作**: 支持多选对象编辑
3. **数值验证**: 输入范围检查

### 中期计划
1. **预设管理**: 保存和加载CPD配置
2. **复制粘贴**: CPD数据的复制粘贴
3. **导入导出**: 从文件导入CPD数据

### 长期计划
1. **Mesh Paint集成**: 转换Mesh Paint到CPD
2. **材质预览**: 实时预览CPD对材质的影响
3. **性能分析**: CPD对渲染性能的影响分析

## 状态: ✅ 功能完成，准备测试

现在插件可以直接显示Custom Primitive Data Defaults的内容，与Details面板保持一致！
