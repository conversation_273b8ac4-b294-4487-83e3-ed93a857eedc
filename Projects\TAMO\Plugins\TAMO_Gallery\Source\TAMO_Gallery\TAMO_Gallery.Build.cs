using UnrealBuildTool;

public class TAMO_Gallery : ModuleRules
{
	public TAMO_Gallery(ReadOnlyTargetRules Target) : base(Target)
	{
		PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;

		PublicIncludePaths.AddRange(
			new string[] {
			}
		);

		PrivateIncludePaths.AddRange(
			new string[] {
			}
		);

		PublicDependencyModuleNames.AddRange(
			new string[]
			{
				"Core",
				"CoreUObject",
				"Engine",
				"UnrealEd",
				"EditorStyle",
				"EditorWidgets",
				"ToolMenus",
				"Slate",
				"SlateCore",
				"InputCore",
				"EditorSubsystem",
				"LevelEditor",
				"ApplicationCore",
				"ToolWidgets"
			}
		);

		PrivateDependencyModuleNames.AddRange(
			new string[]
			{
				"ToolMenus",
				"EditorStyle",
				"EditorWidgets",
				"PropertyEditor",
				"SharedSettingsWidgets",
				"SettingsEditor",
				"DesktopPlatform"
			}
		);

		DynamicallyLoadedModuleNames.AddRange(
			new string[]
			{
			}
		);
	}
}
