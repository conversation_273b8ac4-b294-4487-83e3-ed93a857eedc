# Custom Primitive Data (CPD) API 研究

## 问题描述

编译错误：`'GetCustomPrimitiveDataFloat': is not a member of 'UPrimitiveComponent'`

这表明在当前UE版本中，CPD的API可能与我们预期的不同。

## 可能的CPD API变体

### 方案1: 直接方法 (可能不存在)
```cpp
// 这些方法可能不存在或名称不同
Component->SetCustomPrimitiveDataFloat(Index, Value);
Component->GetCustomPrimitiveDataFloat(Index);
```

### 方案2: 通过CustomPrimitiveData结构
```cpp
// 可能的API
FCustomPrimitiveData& CPD = Component->GetCustomPrimitiveData();
CPD.SetFloat(Index, Value);
float Value = CPD.GetFloat(Index);
```

### 方案3: 通过数组访问
```cpp
// 可能的API
TArray<float>& CPDData = Component->GetCustomPrimitiveData().Data;
if (CPDData.IsValidIndex(Index))
{
    CPDData[Index] = Value;
}
```

### 方案4: 通过材质参数
```cpp
// 可能需要通过材质实例
UMaterialInstanceDynamic* DynMat = Component->CreateDynamicMaterialInstance(0);
DynMat->SetScalarParameterValue(FName(*FString::Printf(TEXT("CPD_%d"), Index)), Value);
```

## 当前解决方案

### 临时修复
为了让插件先编译通过，我们暂时使用空实现：

```cpp
void SCPDWindow::SetCPDValue(int32 Index, float Value)
{
    // Minimal implementation - will be enhanced later
    // TODO: Implement proper CPD setting when API is confirmed
}

float SCPDWindow::GetCPDValue(int32 Index) const
{
    // Minimal implementation - will be enhanced later
    // TODO: Implement proper CPD getting when API is confirmed
    return 0.0f;
}
```

## 研究步骤

### 1. 检查UPrimitiveComponent的实际API
需要查看当前UE版本中UPrimitiveComponent类的实际方法：

```cpp
// 在UE编辑器中，可以通过以下方式检查：
// 1. 打开C++类浏览器
// 2. 搜索UPrimitiveComponent
// 3. 查看可用的CPD相关方法
```

### 2. 查看引擎源码
```cpp
// 查看以下文件：
// Engine/Source/Runtime/Engine/Classes/Components/PrimitiveComponent.h
// Engine/Source/Runtime/Engine/Private/Components/PrimitiveComponent.cpp
```

### 3. 查看示例代码
```cpp
// 查看引擎中其他使用CPD的地方：
// Engine/Source/Runtime/Engine/Private/Materials/
// Engine/Plugins/*/Source/*/Private/
```

## 可能的正确API

### 基于UE5的可能API：

#### 选项A: SetCustomPrimitiveData
```cpp
// 可能的正确方法名
Component->SetCustomPrimitiveData(Index, Value);
float Value = Component->GetCustomPrimitiveData(Index);
```

#### 选项B: CustomPrimitiveData属性
```cpp
// 可能通过属性访问
FCustomPrimitiveData CPD = Component->CustomPrimitiveData;
// 或者
Component->CustomPrimitiveData.Data[Index] = Value;
```

#### 选项C: 通过渲染代理
```cpp
// 可能需要通过渲染代理设置
Component->MarkRenderStateDirty();
// 然后在渲染线程中设置CPD
```

## 测试代码

### 创建测试函数来验证API：

```cpp
void TestCPDAPI(UPrimitiveComponent* Component)
{
    if (!Component) return;
    
    // 测试方案1
    #if 0
    try {
        Component->SetCustomPrimitiveDataFloat(0, 1.0f);
        UE_LOG(LogTemp, Warning, TEXT("SetCustomPrimitiveDataFloat works"));
    } catch (...) {
        UE_LOG(LogTemp, Warning, TEXT("SetCustomPrimitiveDataFloat failed"));
    }
    #endif
    
    // 测试方案2
    #if 0
    try {
        Component->SetCustomPrimitiveData(0, 1.0f);
        UE_LOG(LogTemp, Warning, TEXT("SetCustomPrimitiveData works"));
    } catch (...) {
        UE_LOG(LogTemp, Warning, TEXT("SetCustomPrimitiveData failed"));
    }
    #endif
    
    // 测试方案3 - 检查可用方法
    UE_LOG(LogTemp, Warning, TEXT("Component class: %s"), *Component->GetClass()->GetName());
}
```

## 下一步行动

### 1. 先让插件编译通过
- ✅ 使用空实现暂时解决编译问题
- ✅ 确保基本框架工作

### 2. 研究正确的CPD API
- 🔄 查看UE文档
- 🔄 检查引擎源码
- 🔄 测试不同的API方案

### 3. 实现正确的CPD功能
- 🔄 找到正确的API后更新实现
- 🔄 添加错误处理
- 🔄 测试功能

## 临时状态

当前插件状态：
- ✅ **编译**: 应该可以编译通过
- ✅ **菜单**: VTS Tools菜单可用
- ✅ **窗口**: CPD窗口可以打开
- 🔄 **CPD功能**: 暂时不可用，需要研究正确API

## 备注

这是一个常见的问题，因为：
1. UE的API在不同版本间可能有变化
2. CPD功能可能在不同UE版本中有不同的实现
3. 某些功能可能需要特定的模块依赖

一旦我们找到正确的API，就可以快速实现完整的CPD功能。
