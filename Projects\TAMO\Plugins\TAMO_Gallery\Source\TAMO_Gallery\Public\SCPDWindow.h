#pragma once

#include "CoreMinimal.h"
#include "Widgets/SCompoundWidget.h"
#include "Engine/Selection.h"

// Forward declarations
class SButton;
class STextBlock;
class SVerticalBox;
class SHorizontalBox;
class SEditableTextBox;
class SCheckBox;
template<typename NumericType> class SSpinBox;
class UPrimitiveComponent;

/**
 * CPD (Custom Primitive Data) Window Widget
 * This widget provides functionality to manage Custom Primitive Data for selected objects
 */
class TAMO_GALLERY_API SCPDWindow : public SCompoundWidget
{
public:
	SLATE_BEGIN_ARGS(SCPDWindow) {}
	SLATE_END_ARGS()

	/** Constructs this widget with InArgs */
	void Construct(const FArguments& InArgs);
	
	/** Destructor */
	virtual ~SCPDWindow();

private:
	/** Refresh the CPD data display */
	void RefreshCPDData();

	/** Update CPD display when selection changes */
	void UpdateCPDDisplay();

	/** Display Custom Primitive Data for a component */
	void DisplayCustomPrimitiveData(UPrimitiveComponent* Component);

	/** Display CPD interface similar to Details panel */
	void DisplayCPDInterface(UPrimitiveComponent* Component, const TArray<float>& CPDData, bool bDataFound);

	/** Display material parameter information */
	void DisplayMaterialParameterInfo(UPrimitiveComponent* Component);

	/** Scan materials for CPD parameters and populate parameter names */
	void ScanMaterialParameters(UPrimitiveComponent* Component);

	/** Scan materials for CPD parameters with default values */
	void ScanMaterialParametersWithDefaults(UPrimitiveComponent* Component, TMap<int32, FString>& OutParameterNames, TMap<int32, float>& OutDefaultValues);

	/** Show message when no CPD parameters are found in materials */
	void ShowNoCPDParametersMessage();

	/** Create material slot selector */
	TSharedRef<SWidget> CreateMaterialSlotSelector();

	/** Handle material slot selection change */
	void OnMaterialSlotChanged(TSharedPtr<FString> NewSelection, ESelectInfo::Type SelectInfo);

	/** Get material slot options */
	void GetMaterialSlotOptions(TArray<TSharedPtr<FString>>& OutOptions);

	/** Create material slots display */
	void CreateMaterialSlotsDisplay();

	/** Create single material slot widget */
	TSharedRef<SWidget> CreateMaterialSlotWidget(int32 SlotIndex);

	/** Handle material asset selection */
	void OnMaterialSelected(const FAssetData& AssetData, int32 SlotIndex);

	/** Get current material for slot */
	UMaterialInterface* GetMaterialForSlot(int32 SlotIndex) const;

	/** Show message for multiple different materials */
	void ShowMultipleMaterialsMessage();

	/** Check if multiple objects have different materials */
	bool HasDifferentMaterials() const;

	/** Get CPD value for multiple objects (returns "Multiple Values" if different) */
	FString GetMultiObjectCPDValue(int32 Index) const;

	/** Display CPD header with buttons */
	void DisplayCPDHeader();

	/** Display CPD property data */
	void DisplayCPDPropertyData(UPrimitiveComponent* Component, FProperty* CPDProperty);

	/** Create widget for a single CPD element */
	void CreateCPDElementWidget(int32 Index, float Value);

	/** Create a CPD element row similar to Details panel */
	void CreateCPDElementRow(int32 Index, float Value, bool bDataFound);

	/** Handle adding CPD element */
	FReply OnAddCPDElement();

	/** Handle removing CPD element */
	FReply OnRemoveCPDElement();

	/** Handle editing CPD value */
	FReply OnEditCPDValue(int32 Index);

	/** Get CPD parameter name for display */
	FText GetCPDParameterName(int32 Index) const;

	/** Handle CPD value change */
	void OnCPDValueChanged(float NewValue, int32 Index);

	/** Handle CPD value committed */
	void OnCPDValueCommitted(float NewValue, ETextCommit::Type CommitType, int32 Index);

	/** Handle CPD value text changed */
	void OnCPDValueTextChanged(const FText& Text, int32 Index);

	/** Handle CPD value text committed */
	void OnCPDValueTextCommitted(const FText& Text, ETextCommit::Type CommitType, int32 Index);

	/** Handle right click on CPD parameter name */
	FReply OnCPDParameterRightClick(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent, int32 Index);

	/** Create context menu for CPD parameter */
	TSharedRef<SWidget> CreateCPDParameterContextMenu(int32 Index);

	/** Handle highlight CPD parameter */
	void OnHighlightCPDParameter(int32 Index);

	/** Handle reset CPD parameter to default value */
	FReply OnResetCPDToDefault(int32 Index);

	/** Refresh CPD display (for synchronization) */
	void RefreshCPDDisplay();

	/** Handle mouse button down to refresh display */
	virtual FReply OnMouseButtonDown(const FGeometry& MyGeometry, const FPointerEvent& MouseEvent) override;

	/** Handle focus received to refresh display */
	virtual FReply OnFocusReceived(const FGeometry& MyGeometry, const FFocusEvent& InFocusEvent) override;

	/** Tick function for periodic refresh */
	virtual void Tick(const FGeometry& AllottedGeometry, const double InCurrentTime, const float InDeltaTime) override;

	/** Handle selection change */
	void OnSelectionChanged(UObject* Object);

	/** Get currently selected primitive components */
	TArray<UPrimitiveComponent*> GetSelectedPrimitiveComponents() const;

	/** Set CPD value for selected components */
	void SetCPDValue(int32 Index, float Value);

	/** Get CPD value from the first selected component */
	float GetCPDValue(int32 Index) const;
	
	/** Apply Mesh Paint Texture Descriptor to CPD */
	FReply OnApplyMeshPaintToCPD();
	
	/** Clear all CPD values */
	FReply OnClearAllCPD();

private:
	/** Main content box */
	TSharedPtr<SVerticalBox> MainContentBox;

	/** CPD display box */
	TSharedPtr<SVerticalBox> CPDDisplayBox;

	/** Material slots display box */
	TSharedPtr<SVerticalBox> MaterialSlotsBox;

	/** Array of CPD spin boxes */
	TArray<TSharedPtr<SSpinBox<float>>> CPDSpinBoxes;

	/** Currently selected primitive components */
	TArray<TWeakObjectPtr<UPrimitiveComponent>> SelectedComponents;

	/** Current component being displayed */
	TWeakObjectPtr<UPrimitiveComponent> CurrentComponent;

	/** Material parameter data for CPD indices */
	TMap<int32, FString> CPDParameterNames;

	/** Material default values for CPD indices */
	TMap<int32, float> CPDDefaultValues;

	/** Highlighted CPD parameters */
	TSet<int32> HighlightedCPDParameters;

	/** Current selected material slot index */
	int32 CurrentMaterialSlot;

	/** Material slot options for combo box */
	TArray<TSharedPtr<FString>> MaterialSlotOptions;

	/** Selection change delegate handle */
	FDelegateHandle SelectionChangedHandle;

	/** Timer handle for periodic refresh */
	FTimerHandle RefreshTimerHandle;
};
