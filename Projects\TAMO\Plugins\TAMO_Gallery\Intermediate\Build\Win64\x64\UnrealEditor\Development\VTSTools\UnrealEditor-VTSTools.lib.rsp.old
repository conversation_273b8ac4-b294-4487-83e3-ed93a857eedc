/NOLOGO
/errorReport:prompt
/MACHINE:x64
/SUBSYSTEM:WINDOWS
/DEF
/NAME:"UnrealEditor-VTSTools.dll"
/IGNORE:4221
/NODEFAULTLIB
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\UnrealEd\SharedPCH.UnrealEd.Project.ValApi.Cpp20.h.obj"
"F:\TAMO_Streaming\Projects\TAMO\Plugins\VTSTools\Intermediate\Build\Win64\x64\UnrealEditor\Development\VTSTools\Module.VTSTools.cpp.obj"
"F:\TAMO_Streaming\Projects\TAMO\Plugins\VTSTools\Intermediate\Build\Win64\x64\UnrealEditor\Development\VTSTools\SCPDWindow.cpp.obj"
"F:\TAMO_Streaming\Projects\TAMO\Plugins\VTSTools\Intermediate\Build\Win64\x64\UnrealEditor\Development\VTSTools\SCPDWindow_Minimal.cpp.obj"
"F:\TAMO_Streaming\Projects\TAMO\Plugins\VTSTools\Intermediate\Build\Win64\x64\UnrealEditor\Development\VTSTools\VTSTools.cpp.obj"
"F:\TAMO_Streaming\Projects\TAMO\Plugins\VTSTools\Intermediate\Build\Win64\x64\UnrealEditor\Development\VTSTools\VTSToolsModule.cpp.obj"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\Default.rc2.res"
/OUT:"F:\TAMO_Streaming\Projects\TAMO\Plugins\VTSTools\Intermediate\Build\Win64\x64\UnrealEditor\Development\VTSTools\UnrealEditor-VTSTools.lib"