# VTS Tools 快速开始指南

## 1. 编译和启用插件

### 编译步骤
1. 打开 `F:\TAMO_Streaming\Projects\TAMO\TAMO.uproject`
2. 右键项目文件 -> "Generate Visual Studio project files"
3. 打开生成的 `.sln` 文件
4. 编译项目 (Build -> Build Solution)

### 启用插件
1. 启动Unreal编辑器
2. Edit -> Plugins
3. 搜索 "VTS Tools"
4. 勾选启用插件
5. 重启编辑器

## 2. 打开CPD窗口

1. 在菜单栏找到 "VTS Tools"
2. 点击 "CPD" 打开窗口
3. 窗口可以停靠到任意位置

## 3. 基本使用

### 编辑CPD数据
1. 在场景中选择一个Static Mesh Actor
2. 在CPD窗口中修改 CPD[0] 的值
3. 值会立即应用到选中的对象

### 批量操作
1. 选择多个对象 (Ctrl+点击)
2. 修改CPD值会应用到所有选中对象
3. 使用 "Clear All CPD" 清除所有值

## 4. Mesh Paint优化

### 转换现有Mesh Paint数据
1. 选择使用了Mesh Paint的对象
2. 点击 "Apply MeshPaint to CPD"
3. 数据会转换为CPD格式

### 在材质中使用CPD
1. 打开材质编辑器
2. 添加 "Custom Primitive Data" 节点
3. 设置Index为对应的CPD槽位
4. 连接到材质输出

## 5. 常见问题

### Q: 菜单没有显示？
A: 确保插件已启用并重启编辑器

### Q: CPD值没有生效？
A: 检查材质是否正确使用了Custom Primitive Data节点

### Q: 编译错误？
A: 重新生成项目文件并清理重建

## 6. 高级用法

### 自定义CPD布局
- CPD[0-1]: Mesh Paint纹理坐标
- CPD[2-3]: 自定义颜色数据
- CPD[4-7]: 预留给其他功能
- CPD[8]: Mesh Paint启用标志

### 性能优化建议
1. 相同CPD配置的对象会自动合批
2. 避免频繁修改CPD值
3. 在材质中使用分支节点优化性能

## 7. 技术支持

如有问题，请联系TAMO项目开发团队。

## 8. 文件位置

插件位置：`F:\TAMO_Streaming\Projects\TAMO\Plugins\VTSTools\`
