#include "TAMO_GalleryModule.h"
#include "LevelEditor.h"
#include "ToolMenus.h"
#include "Framework/Docking/TabManager.h"
#include "Widgets/Docking/SDockTab.h"
#include "Styling/AppStyle.h"
#include "SCPDWindow.h"
#include "SReferenceCheckerWindow.h"

#define LOCTEXT_NAMESPACE "FTAMO_GalleryModule"

const FName FTAMO_GalleryModule::CPDTabId(TEXT("CPDTab"));
const FName FTAMO_GalleryModule::ReferenceCheckerTabId(TEXT("ReferenceCheckerTab"));

void FTAMO_GalleryModule::StartupModule()
{
	// Register tab spawners
	FGlobalTabmanager::Get()->RegisterNomadTabSpawner(CPDTabId, FOnSpawnTab::CreateRaw(this, &FTAMO_GalleryModule::SpawnCPDTab))
		.SetDisplayName(LOCTEXT("CPDTabTitle", "CPD Manager"))
		.SetTooltipText(LOCTEXT("CPDTabTooltip", "Open the CPD Manager window"))
		.SetIcon(FSlateIcon(FAppStyle::GetAppStyleSetName(), "LevelEditor.Tabs.Details"));

	FGlobalTabmanager::Get()->RegisterNomadTabSpawner(ReferenceCheckerTabId, FOnSpawnTab::CreateRaw(this, &FTAMO_GalleryModule::SpawnReferenceCheckerTab))
		.SetDisplayName(LOCTEXT("ReferenceCheckerTabTitle", "Reference Checker"))
		.SetTooltipText(LOCTEXT("ReferenceCheckerTabTooltip", "Open the Reference Checker window"))
		.SetIcon(FSlateIcon(FAppStyle::GetAppStyleSetName(), "LevelEditor.Tabs.Outliner"));

	// Register menu extensions
	RegisterMenuExtensions();
}

void FTAMO_GalleryModule::ShutdownModule()
{
	// Unregister menu extensions
	UnregisterMenuExtensions();

	// Unregister tab spawners
	FGlobalTabmanager::Get()->UnregisterNomadTabSpawner(CPDTabId);
	FGlobalTabmanager::Get()->UnregisterNomadTabSpawner(ReferenceCheckerTabId);
}

void FTAMO_GalleryModule::RegisterMenuExtensions()
{
	// Wait for the ToolMenus module to be loaded
	if (!UToolMenus::IsToolMenuUIEnabled())
	{
		return;
	}

	CreateTAMOGalleryMenu();
}

void FTAMO_GalleryModule::UnregisterMenuExtensions()
{
	if (UToolMenus::IsToolMenuUIEnabled())
	{
		UToolMenus* ToolMenus = UToolMenus::Get();
		if (ToolMenus)
		{
			ToolMenus->RemoveMenu("MainFrame.MainMenu.VTSTools");
		}
	}
}

void FTAMO_GalleryModule::CreateTAMOGalleryMenu()
{
	UToolMenus* ToolMenus = UToolMenus::Get();
	if (!ToolMenus)
	{
		return;
	}

	// Create the main TAMO menu in the menu bar
	UToolMenu* MainMenu = ToolMenus->ExtendMenu("MainFrame.MainMenu");
	if (MainMenu)
	{
		FToolMenuSection& Section = MainMenu->FindOrAddSection("WindowLayout");
		
		// Add TAMO submenu
		Section.AddSubMenu(
			"VTSTools",
			LOCTEXT("VTSToolsMenu", "TAMO"),
			LOCTEXT("VTSToolsMenuTooltip", "TAMO functionality"),
			FNewToolMenuDelegate::CreateLambda([](UToolMenu* InMenu)
			{
				// Add CPD menu item
				FToolMenuSection& VTSSection = InMenu->AddSection("VTSToolsSection", LOCTEXT("VTSToolsSection", "Gallery Tools"));
				VTSSection.AddMenuEntry(
					"OpenCPD",
					LOCTEXT("OpenCPD", "CPD Manager"),
					LOCTEXT("OpenCPDTooltip", "Open CPD Manager window"),
					FSlateIcon(FAppStyle::GetAppStyleSetName(), "LevelEditor.Tabs.Details"),
					FUIAction(FExecuteAction::CreateLambda([]()
					{
						FGlobalTabmanager::Get()->TryInvokeTab(FTAMO_GalleryModule::CPDTabId);
					}))
				);

				VTSSection.AddMenuEntry(
					"OpenReferenceChecker",
					LOCTEXT("OpenReferenceChecker", "Reference Checker"),
					LOCTEXT("OpenReferenceCheckerTooltip", "Open Reference Checker window to analyze asset references"),
					FSlateIcon(FAppStyle::GetAppStyleSetName(), "LevelEditor.Tabs.Outliner"),
					FUIAction(FExecuteAction::CreateLambda([]()
					{
						FGlobalTabmanager::Get()->TryInvokeTab(FTAMO_GalleryModule::ReferenceCheckerTabId);
					}))
				);
			}),
			false,
			FSlateIcon(FAppStyle::GetAppStyleSetName(), "LevelEditor.Tabs.Details")
		);
	}
}

TSharedRef<SDockTab> FTAMO_GalleryModule::SpawnCPDTab(const FSpawnTabArgs& Args)
{
	return SNew(SDockTab)
		.TabRole(ETabRole::NomadTab)
		.Label(LOCTEXT("CPDTabTitle", "CPD Manager"))
		.ToolTipText(LOCTEXT("CPDTabTooltip", "CPD Manager Window"))
		[
			SNew(SCPDWindow)
		];
}

void FTAMO_GalleryModule::OpenCPDWindow()
{
	FGlobalTabmanager::Get()->TryInvokeTab(CPDTabId);
}

TSharedRef<SDockTab> FTAMO_GalleryModule::SpawnReferenceCheckerTab(const FSpawnTabArgs& Args)
{
	return SNew(SDockTab)
		.TabRole(ETabRole::NomadTab)
		.Label(LOCTEXT("ReferenceCheckerTabTitle", "Reference Checker"))
		.ToolTipText(LOCTEXT("ReferenceCheckerTabTooltip", "Reference Checker Window"))
		[
			SNew(SReferenceCheckerWindow)
		];
}

void FTAMO_GalleryModule::OpenReferenceCheckerWindow()
{
	FGlobalTabmanager::Get()->TryInvokeTab(ReferenceCheckerTabId);
}

#undef LOCTEXT_NAMESPACE

IMPLEMENT_MODULE(FTAMO_GalleryModule, TAMO_Gallery)
