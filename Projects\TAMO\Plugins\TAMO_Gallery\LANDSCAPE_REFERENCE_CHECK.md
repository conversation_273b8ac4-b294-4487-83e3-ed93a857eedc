# Landscape 引用检查功能

## 🏔️ **新增Landscape支持**

现在Reference Checker完全支持Landscape的引用检查，包括材质、图层和纹理等所有相关资源：

### **检查内容**
- ✅ **Landscape材质**: 主要的地形材质
- ✅ **Hole材质**: 地形洞洞材质
- ✅ **图层系统**: Landscape编辑图层
- ✅ **图层信息对象**: LayerInfoObject资源
- ✅ **资源有效性**: 所有引用资源的存在性验证

## 🔍 **Landscape检查逻辑**

### **1. Landscape材质检查**
```cpp
// 检查主要的Landscape材质
UMaterialInterface* LandscapeMaterial = LandscapeComp->GetLandscapeMaterial();
if (LandscapeMaterial)
{
    // 验证材质路径和有效性
    FString MaterialPath = LandscapeMaterial->GetPathName();
    bool bMaterialValid = IsAssetValid(MaterialPath);
}

// 检查Landscape Hole材质
UMaterialInterface* HoleMaterial = LandscapeComp->GetLandscapeHoleMaterial();
```

### **2. Landscape图层检查**
```cpp
// 获取Landscape代理
ALandscapeProxy* LandscapeProxy = LandscapeComp->GetLandscapeProxy();

// 检查编辑图层
const TArray<FLandscapeLayer>& LandscapeLayers = LandscapeProxy->GetLayers();
for (const FLandscapeLayer& Layer : LandscapeLayers)
{
    // 检查每个图层的名称和状态
}

// 检查图层信息对象
const TArray<ULandscapeLayerInfoObject*>& LayerInfoObjects = LandscapeProxy->EditorLayerSettings;
for (ULandscapeLayerInfoObject* LayerInfo : LayerInfoObjects)
{
    // 验证LayerInfoObject的有效性
}
```

## 📊 **Landscape检查结果示例**

### **完整的Landscape检查结果**
```
Actor: Landscape
├── LandscapeComponent_0_0
│   ├── Landscape Material: /Game/Landscape/Materials/M_Landscape ✅ Valid
│   ├── Landscape Hole Material: /Game/Landscape/Materials/M_LandscapeHole ✅ Valid
│   ├── Landscape Layer: Base Layer ✅ Valid
│   ├── Landscape Layer: Grass Layer ✅ Valid
│   ├── Landscape Layer: Rock Layer ✅ Valid
│   ├── Layer Info Object: /Game/Landscape/LayerInfos/Grass_LayerInfo ✅ Valid
│   ├── Layer Info Object: /Game/Landscape/LayerInfos/Rock_LayerInfo ✅ Valid
│   └── Layer Info Object: None ❌ Missing (Layer info object 2 is missing)
```

### **有问题的Landscape**
```
Actor: Landscape_Broken
├── LandscapeComponent_0_0
│   ├── Landscape Material: None ❌ Missing (No landscape material assigned)
│   ├── Landscape Hole Material: /Game/Missing/HoleMaterial ❌ Invalid (Landscape hole material is invalid)
│   └── Layer Info Objects: None ❌ Missing (No landscape layer info objects found)
```

### **简单的Landscape**
```
Actor: Landscape_Simple
├── LandscapeComponent_0_0
│   ├── Landscape Material: /Engine/EngineMaterials/DefaultLandscapeMaterial ✅ Valid
│   ├── Landscape Layer: Layer0 ✅ Valid
│   └── Layer Info Object: /Game/Landscape/DefaultLayerInfo ✅ Valid
```

## 🎯 **检查结果类型**

### **Landscape特有的引用类型**
- **Landscape Material**: 地形的主要材质
- **Landscape Hole Material**: 用于创建地形洞洞的材质
- **Landscape Layer**: 地形编辑图层
- **Layer Info Object**: 图层信息对象，定义图层属性
- **Landscape Proxy**: 地形代理对象

### **状态说明**
- 🟢 **Valid**: 资源存在且正确配置
- 🔴 **Missing**: 资源引用为空或未分配
- 🟡 **Invalid**: 资源路径存在但文件缺失或损坏

### **常见问题**
- "No landscape material assigned" - 地形没有分配材质
- "Landscape hole material is invalid" - 洞洞材质文件缺失
- "Layer info object X is missing" - 图层信息对象缺失
- "No landscape layer info objects found" - 没有配置图层信息
- "Cannot access landscape proxy" - 无法访问地形代理

## 🚀 **测试Landscape检查**

### **测试场景1: 创建新Landscape**
```
1. Landscape Mode -> Create
2. 创建一个基本的Landscape
3. 选择Landscape Actor
4. 运行Reference Checker
5. 应该看到:
   - Landscape Material: /Engine/EngineMaterials/DefaultLandscapeMaterial (Valid)
   - 基本的图层信息
```

### **测试场景2: 配置复杂Landscape**
```
1. 为Landscape分配自定义材质
2. 添加多个Paint图层
3. 配置LayerInfoObject
4. 运行Reference Checker
5. 应该看到:
   - 自定义材质路径
   - 多个图层信息
   - 各个LayerInfoObject的状态
```

### **测试场景3: 有问题的Landscape**
```
1. 清空Landscape的材质
2. 删除某些LayerInfoObject文件
3. 运行Reference Checker
4. 应该看到:
   - Missing状态的材质
   - Invalid状态的LayerInfoObject
```

## 🔧 **Landscape问题诊断**

### **材质问题**
```
问题: "No landscape material assigned"
解决: 在Landscape Details面板中分配Landscape Material

问题: "Landscape material is invalid"
解决: 检查材质文件是否存在，重新分配正确的材质
```

### **图层问题**
```
问题: "Layer info object X is missing"
解决: 
1. 在Landscape Paint模式中重新分配LayerInfoObject
2. 或者创建新的LayerInfoObject资源

问题: "No landscape layer info objects found"
解决: 在Landscape Paint模式中添加Paint图层
```

### **代理问题**
```
问题: "Cannot access landscape proxy"
解决: 
1. 确保选择的是正确的Landscape Actor
2. 检查Landscape是否正确创建
3. 重新创建Landscape如果必要
```

## 🎉 **功能优势**

### **✅ 全面检查**
- 覆盖Landscape的所有主要引用类型
- 检查材质、图层、纹理等所有相关资源
- 提供详细的问题诊断信息

### **✅ 准确诊断**
- 区分Missing、Invalid和Valid状态
- 提供具体的错误描述
- 显示详细的资源路径

### **✅ 易于理解**
- 清晰的层级结构显示
- 直观的颜色编码
- 具体的修复建议

## 🚀 **立即测试**

现在选择你的Landscape Actor并运行Reference Checker：

```bash
1. 编译项目 (Build -> Rebuild Solution)
2. 在场景中选择Landscape
3. 打开TAMO -> Reference Checker
4. 点击 "Check References"
5. 查看详细的Landscape引用信息
```

Reference Checker现在完全支持Landscape的引用检查！🏔️✨
