{"Version": "1.2", "Data": {"Source": "f:\\tamo_streaming\\projects\\tamo\\plugins\\vtstools\\source\\vtstools\\private\\vtstools.cpp", "ProvidedModule": "", "PCH": "f:\\tamo_streaming\\engine\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.cpp20.h.pch", "Includes": ["f:\\tamo_streaming\\projects\\tamo\\plugins\\vtstools\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\vtstools\\definitions.vtstools.h", "f:\\tamo_streaming\\projects\\tamo\\plugins\\vtstools\\source\\vtstools\\public\\vtstools.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}