#include "SReferenceCheckerWindow.h"
#include "Engine/Selection.h"
#include "Editor.h"
#include "Widgets/Text/STextBlock.h"
#include "Widgets/Input/SButton.h"
#include "Widgets/SBoxPanel.h"
#include "Widgets/Layout/SBorder.h"
#include "Widgets/Layout/SScrollBox.h"
#include "Widgets/Layout/SSeparator.h"
#include "Widgets/Views/STableRow.h"
#include "Widgets/Views/SHeaderRow.h"
#include "Widgets/Views/SListView.h"
#include "AssetRegistry/AssetRegistryModule.h"

#define LOCTEXT_NAMESPACE "ReferenceChecker"

void SReferenceCheckerWindow::Construct(const FArguments& InArgs)
{
	SelectedActorCount = 0;
	TotalReferences = 0;
	ValidReferences = 0;
	IssueReferences = 0;

	ChildSlot
	[
		SNew(SBorder)
		.BorderImage(FCoreStyle::Get().GetBrush("Border"))
		.Padding(8.0f)
		[
			SNew(SVerticalBox)
			
			// 标题
			+ SVerticalBox::Slot()
			.AutoHeight()
			.Padding(0, 0, 0, 10)
			[
				SNew(STextBlock)
				.Text(LOCTEXT("ReferenceCheckerTitle", "Reference Checker"))
				.Font(FCoreStyle::GetDefaultFontStyle("Bold", 16))
				.Justification(ETextJustify::Center)
			]
			
			// 选中Actor信息
			+ SVerticalBox::Slot()
			.AutoHeight()
			.Padding(0, 0, 0, 10)
			[
				SNew(SBorder)
				.BorderImage(FCoreStyle::Get().GetBrush("Border"))
				.Padding(8.0f)
				[
					SNew(STextBlock)
					.Text(this, &SReferenceCheckerWindow::GetSelectedActorCountText)
					.Font(FCoreStyle::GetDefaultFontStyle("Regular", 12))
				]
			]
			
			// 操作按钮
			+ SVerticalBox::Slot()
			.AutoHeight()
			.Padding(0, 0, 0, 10)
			[
				SNew(SHorizontalBox)

				+ SHorizontalBox::Slot()
				.FillWidth(1.0f)
				.Padding(5.0f)
				[
					SAssignNew(CheckButton, SButton)
					.Text(LOCTEXT("CheckReferences", "Check References"))
					.ToolTipText(LOCTEXT("CheckReferencesTooltip", "Check all references for selected actors"))
					.OnClicked(this, &SReferenceCheckerWindow::OnCheckReferencesClicked)
					.HAlign(HAlign_Center)
				]

				+ SHorizontalBox::Slot()
				.FillWidth(1.0f)
				.Padding(5.0f)
				[
					SAssignNew(ClearButton, SButton)
					.Text(LOCTEXT("ClearResults", "Clear Results"))
					.ToolTipText(LOCTEXT("ClearResultsTooltip", "Clear all check results"))
					.OnClicked(this, &SReferenceCheckerWindow::OnClearResultsClicked)
					.HAlign(HAlign_Center)
				]
			]
			
			// 分隔线
			+ SVerticalBox::Slot()
			.AutoHeight()
			.Padding(0, 0, 0, 10)
			[
				SNew(SSeparator)
			]
			
			// 检查结果统计
			+ SVerticalBox::Slot()
			.AutoHeight()
			.Padding(0, 0, 0, 10)
			[
				SNew(STextBlock)
				.Text(this, &SReferenceCheckerWindow::GetCheckResultsText)
				.Font(FCoreStyle::GetDefaultFontStyle("Bold", 12))
			]
			
			// 引用列表
			+ SVerticalBox::Slot()
			.FillHeight(1.0f)
			[
				SNew(SBorder)
				.BorderImage(FCoreStyle::Get().GetBrush("Border"))
				.Padding(4.0f)
				[
					SAssignNew(ReferenceListView, SListView<TSharedPtr<FReferenceInfo>>)
					.ListItemsSource(&ReferenceInfoList)
					.OnGenerateRow(this, &SReferenceCheckerWindow::OnGenerateReferenceRow)
					.HeaderRow
					(
						SNew(SHeaderRow)
						
						+ SHeaderRow::Column("Actor")
						.DefaultLabel(LOCTEXT("ActorColumn", "Actor"))
						.FillWidth(0.15f)
						
						+ SHeaderRow::Column("Component")
						.DefaultLabel(LOCTEXT("ComponentColumn", "Component"))
						.FillWidth(0.15f)
						
						+ SHeaderRow::Column("Type")
						.DefaultLabel(LOCTEXT("TypeColumn", "Type"))
						.FillWidth(0.12f)
						
						+ SHeaderRow::Column("Asset")
						.DefaultLabel(LOCTEXT("AssetColumn", "Asset Path"))
						.FillWidth(0.35f)
						
						+ SHeaderRow::Column("Status")
						.DefaultLabel(LOCTEXT("StatusColumn", "Status"))
						.FillWidth(0.1f)
						
						+ SHeaderRow::Column("Issue")
						.DefaultLabel(LOCTEXT("IssueColumn", "Issue"))
						.FillWidth(0.13f)
					)
				]
			]
		]
	];

	// 初始化统计信息
	SelectedActorCount = GetCurrentSelectedActorCount();
}

FReply SReferenceCheckerWindow::OnCheckReferencesClicked()
{
	// 清除之前的结果
	ReferenceInfoList.Empty();
	TotalReferences = 0;
	ValidReferences = 0;
	IssueReferences = 0;

	// 获取当前选中的Actor
	if (!GEditor || !GEditor->GetSelectedActors())
	{
		UE_LOG(LogTemp, Warning, TEXT("No editor or selected actors found"));
		return FReply::Handled();
	}

	USelection* SelectedActors = GEditor->GetSelectedActors();
	int32 ActorCount = 0;

	// 检查每个选中的Actor
	for (FSelectionIterator It(*SelectedActors); It; ++It)
	{
		if (AActor* Actor = Cast<AActor>(*It))
		{
			ActorCount++;
			CheckActorReferences(Actor, ReferenceInfoList);
		}
	}

	// 更新选中Actor数量
	SelectedActorCount = ActorCount;

	// 更新统计信息
	TotalReferences = ReferenceInfoList.Num();
	for (const auto& RefInfo : ReferenceInfoList)
	{
		if (RefInfo->bHasIssue)
		{
			IssueReferences++;
		}
		else
		{
			ValidReferences++;
		}
	}

	// 输出检查结果到日志
	UE_LOG(LogTemp, Log, TEXT("Reference Check Complete: %d actors, %d total references, %d issues"),
		ActorCount, TotalReferences, IssueReferences);

	// 刷新列表视图
	if (ReferenceListView.IsValid())
	{
		ReferenceListView->RequestListRefresh();
	}

	return FReply::Handled();
}

FReply SReferenceCheckerWindow::OnClearResultsClicked()
{
	ReferenceInfoList.Empty();
	TotalReferences = 0;
	ValidReferences = 0;
	IssueReferences = 0;

	if (ReferenceListView.IsValid())
	{
		ReferenceListView->RequestListRefresh();
	}

	return FReply::Handled();
}

int32 SReferenceCheckerWindow::GetCurrentSelectedActorCount() const
{
	int32 Count = 0;

	if (GEditor && GEditor->GetSelectedActors())
	{
		USelection* SelectedActors = GEditor->GetSelectedActors();
		for (FSelectionIterator It(*SelectedActors); It; ++It)
		{
			if (Cast<AActor>(*It))
			{
				Count++;
			}
		}
	}

	return Count;
}

FText SReferenceCheckerWindow::GetSelectedActorCountText() const
{
	int32 CurrentCount = GetCurrentSelectedActorCount();
	return FText::Format(LOCTEXT("SelectedActorCount", "Selected Actors: {0}"),
		FText::AsNumber(CurrentCount));
}

FText SReferenceCheckerWindow::GetCheckResultsText() const
{
	if (TotalReferences == 0)
	{
		return LOCTEXT("NoResults", "No check results yet. Select actors in the scene and click 'Check References' to analyze their asset references.");
	}

	return FText::Format(LOCTEXT("CheckResults", "Check Results - Total: {0} | Valid: {1} | Issues: {2}"),
		FText::AsNumber(TotalReferences),
		FText::AsNumber(ValidReferences),
		FText::AsNumber(IssueReferences));
}

void SReferenceCheckerWindow::CheckActorReferences(AActor* Actor, TArray<TSharedPtr<FReferenceInfo>>& OutReferences)
{
	if (!Actor)
	{
		return;
	}

	FString ActorName = Actor->GetName();

	// 获取Actor的所有组件
	TArray<UActorComponent*> Components = Actor->GetComponents().Array();

	for (UActorComponent* Component : Components)
	{
		if (!Component)
		{
			continue;
		}

		FString ComponentName = Component->GetName();
		FString ComponentClass = Component->GetClass()->GetName();

		// 检查不同类型的组件
		if (ComponentClass.Contains(TEXT("StaticMesh")))
		{
			CheckStaticMeshComponentReferences(Cast<UStaticMeshComponent>(Component), ActorName, OutReferences);
		}
		else if (ComponentClass.Contains(TEXT("SkeletalMesh")))
		{
			CheckSkeletalMeshComponentReferences(Cast<USkeletalMeshComponent>(Component), ActorName, OutReferences);
		}
		else if (ComponentClass.Contains(TEXT("Landscape")))
		{
			// 避免Cast，直接传递nullptr并使用组件信息
			CheckLandscapeComponentReferences(nullptr, ActorName, OutReferences);
		}
		else
		{
			// 其他组件类型
			TSharedPtr<FReferenceInfo> ComponentRef = MakeShareable(new FReferenceInfo(
				Component->GetPathName(),
				ComponentClass,
				TEXT("Valid"),
				false,
				TEXT(""),
				ActorName,
				ComponentName
			));
			OutReferences.Add(ComponentRef);
		}
	}

	// 如果没有找到任何组件，添加一个提示
	if (Components.Num() == 0)
	{
		TSharedPtr<FReferenceInfo> NoComponentRef = MakeShareable(new FReferenceInfo(
			TEXT("None"),
			TEXT("Component"),
			TEXT("Missing"),
			true,
			TEXT("Actor has no components"),
			ActorName,
			TEXT("N/A")
		));
		OutReferences.Add(NoComponentRef);
	}
}

void SReferenceCheckerWindow::CheckStaticMeshComponentReferences(UStaticMeshComponent* MeshComp, const FString& ActorName, TArray<TSharedPtr<FReferenceInfo>>& OutReferences)
{
	if (!MeshComp)
	{
		return;
	}

	FString ComponentName = MeshComp->GetName();

	// 检查静态网格资源
	UStaticMesh* StaticMesh = MeshComp->GetStaticMesh();
	if (StaticMesh)
	{
		FString MeshPath = StaticMesh->GetPathName();
		TSharedPtr<FReferenceInfo> MeshRef = MakeShareable(new FReferenceInfo(
			MeshPath,
			TEXT("Static Mesh"),
			TEXT("Valid"),
			false,
			TEXT(""),
			ActorName,
			ComponentName
		));
		OutReferences.Add(MeshRef);
	}
	else
	{
		// 没有静态网格
		TSharedPtr<FReferenceInfo> MeshRef = MakeShareable(new FReferenceInfo(
			TEXT("None"),
			TEXT("Static Mesh"),
			TEXT("Missing"),
			true,
			TEXT("No static mesh assigned"),
			ActorName,
			ComponentName
		));
		OutReferences.Add(MeshRef);
	}

	// 检查材质
	int32 MaterialCount = MeshComp->GetNumMaterials();
	for (int32 i = 0; i < MaterialCount; ++i)
	{
		UMaterialInterface* Material = MeshComp->GetMaterial(i);
		FString SlotName = ComponentName + FString::Printf(TEXT("[Slot %d]"), i);

		if (Material)
		{
			CheckMaterialReferences(Material, ActorName, SlotName, OutReferences);
		}
		else
		{
			// 材质槽为空
			TSharedPtr<FReferenceInfo> MaterialRef = MakeShareable(new FReferenceInfo(
				TEXT("None"),
				TEXT("Material"),
				TEXT("Missing"),
				true,
				FString::Printf(TEXT("Material slot %d is empty"), i),
				ActorName,
				SlotName
			));
			OutReferences.Add(MaterialRef);
		}
	}
}

void SReferenceCheckerWindow::CheckSkeletalMeshComponentReferences(USkeletalMeshComponent* SkelMeshComp, const FString& ActorName, TArray<TSharedPtr<FReferenceInfo>>& OutReferences)
{
	if (!SkelMeshComp)
	{
		return;
	}

	FString ComponentName = SkelMeshComp->GetName();

	// 检查骨骼网格资源
	USkeletalMesh* SkeletalMesh = SkelMeshComp->GetSkeletalMeshAsset();
	if (SkeletalMesh)
	{
		FString MeshPath = SkeletalMesh->GetPathName();
		TSharedPtr<FReferenceInfo> MeshRef = MakeShareable(new FReferenceInfo(
			MeshPath,
			TEXT("Skeletal Mesh"),
			TEXT("Valid"),
			false,
			TEXT(""),
			ActorName,
			ComponentName
		));
		OutReferences.Add(MeshRef);
	}
	else
	{
		// 没有骨骼网格
		TSharedPtr<FReferenceInfo> MeshRef = MakeShareable(new FReferenceInfo(
			TEXT("None"),
			TEXT("Skeletal Mesh"),
			TEXT("Missing"),
			true,
			TEXT("No skeletal mesh assigned"),
			ActorName,
			ComponentName
		));
		OutReferences.Add(MeshRef);
	}

	// 检查材质
	int32 MaterialCount = SkelMeshComp->GetNumMaterials();
	for (int32 i = 0; i < MaterialCount; ++i)
	{
		UMaterialInterface* Material = SkelMeshComp->GetMaterial(i);
		FString SlotName = ComponentName + FString::Printf(TEXT("[Slot %d]"), i);

		if (Material)
		{
			CheckMaterialReferences(Material, ActorName, SlotName, OutReferences);
		}
		else
		{
			// 材质槽为空
			TSharedPtr<FReferenceInfo> MaterialRef = MakeShareable(new FReferenceInfo(
				TEXT("None"),
				TEXT("Material"),
				TEXT("Missing"),
				true,
				FString::Printf(TEXT("Material slot %d is empty"), i),
				ActorName,
				SlotName
			));
			OutReferences.Add(MaterialRef);
		}
	}
}

void SReferenceCheckerWindow::CheckMaterialReferences(UMaterialInterface* Material, const FString& ActorName, const FString& ComponentName, TArray<TSharedPtr<FReferenceInfo>>& OutReferences)
{
	if (!Material)
	{
		// 材质为空
		TSharedPtr<FReferenceInfo> MaterialRef = MakeShareable(new FReferenceInfo(
			TEXT("None"),
			TEXT("Material"),
			TEXT("Missing"),
			true,
			TEXT("Material is missing"),
			ActorName,
			ComponentName
		));
		OutReferences.Add(MaterialRef);
		return;
	}

	// 检查材质本身
	FString MaterialPath = Material->GetPathName();
	bool bMaterialValid = IsAssetValid(MaterialPath);

	TSharedPtr<FReferenceInfo> MaterialRef = MakeShareable(new FReferenceInfo(
		MaterialPath,
		TEXT("Material"),
		bMaterialValid ? TEXT("Valid") : TEXT("Invalid"),
		!bMaterialValid,
		!bMaterialValid ? TEXT("Material asset is invalid or missing") : TEXT(""),
		ActorName,
		ComponentName
	));
	OutReferences.Add(MaterialRef);

	// 如果是材质实例，检查父材质
	if (UMaterialInstance* MaterialInstance = Cast<UMaterialInstance>(Material))
	{
		if (UMaterialInterface* Parent = MaterialInstance->Parent)
		{
			FString ParentPath = Parent->GetPathName();
			bool bParentValid = IsAssetValid(ParentPath);

			TSharedPtr<FReferenceInfo> ParentRef = MakeShareable(new FReferenceInfo(
				ParentPath,
				TEXT("Parent Material"),
				bParentValid ? TEXT("Valid") : TEXT("Invalid"),
				!bParentValid,
				!bParentValid ? TEXT("Parent material is invalid or missing") : TEXT(""),
				ActorName,
				ComponentName + TEXT("->Parent")
			));
			OutReferences.Add(ParentRef);
		}
		else
		{
			// 材质实例没有父材质
			TSharedPtr<FReferenceInfo> ParentRef = MakeShareable(new FReferenceInfo(
				TEXT("None"),
				TEXT("Parent Material"),
				TEXT("Missing"),
				true,
				TEXT("Material instance has no parent material"),
				ActorName,
				ComponentName + TEXT("->Parent")
			));
			OutReferences.Add(ParentRef);
		}
	}
}

void SReferenceCheckerWindow::CheckTextureReferences(UTexture* Texture, const FString& ActorName, const FString& ComponentName, TArray<TSharedPtr<FReferenceInfo>>& OutReferences)
{
	// 简化版本 - 只检查基本信息
	if (!Texture)
	{
		return;
	}

	FString TexturePath = Texture->GetPathName();
	TSharedPtr<FReferenceInfo> TextureRef = MakeShareable(new FReferenceInfo(
		TexturePath,
		TEXT("Texture"),
		TEXT("Valid"),
		false,
		TEXT(""),
		ActorName,
		ComponentName
	));
	OutReferences.Add(TextureRef);
}

void SReferenceCheckerWindow::CheckLandscapeComponentReferences(ULandscapeComponent* LandscapeComp, const FString& ActorName, TArray<TSharedPtr<FReferenceInfo>>& OutReferences)
{
	// 简化的Landscape检查 - 避免复杂的依赖
	FString ComponentName = TEXT("LandscapeComponent");

	// 添加Landscape检测信息
	TSharedPtr<FReferenceInfo> LandscapeRef = MakeShareable(new FReferenceInfo(
		TEXT("Landscape System"),
		TEXT("Landscape"),
		TEXT("Valid"),
		false,
		TEXT("Landscape component detected (detailed checking requires Landscape module)"),
		ActorName,
		ComponentName
	));
	OutReferences.Add(LandscapeRef);

	// 添加材质检查提示
	TSharedPtr<FReferenceInfo> MaterialRef = MakeShareable(new FReferenceInfo(
		TEXT("Landscape Materials"),
		TEXT("Landscape Material"),
		TEXT("Valid"),
		false,
		TEXT("Landscape materials present (use Landscape tools for detailed inspection)"),
		ActorName,
		ComponentName
	));
	OutReferences.Add(MaterialRef);

	// 添加图层检查提示
	TSharedPtr<FReferenceInfo> LayerRef = MakeShareable(new FReferenceInfo(
		TEXT("Landscape Layers"),
		TEXT("Landscape Layers"),
		TEXT("Valid"),
		false,
		TEXT("Landscape layer system detected (use Landscape Paint mode for layer details)"),
		ActorName,
		ComponentName
	));
	OutReferences.Add(LayerRef);
}

bool SReferenceCheckerWindow::IsAssetValid(const FString& AssetPath)
{
	// 基本检查
	if (AssetPath.IsEmpty() || AssetPath == TEXT("None"))
	{
		return false;
	}

	// 检查路径格式
	if (!AssetPath.StartsWith(TEXT("/Game/")) &&
		!AssetPath.StartsWith(TEXT("/Engine/")) &&
		!AssetPath.StartsWith(TEXT("/Script/")))
	{
		return false;
	}

	// 使用Asset Registry检查资源是否存在
	FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
	IAssetRegistry& AssetRegistry = AssetRegistryModule.Get();

	FAssetData AssetData = AssetRegistry.GetAssetByObjectPath(FSoftObjectPath(AssetPath));
	return AssetData.IsValid();
}

bool SReferenceCheckerWindow::HasCircularDependency(const FString& AssetPath, TSet<FString>& VisitedAssets)
{
	// 简化版本 - 暂时返回false
	return false;
}

TSharedRef<ITableRow> SReferenceCheckerWindow::OnGenerateReferenceRow(TSharedPtr<FReferenceInfo> Item, const TSharedRef<STableViewBase>& OwnerTable)
{
	return SNew(STableRow<TSharedPtr<FReferenceInfo>>, OwnerTable)
		[
			SNew(SHorizontalBox)

			// Actor列
			+ SHorizontalBox::Slot()
			.FillWidth(0.15f)
			.Padding(4, 2)
			[
				SNew(STextBlock)
				.Text(FText::FromString(Item->ActorName))
				.Font(FCoreStyle::GetDefaultFontStyle("Regular", 9))
			]

			// Component列
			+ SHorizontalBox::Slot()
			.FillWidth(0.15f)
			.Padding(4, 2)
			[
				SNew(STextBlock)
				.Text(FText::FromString(Item->ComponentName))
				.Font(FCoreStyle::GetDefaultFontStyle("Regular", 9))
			]

			// Type列
			+ SHorizontalBox::Slot()
			.FillWidth(0.12f)
			.Padding(4, 2)
			[
				SNew(STextBlock)
				.Text(FText::FromString(Item->ReferenceType))
				.Font(FCoreStyle::GetDefaultFontStyle("Regular", 9))
			]

			// Asset Path列
			+ SHorizontalBox::Slot()
			.FillWidth(0.35f)
			.Padding(4, 2)
			[
				SNew(STextBlock)
				.Text(FText::FromString(Item->AssetPath))
				.Font(FCoreStyle::GetDefaultFontStyle("Regular", 9))
				.AutoWrapText(true)
			]

			// Status列
			+ SHorizontalBox::Slot()
			.FillWidth(0.1f)
			.Padding(4, 2)
			[
				SNew(STextBlock)
				.Text(FText::FromString(Item->Status))
				.Font(FCoreStyle::GetDefaultFontStyle("Bold", 9))
				.ColorAndOpacity(this, &SReferenceCheckerWindow::GetReferenceStatusColor, Item)
			]

			// Issue列
			+ SHorizontalBox::Slot()
			.FillWidth(0.13f)
			.Padding(4, 2)
			[
				SNew(STextBlock)
				.Text(FText::FromString(Item->IssueDescription))
				.Font(FCoreStyle::GetDefaultFontStyle("Regular", 9))
				.ColorAndOpacity(FLinearColor::Red)
				.AutoWrapText(true)
			]
		];
}

FSlateColor SReferenceCheckerWindow::GetReferenceStatusColor(TSharedPtr<FReferenceInfo> Item) const
{
	if (!Item.IsValid())
	{
		return FLinearColor::White;
	}

	if (Item->Status == TEXT("Valid"))
	{
		return FLinearColor::Green;
	}
	else if (Item->Status == TEXT("Missing"))
	{
		return FLinearColor::Red;
	}
	else if (Item->Status == TEXT("Circular"))
	{
		return FLinearColor::Yellow;
	}

	return FLinearColor::White;
}

#undef LOCTEXT_NAMESPACE
