# Reference Checker 工具实现文档

## 🎯 **功能概述**

在TAMO Gallery插件中成功实现了Reference Checker工具，用于检查选中Actor的所有引用信息并高亮显示问题。

## 📁 **新增文件**

### **1. SReferenceCheckerWindow.h**
- **路径**: `Public/SReferenceCheckerWindow.h`
- **功能**: Reference Checker窗口的头文件定义
- **包含**: UI组件声明、引用信息结构体、检查方法声明

### **2. SReferenceCheckerWindow.cpp**
- **路径**: `Private/SReferenceCheckerWindow.cpp`
- **功能**: Reference Checker窗口的完整实现
- **包含**: UI构建、引用检查逻辑、问题检测算法

## 🏗️ **核心功能架构**

### **1. 引用信息结构体**
```cpp
struct FReferenceInfo
{
    FString AssetPath;          // 资源路径
    FString ReferenceType;      // 引用类型 (Material, Texture, Mesh等)
    FString Status;             // 引用状态 (Valid, Missing, Circular等)
    bool bHasIssue;            // 是否有问题
    FString IssueDescription;   // 问题描述
    FString ActorName;         // Actor名称
    FString ComponentName;     // 组件名称
};
```

### **2. 检查功能分类**

#### **A. 网格检查**
- ✅ **静态网格**: 检查StaticMeshComponent的StaticMesh引用
- ✅ **骨骼网格**: 检查SkeletalMeshComponent的SkeletalMesh引用
- ✅ **缺失检测**: 检测未分配的网格资源

#### **B. 材质检查**
- ✅ **材质引用**: 检查所有材质槽的材质引用
- ✅ **材质实例**: 检查MaterialInstance的父材质引用
- ✅ **材质参数**: 检查材质中的纹理参数引用

#### **C. 纹理检查**
- ✅ **纹理引用**: 检查材质中使用的所有纹理
- ✅ **纹理有效性**: 验证纹理资源是否存在

#### **D. 问题检测**
- ✅ **缺失引用**: 检测Missing或None的资源引用
- ✅ **循环依赖**: 检测资源间的循环依赖关系
- ✅ **无效资源**: 使用Asset Registry验证资源有效性

## 🎨 **用户界面设计**

### **1. 窗口布局**
```
┌─────────────────────────────────────────┐
│              Reference Checker           │
├─────────────────────────────────────────┤
│ Selected Actors: 3                      │
├─────────────────────────────────────────┤
│ [Check References] [Clear Results]      │
├─────────────────────────────────────────┤
│ Total: 15 | Valid: 12 | Issues: 3       │
├─────────────────────────────────────────┤
│ Actor │ Component │ Type │ Asset │ Status│
├─────────────────────────────────────────┤
│ Cube1 │ StaticMesh│ Mat  │ /Game...│ Valid │
│ Cube2 │ StaticMesh│ Tex  │ None    │Missing│
│ ...   │ ...       │ ...  │ ...     │ ...   │
└─────────────────────────────────────────┘
```

### **2. 颜色编码**
- 🟢 **绿色**: Valid - 引用正常
- 🔴 **红色**: Missing - 引用缺失
- 🟡 **黄色**: Circular - 循环依赖

### **3. 实时更新**
- ✅ **选择监听**: 实时监听Actor选择变化
- ✅ **按钮状态**: 根据选中Actor数量启用/禁用检查按钮
- ✅ **统计信息**: 实时显示检查结果统计

## 🔧 **菜单集成**

### **1. TAMO菜单项**
```
主菜单 -> TAMO -> Reference Checker
```

### **2. Tab管理**
- **Tab ID**: `ReferenceCheckerTab`
- **显示名称**: "Reference Checker"
- **图标**: LevelEditor.Tabs.Outliner
- **工具提示**: "Open Reference Checker window to analyze asset references"

## 🚀 **核心算法**

### **1. 引用检查流程**
```cpp
1. 获取选中的所有Actor
2. 遍历每个Actor的所有组件
3. 检查组件的网格引用
4. 检查组件的材质引用
5. 递归检查材质的纹理引用
6. 验证每个引用的有效性
7. 检测循环依赖
8. 生成问题报告
```

### **2. 循环依赖检测**
```cpp
bool HasCircularDependency(const FString& AssetPath, TSet<FString>& VisitedAssets)
{
    if (VisitedAssets.Contains(AssetPath))
        return true; // 发现循环
    
    VisitedAssets.Add(AssetPath);
    
    // 递归检查所有依赖
    for (const FAssetDependency& Dependency : GetDependencies(AssetPath))
    {
        if (HasCircularDependency(Dependency.AssetId.ToString(), VisitedAssets))
            return true;
    }
    
    VisitedAssets.Remove(AssetPath);
    return false;
}
```

### **3. Asset Registry集成**
```cpp
// 验证资源有效性
bool IsAssetValid(const FString& AssetPath)
{
    FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
    IAssetRegistry& AssetRegistry = AssetRegistryModule.Get();
    
    FAssetData AssetData = AssetRegistry.GetAssetByObjectPath(FSoftObjectPath(AssetPath));
    return AssetData.IsValid();
}
```

## 📊 **检查结果示例**

### **正常情况**
```
Actor: StaticMeshActor_1
├── StaticMesh: /Game/Meshes/Cube.Cube ✅ Valid
├── Material[0]: /Game/Materials/M_Basic.M_Basic ✅ Valid
│   ├── Parent: /Game/Materials/M_Master.M_Master ✅ Valid
│   └── Texture: /Game/Textures/T_Diffuse.T_Diffuse ✅ Valid
└── Material[1]: /Game/Materials/M_Detail.M_Detail ✅ Valid
```

### **问题情况**
```
Actor: StaticMeshActor_2
├── StaticMesh: None ❌ Missing (No static mesh assigned)
├── Material[0]: /Game/Materials/M_Missing.M_Missing ❌ Missing (Asset not found)
├── Material[1]: /Game/Materials/M_Circular.M_Circular ⚠️ Circular (Circular dependency)
└── Material[2]: None ❌ Missing (Material slot 2 is empty)
```

## 🎯 **使用方法**

### **1. 打开工具**
```
1. 在场景中选择一个或多个Actor
2. 点击主菜单 -> TAMO -> Reference Checker
3. Reference Checker窗口将打开
```

### **2. 执行检查**
```
1. 确认已选择要检查的Actor
2. 点击 "Check References" 按钮
3. 等待检查完成
4. 查看结果列表和统计信息
```

### **3. 分析结果**
```
1. 查看 "Total/Valid/Issues" 统计
2. 在列表中查看具体的引用信息
3. 关注红色和黄色标记的问题项
4. 根据Issue列的描述修复问题
```

## 🔄 **扩展功能建议**

### **1. 导出功能**
- 导出检查结果到CSV文件
- 生成问题报告文档

### **2. 自动修复**
- 自动移除无效引用
- 提供引用替换建议

### **3. 批量操作**
- 批量检查场景中所有Actor
- 按类型过滤检查结果

### **4. 性能优化**
- 异步检查大量Actor
- 缓存检查结果

## 状态: ✅ Reference Checker 完全实现

现在TAMO Gallery插件包含了完整的Reference Checker功能：
- ✅ **完整UI**: 专业的检查界面
- ✅ **全面检查**: 网格、材质、纹理引用检查
- ✅ **问题检测**: 缺失引用、循环依赖检测
- ✅ **实时更新**: 选择变化实时响应
- ✅ **菜单集成**: 完美集成到TAMO菜单
- ✅ **颜色高亮**: 直观的问题标识

Reference Checker工具现在可以帮助开发者快速识别和解决资源引用问题！🎉
