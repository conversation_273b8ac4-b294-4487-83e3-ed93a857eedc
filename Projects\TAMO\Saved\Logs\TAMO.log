﻿Log file open, 08/11/25 11:16:56
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: TAMO
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Display Control listening on port 1985
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="UE5-CL-0"
LogCsvProfiler: Display: Metadata set : engineversion="5.5.1-0+UE5"
LogCsvProfiler: Display: Metadata set : os="Windows 10 (22H2) [10.0.19045.6093] "
LogCsvProfiler: Display: Metadata set : cpu="GenuineIntel|Intel(R) Xeon(R) W-2255 CPU @ 3.70GHz"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" TAMO""
LogCsvProfiler: Display: Metadata set : loginid="0cf912ee4b52d61c1327948a4429657f"
LogCsvProfiler: Display: Metadata set : llm="0"
LogInit: Display: Project file not found: F:/TAMO_Streaming/TAMO/TAMO.uproject
LogInit: Display: 	Attempting to find via project info helper.
LogInit: Display: 	Found project file ../../../Projects/TAMO/TAMO.uproject.
LogStats: Stats thread started at 2.012204
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetActorFactory id: 0
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetSubObjectFactory id: 1
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: +8:00, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-55451B204949593DAE5080B50E3206E2
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [../../../Projects/TAMO/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogConfig: Display: Loading GDK ini files took 0.10 seconds
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogConfig: Display: Loading IOS ini files took 0.10 seconds
LogConfig: Display: Loading Nintendo ini files took 0.11 seconds
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogConfig: Display: Loading VulkanPC ini files took 0.11 seconds
LogConfig: Display: Loading Android ini files took 0.11 seconds
LogConfig: Display: Loading Mac ini files took 0.11 seconds
LogConfig: Display: Loading Sony ini files took 0.12 seconds
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogPluginManager: Looking for build plugins target receipt
LogConfig: Display: Loading Unix ini files took 0.12 seconds
LogConfig: Display: Loading TVOS ini files took 0.13 seconds
LogConfig: Display: Loading Windows ini files took 0.14 seconds
LogConfig: Display: Loading PS4 ini files took 0.14 seconds
LogConfig: Display: Loading Switch ini files took 0.14 seconds
LogConfig: Display: Loading VisionOS ini files took 0.14 seconds
LogConfig: Display: Loading LinuxArm64 ini files took 0.14 seconds
LogConfig: Display: Loading Linux ini files took 0.14 seconds
LogConfig: Display: Loading PS5 ini files took 0.15 seconds
LogConfig: Display: Loading XboxCommon ini files took 0.05 seconds
LogPluginManager: Found matching target receipt: ../../../Projects/TAMO/Binaries/Win64/TAMOEditor.target
LogPluginManager: Looking for enabled plugins target receipt
LogConfig: Display: Loading XB1 ini files took 0.07 seconds
LogConfig: Display: Loading WinGDK ini files took 0.08 seconds
LogConfig: Display: Loading XSX ini files took 0.07 seconds
LogPluginManager: Found matching target receipt: ../../../Projects/TAMO/Binaries/Win64/TAMOEditor.target
LogPluginManager: Mounting Engine plugin Bridge
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin Fab
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin PCG
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin WorldMetrics
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin DatasmithContent
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin PCGGeometryScriptInterop
LogPluginManager: Mounting Engine plugin PCGExternalDataInterop
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GeometryScripting
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin HairStrands
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin USDCore
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin Paper2D
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin ACLPlugin
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin LiveLink
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin BlueprintMaterialTextureNodes
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin Landmass
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin StudioTelemetry
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin Volumetrics
LogPluginManager: Mounting Engine plugin Niagara
LogAssetRegistry: Display: Asset registry cache read as 61.7 MiB from ../../../Projects/TAMO/Intermediate/CachedAssetRegistry_0.bin
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin HoldoutComposite
LogPluginManager: Mounting Engine plugin SQLiteCore
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Engine plugin ObjectMixer
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin ConcertMain
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Engine plugin ConcertSyncClient
LogPluginManager: Mounting Engine plugin ConcertSyncCore
LogPluginManager: Mounting Project plugin TAMO_Gallery
LogPluginManager: Mounting Project plugin VTS_Tools
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
LogEOSSDK: Initializing EOSSDK Version:1.16.4-36651368
LogInit: Using libcurl 8.4.0
LogInit:  - built for Windows
LogInit:  - supports SSL with OpenSSL/1.1.1t
LogInit:  - supports HTTP deflate (compression) using libz 1.3
LogInit:  - other features:
LogInit:      CURL_VERSION_SSL
LogInit:      CURL_VERSION_LIBZ
LogInit:      CURL_VERSION_IPV6
LogInit:      CURL_VERSION_ASYNCHDNS
LogInit:      CURL_VERSION_LARGEFILE
LogInit:      CURL_VERSION_HTTP2
LogInit:  CurlRequestOptions (configurable via config and command line):
LogInit:  - bVerifyPeer = true  - Libcurl will verify peer certificate
LogInit:  - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
LogInit:  - bDontReuseConnections = false  - Libcurl will reuse connections
LogInit:  - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
LogInit:  - LocalHostAddr = Default
LogInit:  - BufferSize = 65536
LogInit: CreateHttpThread using FCurlMultiPollEventLoopHttpThread
LogInit: Creating http thread with maximum ********** concurrent requests
LogInit: WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
LogOnline: OSS: Created online subsystem instance for: NULL
LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
LogWindows: Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
LogWindows: File 'WinPixGpuCapturer.dll' does not exist
PixWinPlugin: PIX capture plugin failed to initialize! Check that the process is launched from PIX.
LogConfig: Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
LogConfig: Set CVar [[renderdoc.AutoAttach:1]]
LogConfig: Set CVar [[renderdoc.BinaryPath:C:\Program Files\RenderDoc]]
RenderDocPlugin: locating RenderDoc library (renderdoc.dll)...
RenderDocPlugin: a RenderDoc library has been located at: C:\Program Files\RenderDoc/renderdoc.dll
RenderDocPlugin: RenderDoc library has been loaded (RenderDoc API v1.6.0).
RenderDocPlugin: plugin has been loaded successfully.
RenderDocPlugin: RenderDoc plugin is ready!
LogNFORDenoise: NFORDenoise function starting up
LogStudioTelemetry: Display: Starting StudioTelemetry Module
LogStudioTelemetry: Started StudioTelemetry Session
LogInit: ExecutableName: UnrealEditor.exe
LogInit: Build: UE5-CL-0
LogInit: Platform=WindowsEditor
LogInit: MachineId=0cf912ee4b52d61c1327948a4429657f
LogInit: DeviceId=
LogInit: Engine Version: 5.5.1-0+UE5
LogInit: Compatible Engine Version: 5.5.0-0+UE5
LogInit: Net CL: 0
LogInit: OS: Windows 10 (22H2) [10.0.19045.6093] (), CPU: Intel(R) Xeon(R) W-2255 CPU @ 3.70GHz, GPU: NVIDIA GeForce RTX 4070
LogInit: Compiled (64-bit): Apr 16 2025 17:50:44
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33135.00
LogInit: Build Configuration: Development
LogInit: Branch Name: UE5
LogInit: Command Line: 
LogInit: Base Directory: F:/TAMO_Streaming/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 0
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 36
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 170
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 15
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 10
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 119
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 51
LogDevObjectVersion:   UE5-PrivateFrosty (59DA5D52-1232-4948-B878-597870B8E98B): 8
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogDevObjectVersion:   Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
LogConfig: Branch 'EditorLayout' had been unloaded. Reloading on-demand took 1.40ms
LogConfig: Branch 'Bridge' had been unloaded. Reloading on-demand took 0.16ms
LogConfig: Branch 'ChaosCloth' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'CmdLinkServer' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'FastBuildController' had been unloaded. Reloading on-demand took 0.15ms
LogConfig: Branch 'MeshPainting' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'RenderGraphInsights' had been unloaded. Reloading on-demand took 0.16ms
LogConfig: Branch 'UbaController' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'WorldMetrics' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'XGEController' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'OodleNetwork' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'VariantManager' had been unloaded. Reloading on-demand took 0.15ms
LogConfig: Branch 'AlembicImporter' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'InterchangeEditor' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'ActorSequence' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'LevelSequenceEditor' had been unloaded. Reloading on-demand took 0.15ms
LogConfig: Branch 'SequencerScripting' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'TemplateSequence' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'NNERuntimeORT' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'NNEDenoiser' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'PCGExternalDataInterop' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'ActorLayerUtilities' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'LauncherChunkInstaller' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'AndroidDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'AndroidMoviePlayer' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'AndroidFileServer' had been unloaded. Reloading on-demand took 0.12ms
LogConfig: Branch 'AndroidPermission' had been unloaded. Reloading on-demand took 0.12ms
LogConfig: Branch 'AppleImageUtils' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'AppleMoviePlayer' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'ArchVisCharacter' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'AssetTags' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'AudioCapture' had been unloaded. Reloading on-demand took 0.12ms
LogConfig: Branch 'AudioSynesthesia' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'AudioWidgets' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'ChunkDownloader' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'CableComponent' had been unloaded. Reloading on-demand took 0.12ms
LogConfig: Branch 'ComputeFramework' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'CustomMeshComponent' had been unloaded. Reloading on-demand took 0.16ms
LogConfig: Branch 'ExampleDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'GeometryProcessing' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'GeometryCache' had been unloaded. Reloading on-demand took 0.12ms
LogConfig: Branch 'GeometryScripting' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'GoogleCloudMessaging' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'GooglePAD' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'IOSDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'InputDebugging' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'LinuxDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'LocationServicesBPLibrary' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'MeshModelingToolset' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'MobilePatchingUtils' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'MsQuic' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'ProceduralMeshComponent' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'PropertyAccessEditor' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'SignificanceManager' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'ResonanceAudio' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'SoundFields' had been unloaded. Reloading on-demand took 0.21ms
LogConfig: Branch 'WaveTable' had been unloaded. Reloading on-demand took 0.25ms
LogConfig: Branch 'WebMMoviePlayer' had been unloaded. Reloading on-demand took 0.20ms
LogConfig: Branch 'WindowsDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.15ms
LogConfig: Branch 'WindowsMoviePlayer' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'InterchangeTests' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'EOSShared' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'OnlineBase' had been unloaded. Reloading on-demand took 0.12ms
LogConfig: Branch 'OnlineServices' had been unloaded. Reloading on-demand took 0.12ms
LogConfig: Branch 'OnlineSubsystem' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'OnlineSubsystemNull' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'OnlineSubsystemUtils' had been unloaded. Reloading on-demand took 0.20ms
LogConfig: Branch 'AISupport' had been unloaded. Reloading on-demand took 0.20ms
LogConfig: Branch 'EnvironmentQueryEditor' had been unloaded. Reloading on-demand took 0.16ms
LogConfig: Branch 'ACLPlugin' had been unloaded. Reloading on-demand took 0.20ms
LogConfig: Branch 'AnimationData' had been unloaded. Reloading on-demand took 0.19ms
LogConfig: Branch 'AnimationModifierLibrary' had been unloaded. Reloading on-demand took 0.19ms
LogConfig: Branch 'BlendSpaceMotionAnalysis' had been unloaded. Reloading on-demand took 0.19ms
LogConfig: Branch 'ControlRigModules' had been unloaded. Reloading on-demand took 0.20ms
LogConfig: Branch 'ControlRigSpline' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'RigLogic' had been unloaded. Reloading on-demand took 0.16ms
LogConfig: Branch 'CameraShakePreviewer' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'EngineCameras' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'AnimationSharing' had been unloaded. Reloading on-demand took 0.21ms
LogConfig: Branch 'CLionSourceCodeAccess' had been unloaded. Reloading on-demand took 0.21ms
LogConfig: Branch 'DumpGPUServices' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'CodeLiteSourceCodeAccess' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'N10XSourceCodeAccess' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'GitSourceControl' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'KDevelopSourceCodeAccess' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'NullSourceCodeAccess' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'PerforceSourceControl' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'PixWinPlugin' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'PluginUtils' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'PlasticSourceControl' had been unloaded. Reloading on-demand took 0.19ms
LogConfig: Branch 'PropertyAccessNode' had been unloaded. Reloading on-demand took 0.19ms
LogConfig: Branch 'RenderDocPlugin' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'RiderSourceCodeAccess' had been unloaded. Reloading on-demand took 0.20ms
LogConfig: Branch 'SubversionSourceControl' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'TextureFormatOodle' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'VisualStudioSourceCodeAccess' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'VisualStudioCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'UObjectPlugin' had been unloaded. Reloading on-demand took 0.16ms
LogConfig: Branch 'XCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.19ms
LogConfig: Branch 'AssetManagerEditor' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'BlueprintMaterialTextureNodes' had been unloaded. Reloading on-demand took 0.19ms
LogConfig: Branch 'ColorGrading' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'ChangelistReview' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'BlueprintHeaderView' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'CurveEditorTools' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'CryptoKeys' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'DataValidation' had been unloaded. Reloading on-demand took 0.17ms
LogConfig: Branch 'EditorDebugTools' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'GameplayTagsEditor' had been unloaded. Reloading on-demand took 0.19ms
LogConfig: Branch 'EngineAssetDefinitions' had been unloaded. Reloading on-demand took 0.19ms
LogConfig: Branch 'FacialAnimation' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'GeometryMode' had been unloaded. Reloading on-demand took 0.19ms
LogConfig: Branch 'MaterialAnalyzer' had been unloaded. Reloading on-demand took 0.19ms
LogConfig: Branch 'MacGraphicsSwitching' had been unloaded. Reloading on-demand took 0.19ms
LogConfig: Branch 'MeshLODToolset' had been unloaded. Reloading on-demand took 0.18ms
LogConfig: Branch 'ModelingToolsEditorMode' had been unloaded. Reloading on-demand took 0.20ms
LogConfig: Branch 'ProxyLODPlugin' had been unloaded. Reloading on-demand took 0.19ms
LogConfig: Branch 'MobileLauncherProfileWizard' had been unloaded. Reloading on-demand took 0.21ms
LogConfig: Branch 'PluginBrowser' had been unloaded. Reloading on-demand took 0.21ms
LogConfig: Branch 'SequencerAnimTools' had been unloaded. Reloading on-demand took 0.20ms
LogConfig: Branch 'SpeedTreeImporter' had been unloaded. Reloading on-demand took 0.20ms
LogConfig: Branch 'UMGWidgetPreview' had been unloaded. Reloading on-demand took 0.20ms
LogConfig: Branch 'StylusInput' had been unloaded. Reloading on-demand took 0.19ms
LogConfig: Branch 'UVEditor' had been unloaded. Reloading on-demand took 0.19ms
LogConfig: Branch 'WorldPartitionHLODUtilities' had been unloaded. Reloading on-demand took 0.20ms
LogConfig: Branch 'AdvancedRenamer' had been unloaded. Reloading on-demand took 0.20ms
LogConfig: Branch 'AutomationUtils' had been unloaded. Reloading on-demand took 0.20ms
LogConfig: Branch 'BackChannel' had been unloaded. Reloading on-demand took 0.19ms
LogConfig: Branch 'ChaosCaching' had been unloaded. Reloading on-demand took 0.19ms
LogConfig: Branch 'ChaosEditor' had been unloaded. Reloading on-demand took 0.19ms
LogConfig: Branch 'ChaosSolverPlugin' had been unloaded. Reloading on-demand took 0.19ms
LogConfig: Branch 'ChaosUserDataPT' had been unloaded. Reloading on-demand took 0.16ms
LogConfig: Branch 'ChaosNiagara' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'CharacterAI' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'Dataflow' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'EditorPerformance' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'EditorDataStorage' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'EditorTelemetry' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'Fracture' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'GeometryCollectionPlugin' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'GeometryFlow' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'Landmass' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'LocalizableMessage' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'LowLevelNetTrace' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'MeshModelingToolsetExp' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'NFORDenoise' had been unloaded. Reloading on-demand took 0.12ms
LogConfig: Branch 'PlanarCut' had been unloaded. Reloading on-demand took 0.12ms
LogConfig: Branch 'PlatformCrypto' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'PythonScriptPlugin' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'SkeletalReduction' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'StudioTelemetry' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'Volumetrics' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'NiagaraSimCaching' had been unloaded. Reloading on-demand took 0.15ms
LogConfig: Branch 'AndroidMedia' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'AvfMedia' had been unloaded. Reloading on-demand took 0.12ms
LogConfig: Branch 'ImgMedia' had been unloaded. Reloading on-demand took 0.12ms
LogConfig: Branch 'MediaCompositing' had been unloaded. Reloading on-demand took 0.12ms
LogConfig: Branch 'MediaPlayerEditor' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'MediaPlate' had been unloaded. Reloading on-demand took 0.12ms
LogConfig: Branch 'WebMMedia' had been unloaded. Reloading on-demand took 0.12ms
LogConfig: Branch 'WmfMedia' had been unloaded. Reloading on-demand took 0.12ms
LogConfig: Branch 'TcpMessaging' had been unloaded. Reloading on-demand took 0.12ms
LogConfig: Branch 'UdpMessaging' had been unloaded. Reloading on-demand took 0.12ms
LogConfig: Branch 'BaseCharacterFXEditor' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'PortableObjectFileDataSource' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'SQLiteCore' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'XInputDevice' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'ObjectMixer' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'LightMixer' had been unloaded. Reloading on-demand took 0.12ms
LogConfig: Branch 'OnlineSubsystemGooglePlay' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'SkeletalMeshModelingTools' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'ConcertMain' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'OnlineSubsystemIOS' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'ContentBrowserAssetDataSource' had been unloaded. Reloading on-demand took 0.14ms
LogConfig: Branch 'ContentBrowserClassDataSource' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'ContentBrowserFileDataSource' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'ConcertSyncClient' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'TAMO_Gallery' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'VTS_Tools' had been unloaded. Reloading on-demand took 0.13ms
LogInit: Presizing for max 25165824 objects, including 0 objects not considered by GC.
LogStreaming: Display: AsyncLoading2 - Created: Event Driven Loader: false, Async Loading Thread: false, Async Post Load: false
LogStreaming: Display: AsyncLoading2 - Initialized
LogInit: Object subsystem initialized
LogConfig: Set CVar [[con.DebugEarlyDefault:1]]
LogConfig: CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
LogConfig: CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
LogConfig: CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
LogConfig: Set CVar [[r.setres:1280x720]]
LogConfig: CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
LogConfig: Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
LogConfig: CVar [[QualityLevelMapping:high]] deferred - dummy variable created
LogConfig: CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
LogConfig: Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
LogConfig: Set CVar [[r.Nanite.Streaming.AsyncCompute:0	; Temporary workaround for Nanite geometry corruption (FORT-805141)]]
LogConfig: CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
LogConfig: CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
LogConfig: Set CVar [[r.PSOPrecache.GlobalShaders:1]]
LogConfig: Set CVar [[r.DynamicRes.DynamicFrameTime:1]]
LogConfig: Set CVar [[r.VRS.EnableSoftware:1]]
LogConfig: Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[r.VSync:0]]
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[r.RHICmdBypass:0]]
[2025.08.11-03.16.58:716][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
[2025.08.11-03.16.58:716][  0]LogConfig: CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
[2025.08.11-03.16.58:716][  0]LogConfig: CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[r.GPUCrashDebugging:0]]
[2025.08.11-03.16.58:716][  0]LogConfig: CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[r.GenerateMeshDistanceFields:1]]
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[r.ReflectionMethod:1]]
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[r.SkinCache.CompileShaders:1]]
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[r.RayTracing:1]]
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[r.Shadow.Virtual.Enable:0]]
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange:1]]
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.HighlightContrastScale:0.8]]
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.ShadowContrastScale:0.8]]
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[r.VirtualTextures:1]]
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
[2025.08.11-03.16.58:716][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
[2025.08.11-03.16.58:716][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[s.AsyncLoadingThreadEnabled:1]]
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[s.EventDrivenLoaderEnabled:1]]
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[s.WarnIfTimeLimitExceeded:0]]
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[s.TimeLimitExceededMinTime:0.005]]
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[s.UseBackgroundLevelStreaming:1]]
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
[2025.08.11-03.16.58:716][  0]LogConfig: CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[s.FlushStreamingOnExit:1]]
[2025.08.11-03.16.58:716][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
[2025.08.11-03.16.58:716][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
[2025.08.11-03.16.58:716][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
[2025.08.11-03.16.58:716][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[gc.FlushStreamingOnGC:0]]
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[gc.AllowParallelGC:1]]
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[gc.MaxObjectsInEditor:25165824]]
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[gc.CreateGCClusters:1]]
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[gc.MinGCClusterSize:5]]
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[gc.AssetClustreringEnabled:0]]
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[gc.ActorClusteringEnabled:0]]
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
[2025.08.11-03.16.58:716][  0]LogConfig: Set CVar [[gc.GarbageEliminationEnabled:1]]
[2025.08.11-03.16.58:716][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
[2025.08.11-03.16.58:716][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
[2025.08.11-03.16.58:716][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
[2025.08.11-03.16.58:716][  0]LogConfig: Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
[2025.08.11-03.16.58:716][  0]LogConfig: CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
[2025.08.11-03.16.58:716][  0]LogConfig: CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
[2025.08.11-03.16.58:716][  0]LogConfig: CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
[2025.08.11-03.16.58:716][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
[2025.08.11-03.16.58:716][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
[2025.08.11-03.16.58:716][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
[2025.08.11-03.16.58:716][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
[2025.08.11-03.16.58:716][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
[2025.08.11-03.16.58:716][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
[2025.08.11-03.16.58:716][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
[2025.08.11-03.16.58:716][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
[2025.08.11-03.16.58:716][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
[2025.08.11-03.16.58:716][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
[2025.08.11-03.16.58:716][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
[2025.08.11-03.16.58:716][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
[2025.08.11-03.16.58:716][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
[2025.08.11-03.16.58:716][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
[2025.08.11-03.16.58:716][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
[2025.08.11-03.16.58:716][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
[2025.08.11-03.16.58:716][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
[2025.08.11-03.16.58:717][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
[2025.08.11-03.16.58:717][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
[2025.08.11-03.16.58:717][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
[2025.08.11-03.16.58:717][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
[2025.08.11-03.16.58:717][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
[2025.08.11-03.16.58:717][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
[2025.08.11-03.16.58:717][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
[2025.08.11-03.16.58:717][  0]LogConfig: CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
[2025.08.11-03.16.58:717][  0]LogConfig: CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
[2025.08.11-03.16.58:717][  0]LogConfig: CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
[2025.08.11-03.16.58:717][  0]LogConfig: CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
[2025.08.11-03.16.58:717][  0]LogConfig: CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
[2025.08.11-03.16.58:717][  0]LogConfig: CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
[2025.08.11-03.16.58:717][  0]LogConfig: CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
[2025.08.11-03.16.58:717][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
[2025.08.11-03.16.58:717][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
[2025.08.11-03.16.58:717][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
[2025.08.11-03.16.58:717][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
[2025.08.11-03.16.58:717][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
[2025.08.11-03.16.58:717][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
[2025.08.11-03.16.58:717][  0]LogConfig: CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
[2025.08.11-03.16.58:717][  0]LogConfig: CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
[2025.08.11-03.16.58:717][  0]LogConfig: CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
[2025.08.11-03.16.58:737][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.SkeletalMeshLODBias:0]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.ViewDistanceScale:1.0]]
[2025.08.11-03.16.58:737][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.FXAA.Quality:4]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.TemporalAA.Quality:2]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.TSR.History.R11G11B10:1]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.TSR.History.ScreenPercentage:200]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.TSR.History.UpdateQuality:3]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.TSR.ReprojectionField:1]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.TSR.Resurrection:1]]
[2025.08.11-03.16.58:737][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.LightFunctionQuality:1]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.ShadowQuality:5]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.Shadow.MaxResolution:2048]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.Shadow.RadiusThreshold:0.01]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.Shadow.DistanceScale:1.0]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.DistanceFieldShadowing:1]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.VolumetricFog:1]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.CapsuleShadows:1]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
[2025.08.11-03.16.58:737][  0]LogConfig: Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.DistanceFieldAO:1]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.AOQuality:2]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:300]]
[2025.08.11-03.16.58:737][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:200]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.SkyLight.RealTimeReflectionCapture:1]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.RayTracing.Scene.BuildMode:1]]
[2025.08.11-03.16.58:738][  0]LogConfig: Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.SSR.Quality:3]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.SSR.HalfResSceneColor:0]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.Lumen.Reflections.Allow:1]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.TonemapMode:1]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
[2025.08.11-03.16.58:738][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.MotionBlurQuality:4]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.MotionBlur.HalfResGather:0]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.AmbientOcclusionLevels:-1]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.DepthOfFieldQuality:2]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.RenderTargetPoolMin:400]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.LensFlareQuality:2]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.SceneColorFringeQuality:1]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.EyeAdaptationQuality:2]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.BloomQuality:5]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.Bloom.ScreenPercentage:50.000]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.FastBlurThreshold:100]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.Upscale.Quality:3]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.LightShaftQuality:1]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.Filter.SizeScale:1]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.Tonemapper.Quality:5]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.08.11-03.16.58:738][  0]LogConfig: Applying CVar settings from Section [TextureQuality@3] File [Scalability]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.Streaming.MipBias:0]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.Streaming.Boost:1]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.MaxAnisotropy:8]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.VT.MaxAnisotropy:8]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.Streaming.PoolSize:1000]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.08.11-03.16.58:738][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.TranslucencyLightingVolumeDim:64]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.RefractionQuality:2]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.SceneColorFormat:4]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.DetailMode:3]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.TranslucencyVolumeBlur:1]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.SSS.Scale:1]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.SSS.SampleSet:2]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.SSS.Quality:1]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.SSS.HalfRes:0]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.SSGI.Quality:3]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.EmitterSpawnRateScale:1.0]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.ParticleLightQuality:2]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[fx.Niagara.QualityLevel:3]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.Refraction.OffsetQuality:1]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:1]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.MaxStepCount:512]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:512]]
[2025.08.11-03.16.58:738][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[foliage.DensityScale:1.0]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[grass.DensityScale:1.0]]
[2025.08.11-03.16.58:738][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.08.11-03.16.58:738][  0]LogConfig: Set CVar [[r.AnisotropicMaterials:1]]
[2025.08.11-03.16.58:738][  0]LogConfig: Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
[2025.08.11-03.16.58:752][  0]LogRHI: Using Default RHI: D3D12
[2025.08.11-03.16.58:752][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.08.11-03.16.58:752][  0]LogRHI: Loading RHI module D3D12RHI
[2025.08.11-03.16.58:795][  0]LogD3D12RHI: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
[2025.08.11-03.16.58:795][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.08.11-03.16.59:335][  0]LogD3D12RHI: Found D3D12 adapter 0: NVIDIA GeForce RTX 4070 (VendorId: 10de, DeviceId: 2786, SubSysId: 40ee1458, Revision: 00a1
[2025.08.11-03.16.59:335][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.08.11-03.16.59:335][  0]LogD3D12RHI:   Adapter has 12012MB of dedicated video memory, 0MB of dedicated system memory, and 65378MB of shared system memory, 3 output[s]
[2025.08.11-03.16.59:335][  0]LogD3D12RHI:   Driver Version: 560.94 (internal:32.0.15.6094, unified:560.94)
[2025.08.11-03.16.59:335][  0]LogD3D12RHI:      Driver Date: 8-14-2024
[2025.08.11-03.16.59:369][  0]LogD3D12RHI: Found D3D12 adapter 1: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.08.11-03.16.59:369][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.2, binding tier 3, wave ops supported, atomic64 unsupported
[2025.08.11-03.16.59:369][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 65378MB of shared system memory, 0 output[s]
[2025.08.11-03.16.59:403][  0]LogD3D12RHI: Found D3D12 adapter 2: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.08.11-03.16.59:403][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.2, binding tier 3, wave ops supported, atomic64 unsupported
[2025.08.11-03.16.59:403][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 65378MB of shared system memory, 0 output[s]
[2025.08.11-03.16.59:669][  0]LogD3D12RHI: Found D3D12 adapter 3: NVIDIA GeForce RTX 4070 (VendorId: 10de, DeviceId: 2786, SubSysId: 40ee1458, Revision: 00a1
[2025.08.11-03.16.59:669][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.08.11-03.16.59:669][  0]LogD3D12RHI:   Adapter has 12012MB of dedicated video memory, 0MB of dedicated system memory, and 65378MB of shared system memory, 0 output[s]
[2025.08.11-03.16.59:669][  0]LogD3D12RHI:   Driver Version: 560.94 (internal:32.0.15.6094, unified:560.94)
[2025.08.11-03.16.59:669][  0]LogD3D12RHI:      Driver Date: 8-14-2024
[2025.08.11-03.16.59:930][  0]LogD3D12RHI: Found D3D12 adapter 4: NVIDIA GeForce RTX 4070 (VendorId: 10de, DeviceId: 2786, SubSysId: 40ee1458, Revision: 00a1
[2025.08.11-03.16.59:930][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.08.11-03.16.59:930][  0]LogD3D12RHI:   Adapter has 12012MB of dedicated video memory, 0MB of dedicated system memory, and 65378MB of shared system memory, 0 output[s]
[2025.08.11-03.16.59:931][  0]LogD3D12RHI:   Driver Version: 560.94 (internal:32.0.15.6094, unified:560.94)
[2025.08.11-03.16.59:931][  0]LogD3D12RHI:      Driver Date: 8-14-2024
[2025.08.11-03.17.00:174][  0]LogD3D12RHI: Found D3D12 adapter 5: NVIDIA GeForce RTX 4070 (VendorId: 10de, DeviceId: 2786, SubSysId: 40ee1458, Revision: 00a1
[2025.08.11-03.17.00:175][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.08.11-03.17.00:175][  0]LogD3D12RHI:   Adapter has 12012MB of dedicated video memory, 0MB of dedicated system memory, and 65378MB of shared system memory, 0 output[s]
[2025.08.11-03.17.00:175][  0]LogD3D12RHI:   Driver Version: 560.94 (internal:32.0.15.6094, unified:560.94)
[2025.08.11-03.17.00:175][  0]LogD3D12RHI:      Driver Date: 8-14-2024
[2025.08.11-03.17.00:176][  0]LogD3D12RHI: DirectX Agility SDK runtime found.
[2025.08.11-03.17.00:176][  0]LogD3D12RHI: Chosen D3D12 Adapter Id = 0
[2025.08.11-03.17.00:176][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.08.11-03.17.00:176][  0]LogInit: Selected Device Profile: [WindowsEditor]
[2025.08.11-03.17.00:176][  0]LogHAL: Display: Platform has ~ 128 GB [137107709952 / 137438953472 / 128], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
[2025.08.11-03.17.00:177][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [Windows]
[2025.08.11-03.17.00:177][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.11-03.17.00:177][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
[2025.08.11-03.17.00:177][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
[2025.08.11-03.17.00:177][  0]LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.08.11-03.17.00:178][  0]LogConfig: Set CVar [[r.DumpShaderDebugInfo:2]]
[2025.08.11-03.17.00:178][  0]LogConfig: Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
[2025.08.11-03.17.00:178][  0]LogConfig: Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
[2025.08.11-03.17.00:178][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.08.11-03.17.00:178][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.08.11-03.17.00:178][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.08.11-03.17.00:178][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.08.11-03.17.00:178][  0]LogConfig: Set CVar [[memory.MemoryPressureCriticalThresholdMB:512]]
[2025.08.11-03.17.00:178][  0]LogConfig: CVar [[r.CEFGPUAcceleration:1]] deferred - dummy variable created
[2025.08.11-03.17.00:178][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [../../../Projects/TAMO/Saved/Config/WindowsEditor/Editor.ini]
[2025.08.11-03.17.00:178][  0]LogInit: Computer: CNCDUW1174
[2025.08.11-03.17.00:178][  0]LogInit: User: zengyanjia
[2025.08.11-03.17.00:178][  0]LogInit: CPU Page size=4096, Cores=10
[2025.08.11-03.17.00:178][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.08.11-03.17.02:297][  0]LogMemory: Memory total: Physical=127.7GB (128GB approx) Virtual=157.7GB
[2025.08.11-03.17.02:297][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.08.11-03.17.02:297][  0]LogMemory: Process Physical Memory: 685.75 MB used, 702.20 MB peak
[2025.08.11-03.17.02:297][  0]LogMemory: Process Virtual Memory: 751.45 MB used, 751.45 MB peak
[2025.08.11-03.17.02:297][  0]LogMemory: Physical Memory: 69373.42 MB used,  61382.68 MB free, 130756.10 MB total
[2025.08.11-03.17.02:297][  0]LogMemory: Virtual Memory: 83349.33 MB used,  78126.77 MB free, 161476.09 MB total
[2025.08.11-03.17.02:297][  0]LogCsvProfiler: Display: Metadata set : extradevelopmentmemorymb="0"
[2025.08.11-03.17.02:318][  0]LogWindows: WindowsPlatformFeatures enabled
[2025.08.11-03.17.02:381][  0]LogChaosDD: Chaos Debug Draw Startup
[2025.08.11-03.17.02:381][  0]LogInit: Physics initialised using underlying interface: Chaos
[2025.08.11-03.17.02:411][  0]LogInit: Using OS detected language (en-US).
[2025.08.11-03.17.02:411][  0]LogInit: Using OS detected locale (en-US).
[2025.08.11-03.17.02:415][  0]LogTextLocalizationManager: No specific localization for 'en-US' exists, so 'en' will be used for the language.
[2025.08.11-03.17.02:416][  0]LogInit: Setting process to per monitor DPI aware
[2025.08.11-03.17.03:189][  0]LogWindowsTextInputMethodSystem: Available input methods:
[2025.08.11-03.17.03:189][  0]LogWindowsTextInputMethodSystem:   - English (United States) - (Keyboard).
[2025.08.11-03.17.03:189][  0]LogWindowsTextInputMethodSystem:   - Chinese (Simplified, China) - 搜狗拼音输入法 (TSF IME).
[2025.08.11-03.17.03:189][  0]LogWindowsTextInputMethodSystem: Activated input method: English (United States) - (Keyboard).
[2025.08.11-03.17.03:256][  0]LogSlate: New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
[2025.08.11-03.17.03:256][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.08.11-03.17.03:840][  0]LogRHI: Using Default RHI: D3D12
[2025.08.11-03.17.03:840][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.08.11-03.17.03:840][  0]LogRHI: Loading RHI module D3D12RHI
[2025.08.11-03.17.03:840][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.08.11-03.17.03:840][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.08.11-03.17.03:840][  0]LogD3D12RHI: Display: Creating D3D12 RHI with Max Feature Level SM6
[2025.08.11-03.17.03:841][  0]LogWindows: Attached monitors:
[2025.08.11-03.17.03:841][  0]LogWindows:     resolution: 1080x1920, work area: (2560, -174) -> (3640, 1706), device: '\\.\DISPLAY1'
[2025.08.11-03.17.03:841][  0]LogWindows:     resolution: 1920x1080, work area: (-1920, 194) -> (0, 1234), device: '\\.\DISPLAY2'
[2025.08.11-03.17.03:841][  0]LogWindows:     resolution: 2560x1440, work area: (0, 0) -> (2560, 1400), device: '\\.\DISPLAY3' [PRIMARY]
[2025.08.11-03.17.03:841][  0]LogWindows: Found 3 attached monitors.
[2025.08.11-03.17.03:841][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.08.11-03.17.03:842][  0]LogRHI: RHI Adapter Info:
[2025.08.11-03.17.03:842][  0]LogRHI:             Name: NVIDIA GeForce RTX 4070
[2025.08.11-03.17.03:842][  0]LogRHI:   Driver Version: 560.94 (internal:32.0.15.6094, unified:560.94)
[2025.08.11-03.17.03:842][  0]LogRHI:      Driver Date: 8-14-2024
[2025.08.11-03.17.03:842][  0]LogD3D12RHI:     GPU DeviceId: 0x2786 (for the marketing name, search the web for "GPU Device Id")
[2025.08.11-03.17.03:842][  0]LogD3D12RHI: InitD3DDevice: -D3DDebug = off -D3D12GPUValidation = off
[2025.08.11-03.17.04:083][  0]LogNvidiaAftermath: Aftermath initialized
[2025.08.11-03.17.04:083][  0]LogD3D12RHI: Emitting draw events for PIX profiling.
[2025.08.11-03.17.04:176][  0]LogNvidiaAftermath: Aftermath enabled but failed to initialize (bad0000a).
[2025.08.11-03.17.04:176][  0]LogD3D12RHI: ID3D12Device1 is supported.
[2025.08.11-03.17.04:176][  0]LogD3D12RHI: ID3D12Device2 is supported.
[2025.08.11-03.17.04:176][  0]LogD3D12RHI: ID3D12Device3 is supported.
[2025.08.11-03.17.04:176][  0]LogD3D12RHI: ID3D12Device4 is supported.
[2025.08.11-03.17.04:176][  0]LogD3D12RHI: ID3D12Device5 is supported.
[2025.08.11-03.17.04:176][  0]LogD3D12RHI: ID3D12Device6 is supported.
[2025.08.11-03.17.04:176][  0]LogD3D12RHI: ID3D12Device7 is supported.
[2025.08.11-03.17.04:176][  0]LogD3D12RHI: ID3D12Device8 is supported.
[2025.08.11-03.17.04:176][  0]LogD3D12RHI: ID3D12Device9 is supported.
[2025.08.11-03.17.04:176][  0]LogD3D12RHI: ID3D12Device10 is supported.
[2025.08.11-03.17.04:176][  0]LogD3D12RHI: ID3D12Device11 is supported.
[2025.08.11-03.17.04:176][  0]LogD3D12RHI: ID3D12Device12 is supported.
[2025.08.11-03.17.04:176][  0]LogD3D12RHI: Bindless resources are supported
[2025.08.11-03.17.04:176][  0]LogD3D12RHI: Stencil ref from pixel shader is not supported
[2025.08.11-03.17.04:176][  0]LogD3D12RHI: Raster order views are supported
[2025.08.11-03.17.04:176][  0]LogD3D12RHI: Wave Operations are supported (wave size: min=32 max=32).
[2025.08.11-03.17.04:176][  0]LogD3D12RHI: D3D12 ray tracing tier 1.1 and bindless resources are supported.
[2025.08.11-03.17.04:177][  0]LogD3D12RHI: Mesh shader tier 1.0 is supported
[2025.08.11-03.17.04:177][  0]LogD3D12RHI: AtomicInt64OnTypedResource is supported
[2025.08.11-03.17.04:177][  0]LogD3D12RHI: AtomicInt64OnGroupShared is supported
[2025.08.11-03.17.04:177][  0]LogD3D12RHI: AtomicInt64OnDescriptorHeapResource is supported
[2025.08.11-03.17.04:177][  0]LogD3D12RHI: Shader Model 6.6 atomic64 is supported
[2025.08.11-03.17.04:680][  0]LogRHI: Display: Setting GPU Capture Options: 1
[2025.08.11-03.17.04:680][  0]LogRHI: Display: Toggling showmaterialdrawevents: 1
[2025.08.11-03.17.04:680][  0]LogRHI: Display: Enabling full RDG events (r.RDG.Events 3)
[2025.08.11-03.17.04:723][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x00000947C7A4CB00)
[2025.08.11-03.17.04:723][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x00000947C7A4CD80)
[2025.08.11-03.17.04:723][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x00000947C7A4D000)
[2025.08.11-03.17.04:723][  0]LogD3D12RHI: Display: Not using pipeline state disk cache per r.D3D12.PSO.DiskCache=0
[2025.08.11-03.17.04:723][  0]LogD3D12RHI: Display: Not using driver-optimized pipeline state disk cache per r.D3D12.PSO.DriverOptimizedDiskCache=0
[2025.08.11-03.17.04:726][  0]LogD3D12RHI: Warning: Failed to query NVIDIA driver version
[2025.08.11-03.17.04:726][  0]LogRHI: Texture pool is 7083 MB (70% of 10119 MB)
[2025.08.11-03.17.04:726][  0]LogD3D12RHI: Async texture creation enabled
[2025.08.11-03.17.04:726][  0]LogD3D12RHI: RHI has support for 64 bit atomics
[2025.08.11-03.17.04:739][  0]LogVRS: Current RHI supports per-draw and screenspace Variable Rate Shading
[2025.08.11-03.17.04:743][  0]LogInit: Initializing FReadOnlyCVARCache
[2025.08.11-03.17.04:791][  0]LogTurnkeySupport: Running Turnkey SDK detection: ' -ScriptsForProject="F:/TAMO_Streaming/Projects/TAMO/TAMO.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="F:/TAMO_Streaming/Projects/TAMO/Intermediate/TurnkeyReport_0.log" -log="F:/TAMO_Streaming/Projects/TAMO/Intermediate/TurnkeyLog_0.log" -project="F:/TAMO_Streaming/Projects/TAMO/TAMO.uproject"  -platform=all'
[2025.08.11-03.17.04:791][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""F:/TAMO_Streaming/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="F:/TAMO_Streaming/Projects/TAMO/TAMO.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="F:/TAMO_Streaming/Projects/TAMO/Intermediate/TurnkeyReport_0.log" -log="F:/TAMO_Streaming/Projects/TAMO/Intermediate/TurnkeyLog_0.log" -project="F:/TAMO_Streaming/Projects/TAMO/TAMO.uproject"  -platform=all" ]
[2025.08.11-03.17.04:949][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatASTC
[2025.08.11-03.17.04:949][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatDXT
[2025.08.11-03.17.04:949][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatETC2
[2025.08.11-03.17.04:949][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
[2025.08.11-03.17.04:949][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatUncompressed
[2025.08.11-03.17.04:949][  0]LogTextureFormatOodle: Display: Oodle Texture TFO init; latest sdk version = 2.9.12
[2025.08.11-03.17.04:949][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.12.dll
[2025.08.11-03.17.04:962][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
[2025.08.11-03.17.04:974][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatOodle
[2025.08.11-03.17.05:064][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android'
[2025.08.11-03.17.05:064][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTC'
[2025.08.11-03.17.05:064][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXT'
[2025.08.11-03.17.05:064][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2'
[2025.08.11-03.17.05:064][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AndroidClient'
[2025.08.11-03.17.05:064][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTCClient'
[2025.08.11-03.17.05:064][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXTClient'
[2025.08.11-03.17.05:065][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2Client'
[2025.08.11-03.17.05:065][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_Multi'
[2025.08.11-03.17.05:065][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_MultiClient'
[2025.08.11-03.17.05:166][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Linux'
[2025.08.11-03.17.05:166][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxEditor'
[2025.08.11-03.17.05:166][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxServer'
[2025.08.11-03.17.05:166][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxClient'
[2025.08.11-03.17.05:236][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64'
[2025.08.11-03.17.05:236][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Server'
[2025.08.11-03.17.05:236][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Client'
[2025.08.11-03.17.05:382][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Windows'
[2025.08.11-03.17.05:382][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsEditor'
[2025.08.11-03.17.05:382][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsServer'
[2025.08.11-03.17.05:382][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsClient'
[2025.08.11-03.17.05:655][  0]LogTargetPlatformManager: Display: Building Assets For WindowsEditor
[2025.08.11-03.17.06:040][  0]LogRendererCore: Ray tracing is enabled (dynamic). Reason: r.RayTracing=1 and r.RayTracing.EnableOnDemand=1.
[2025.08.11-03.17.06:040][  0]LogRendererCore: Ray tracing shaders are enabled.
[2025.08.11-03.17.06:043][  0]LogDerivedDataCache: Display: Memory: Max Cache Size: -1 MB
[2025.08.11-03.17.06:043][  0]LogDerivedDataCache: FDerivedDataBackendGraph: Pak pak cache file ../../../Projects/TAMO/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.08.11-03.17.06:043][  0]LogDerivedDataCache: Unable to find inner node Pak for hierarchy Root.
[2025.08.11-03.17.06:043][  0]LogDerivedDataCache: FDerivedDataBackendGraph: EnginePak pak cache file ../../../Engine/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.08.11-03.17.06:043][  0]LogDerivedDataCache: Unable to find inner node EnginePak for hierarchy Root.
[2025.08.11-03.17.06:045][  0]LogZenServiceInstance: Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
[2025.08.11-03.17.06:045][  0]LogZenServiceInstance: InTree version at 'F:/TAMO_Streaming/Engine/Binaries/Win64/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.08.11-03.17.06:046][  0]LogZenServiceInstance: Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.6.6-202504241958-windows-x64-release-f090a03'
[2025.08.11-03.17.06:047][  0]LogZenServiceInstance: No current process using the data dir found, launching a new instance
[2025.08.11-03.17.06:047][  0]LogZenServiceInstance: Display: Launching executable 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe', working dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install', data dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data', args '--port 8558 --data-dir "C:\Users\<USER>\AppData\Local\UnrealEngine\Common\Zen\Data" --http asio --gc-cache-duration-seconds 1209600 --gc-interval-seconds 21600 --gc-low-diskspace-threshold 2147483648 --quiet --http-forceloopback --owner-pid 65872 --child-id Zen_65872_Startup'
[2025.08.11-03.17.06:848][  0]LogZenServiceInstance: Display: Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
[2025.08.11-03.17.06:848][  0]LogZenServiceInstance: Local ZenServer AutoLaunch initialization completed in 0.803 seconds
[2025.08.11-03.17.06:862][  0]LogDerivedDataCache: Display: ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
[2025.08.11-03.17.06:886][  0]LogDerivedDataCache: ../../../Engine/DerivedDataCache: Speed tests took 0.02 seconds.
[2025.08.11-03.17.06:887][  0]LogDerivedDataCache: Display: ../../../Engine/DerivedDataCache: Performance: Latency=0.01ms. RandomReadSpeed=85.77MBs, RandomWriteSpeed=74.28MBs. Assigned SpeedClass 'Local'
[2025.08.11-03.17.06:888][  0]LogDerivedDataCache: Local: Using data cache path ../../../Engine/DerivedDataCache: DeleteOnly
[2025.08.11-03.17.06:888][  0]LogDerivedDataCache: ZenShared: Disabled because Host is set to 'None'
[2025.08.11-03.17.06:888][  0]LogDerivedDataCache: Unable to find inner node ZenShared for hierarchy Root.
[2025.08.11-03.17.06:888][  0]LogDerivedDataCache: Shared: Disabled because no path is configured.
[2025.08.11-03.17.06:888][  0]LogDerivedDataCache: Unable to find inner node Shared for hierarchy Root.
[2025.08.11-03.17.06:888][  0]LogDerivedDataCache: Cloud: Disabled because Host is set to 'None'
[2025.08.11-03.17.06:888][  0]LogDerivedDataCache: Unable to find inner node Cloud for hierarchy Root.
[2025.08.11-03.17.06:889][  0]LogShaderCompilers: Guid format shader working directory is 28 characters bigger than the processId version (../../../Projects/TAMO/Intermediate/Shaders/WorkingDirectory/65872/).
[2025.08.11-03.17.06:889][  0]LogShaderCompilers: Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/8B7F805A4E2BEEBB4054CABF78A3B751/'.
[2025.08.11-03.17.06:908][  0]LogXGEController: Cleaning working directory: C:/Users/<USER>/AppData/Local/Temp/UnrealXGEWorkingDir/
[2025.08.11-03.17.06:933][  0]LogXGEController: Display: Initialized XGE controller. XGE tasks will not be spawned on this machine.
[2025.08.11-03.17.06:933][  0]LogShaderCompilers: Display: Using XGE Controller for Shader Compilation.
[2025.08.11-03.17.06:956][  0]LogShaderCompilers: Display: Compiling shader autogen file: ../../../Projects/TAMO/Intermediate/ShaderAutogen/PCD3D_SM6/AutogenShaderHeaders.ush
[2025.08.11-03.17.06:957][  0]LogShaderCompilers: Display: Autogen file is unchanged, skipping write.
[2025.08.11-03.17.11:856][  0]LogSlate: Using FreeType 2.10.0
[2025.08.11-03.17.11:859][  0]LogSlate: SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
[2025.08.11-03.17.11:863][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.08.11-03.17.11:864][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.08.11-03.17.11:932][  0]LogAssetRegistry: FAssetRegistry took 0.0094 seconds to start up
[2025.08.11-03.17.11:935][  0]LogEditorDomain: Display: EditorDomain is Disabled
[2025.08.11-03.17.11:954][  0]LogAssetRegistry: Display: AssetDataGatherer spent 0.010s loading caches ../../../Projects/TAMO/Intermediate/CachedAssetRegistry_*.bin.
[2025.08.11-03.17.12:312][  0]LogStreaming: Display: FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
[2025.08.11-03.17.12:317][  0]LogTextureEncodingSettings: Display: Texture Encode Speed: FinalIfAvailable (editor).
[2025.08.11-03.17.12:317][  0]LogTextureEncodingSettings: Display: Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
[2025.08.11-03.17.12:317][  0]LogTextureEncodingSettings: Display: Shared linear texture encoding: Disabled
[2025.08.11-03.17.12:395][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64Editor not found.
[2025.08.11-03.17.12:395][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64 not found.
[2025.08.11-03.17.12:464][  0]LogDeviceProfileManager: Active device profile: [00000947E6E0D000][00000947E75D8000 66] WindowsEditor
[2025.08.11-03.17.12:464][  0]LogCsvProfiler: Display: Metadata set : deviceprofile="WindowsEditor"
[2025.08.11-03.17.12:559][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.11-03.17.12:560][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 18 to allow recursive sync load to finish
[2025.08.11-03.17.12:560][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.08.11-03.17.12:560][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.11-03.17.12:561][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 19 to allow recursive sync load to finish
[2025.08.11-03.17.12:561][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.08.11-03.17.12:561][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.11-03.17.12:563][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 20 to allow recursive sync load to finish
[2025.08.11-03.17.12:563][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.08.11-03.17.12:563][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.11-03.17.12:563][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 21 to allow recursive sync load to finish
[2025.08.11-03.17.12:563][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.08.11-03.17.12:563][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.11-03.17.12:567][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.11-03.17.12:567][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 23 to allow recursive sync load to finish
[2025.08.11-03.17.12:567][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.08.11-03.17.12:567][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.11-03.17.12:567][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 24 to allow recursive sync load to finish
[2025.08.11-03.17.12:567][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.08.11-03.17.12:567][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.11-03.17.12:568][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.11-03.17.12:568][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 26 to allow recursive sync load to finish
[2025.08.11-03.17.12:568][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.08.11-03.17.12:569][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.11-03.17.12:569][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 27 to allow recursive sync load to finish
[2025.08.11-03.17.12:569][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.08.11-03.17.12:569][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.11-03.17.12:573][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDiffuse with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.08.11-03.17.12:573][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.11-03.17.12:573][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 29 to allow recursive sync load to finish
[2025.08.11-03.17.12:573][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.08.11-03.17.12:573][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.11-03.17.12:573][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 30 to allow recursive sync load to finish
[2025.08.11-03.17.12:573][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.08.11-03.17.12:574][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.08.11-03.17.12:575][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 28 to allow recursive sync load to finish
[2025.08.11-03.17.12:575][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.08.11-03.17.12:575][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.11-03.17.12:575][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 31 to allow recursive sync load to finish
[2025.08.11-03.17.12:575][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.08.11-03.17.12:575][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 25 to allow recursive sync load to finish
[2025.08.11-03.17.12:575][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.08.11-03.17.12:575][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.11-03.17.12:575][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 32 to allow recursive sync load to finish
[2025.08.11-03.17.12:575][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.08.11-03.17.12:576][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 22 to allow recursive sync load to finish
[2025.08.11-03.17.12:576][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.08.11-03.17.12:576][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.08.11-03.17.12:576][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 33 to allow recursive sync load to finish
[2025.08.11-03.17.12:576][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.08.11-03.17.13:670][  0]LogMeshReduction: Display: Using QuadricMeshReduction for automatic static mesh reduction
[2025.08.11-03.17.13:670][  0]LogMeshReduction: Display: Using SkeletalMeshReduction for automatic skeletal mesh reduction
[2025.08.11-03.17.13:670][  0]LogMeshReduction: Display: Using ProxyLODMeshReduction for automatic mesh merging
[2025.08.11-03.17.13:670][  0]LogMeshReduction: Display: No distributed automatic mesh merging module available
[2025.08.11-03.17.13:670][  0]LogMeshMerging: No distributed automatic mesh merging module available
[2025.08.11-03.17.14:555][  0]LogConfig: Branch 'PIEPreviewSettings' had been unloaded. Reloading on-demand took 1.70ms
[2025.08.11-03.17.14:609][  0]LogConfig: Branch 'GameplayTagsList' had been unloaded. Reloading on-demand took 1.65ms
[2025.08.11-03.17.14:643][  0]LogConfig: Branch 'TemplateDefs' had been unloaded. Reloading on-demand took 1.68ms
[2025.08.11-03.17.14:648][  0]LogConfig: Branch 'TemplateCategories' had been unloaded. Reloading on-demand took 1.57ms
[2025.08.11-03.17.15:224][  0]LogVirtualization: Display: VirtualizationSystem name found in ini file: None
[2025.08.11-03.17.15:224][  0]LogVirtualization: Display: FNullVirtualizationSystem mounted, virtualization will be disabled
[2025.08.11-03.17.15:250][  0]LogLiveCoding: Display: Starting LiveCoding
[2025.08.11-03.17.15:251][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: UnrealEditor Win64 Development
[2025.08.11-03.17.15:252][  0]LogLiveCoding: Display: First instance in process group "UE_TAMO_0xb13c2076", spawning console
[2025.08.11-03.17.15:462][  0]LogSlate: Border
[2025.08.11-03.17.15:462][  0]LogSlate: BreadcrumbButton
[2025.08.11-03.17.15:462][  0]LogSlate: Brushes.Title
[2025.08.11-03.17.15:462][  0]LogSlate: Default
[2025.08.11-03.17.15:462][  0]LogSlate: Icons.Save
[2025.08.11-03.17.15:462][  0]LogSlate: Icons.Toolbar.Settings
[2025.08.11-03.17.15:462][  0]LogSlate: ListView
[2025.08.11-03.17.15:462][  0]LogSlate: SoftwareCursor_CardinalCross
[2025.08.11-03.17.15:462][  0]LogSlate: SoftwareCursor_Grab
[2025.08.11-03.17.15:463][  0]LogSlate: TableView.DarkRow
[2025.08.11-03.17.15:463][  0]LogSlate: TableView.Row
[2025.08.11-03.17.15:463][  0]LogSlate: TreeView
[2025.08.11-03.17.16:357][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize started...
[2025.08.11-03.17.16:362][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize took 4.334 ms
[2025.08.11-03.17.16:446][  0]LogConfig: Branch 'Mass' had been unloaded. Reloading on-demand took 1.90ms
[2025.08.11-03.17.16:649][  0]LogInit: XR: Instanced Stereo Rendering is Disabled
[2025.08.11-03.17.16:649][  0]LogInit: XR: MultiViewport is Disabled
[2025.08.11-03.17.16:649][  0]LogInit: XR: Mobile Multiview is Disabled
[2025.08.11-03.17.18:674][  0]LogLiveCoding: Display: Waiting for server
[2025.08.11-03.17.19:483][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.08.11-03.17.19:483][  0]LogNNERuntimeORT: 0: NVIDIA GeForce RTX 4070 (Compute, Graphics)
[2025.08.11-03.17.19:483][  0]LogNNERuntimeORT: 1: Microsoft Basic Render Driver (Compute, Graphics)
[2025.08.11-03.17.19:483][  0]LogNNERuntimeORT: No NPU adapter found!
[2025.08.11-03.17.20:753][  0]LogMetaSound: Display: MetaSound Page Target Initialized to 'Default'
[2025.08.11-03.17.20:756][  0]LogAudio: Display: Registering Engine Module Parameter Interfaces...
[2025.08.11-03.17.20:802][  0]LogMetaSound: MetaSound Engine Initialized
[2025.08.11-03.17.22:995][  0]LogConfig: Branch 'TranslationPickerSettings' had been unloaded. Reloading on-demand took 1.72ms
[2025.08.11-03.17.23:741][  0]LogNiagaraDebuggerClient: Niagara Debugger Client Initialized | Session: 266BAB08055E452E800000000000C500 | Instance: E14B77AB4FDF79B871E963886D1E48B5 (CNCDUW1174-65872).
[2025.08.11-03.17.24:273][  0]LogTcpMessaging: Initializing TcpMessaging bridge
[2025.08.11-03.17.24:317][  0]LogUdpMessaging: Display: Work queue size set to 1024.
[2025.08.11-03.17.24:318][  0]LogUdpMessaging: Initializing bridge on interface 0.0.0.0:0 to multicast group *********:6666.
[2025.08.11-03.17.24:318][  0]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:49577'.
[2025.08.11-03.17.24:364][  0]LogUdpMessaging: Display: Added local interface '***********' to multicast group '*********:6666'
[2025.08.11-03.17.25:163][  0]LogConfig: Applying CVar settings from Section [/Script/PCG.PCGEngineSettings] File [Engine]
[2025.08.11-03.17.26:177][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.08.11-03.17.26:177][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.08.11-03.17.26:360][  0]LogConfig: Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
[2025.08.11-03.17.26:630][  0]LogAndroidPermission: UAndroidPermissionCallbackProxy::GetInstance
[2025.08.11-03.17.26:855][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.08.11-03.17.26:855][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.08.11-03.17.27:840][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/glf/resources/plugInfo.json'
[2025.08.11-03.17.27:842][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hd/resources/plugInfo.json'
[2025.08.11-03.17.27:846][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdLux/resources/plugInfo.json'
[2025.08.11-03.17.27:849][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMedia/resources/plugInfo.json'
[2025.08.11-03.17.27:852][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdSt/resources/plugInfo.json'
[2025.08.11-03.17.27:855][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdGp/resources/plugInfo.json'
[2025.08.11-03.17.27:857][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImagingGL/resources/plugInfo.json'
[2025.08.11-03.17.27:860][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMtlx/resources/plugInfo.json'
[2025.08.11-03.17.27:863][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdx/resources/plugInfo.json'
[2025.08.11-03.17.27:865][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hgiGL/resources/plugInfo.json'
[2025.08.11-03.17.27:869][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdPhysics/resources/plugInfo.json'
[2025.08.11-03.17.27:872][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShaders/resources/plugInfo.json'
[2025.08.11-03.17.27:875][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProc/resources/plugInfo.json'
[2025.08.11-03.17.27:880][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hio/resources/plugInfo.json'
[2025.08.11-03.17.27:883][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ndr/resources/plugInfo.json'
[2025.08.11-03.17.27:886][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRender/resources/plugInfo.json'
[2025.08.11-03.17.27:889][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRiPxrImaging/resources/plugInfo.json'
[2025.08.11-03.17.27:893][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdStorm/resources/plugInfo.json'
[2025.08.11-03.17.27:895][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdf/resources/plugInfo.json'
[2025.08.11-03.17.27:898][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdrGlslfx/resources/plugInfo.json'
[2025.08.11-03.17.27:901][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShade/resources/plugInfo.json'
[2025.08.11-03.17.27:904][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRi/resources/plugInfo.json'
[2025.08.11-03.17.27:907][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/plugInfo.json'
[2025.08.11-03.17.27:910][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProcImaging/resources/plugInfo.json'
[2025.08.11-03.17.27:915][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdUI/resources/plugInfo.json'
[2025.08.11-03.17.27:918][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdAbc/resources/plugInfo.json'
[2025.08.11-03.17.27:921][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVolImaging/resources/plugInfo.json'
[2025.08.11-03.17.27:924][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdHydra/resources/plugInfo.json'
[2025.08.11-03.17.27:927][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkelImaging/resources/plugInfo.json'
[2025.08.11-03.17.27:931][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImaging/resources/plugInfo.json'
[2025.08.11-03.17.27:933][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ar/resources/plugInfo.json'
[2025.08.11-03.17.27:938][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdGeom/resources/plugInfo.json'
[2025.08.11-03.17.27:941][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVol/resources/plugInfo.json'
[2025.08.11-03.17.27:944][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkel/resources/plugInfo.json'
[2025.08.11-03.17.27:947][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'F:/TAMO_Streaming/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/codegenTemplates/plugInfo.json'
[2025.08.11-03.17.29:195][  0]SourceControl: Revision control is disabled
[2025.08.11-03.17.29:332][  0]SourceControl: Revision control is disabled
[2025.08.11-03.17.29:605][  0]LogConfig: Branch 'ObjectMixerSerializedData' had been unloaded. Reloading on-demand took 1.66ms
[2025.08.11-03.17.29:844][  0]LogConfig: Branch 'Crypto' had been unloaded. Reloading on-demand took 1.49ms
[2025.08.11-03.17.31:089][  0]LogLiveCoding: Display: Successfully initialized, removing startup thread
[2025.08.11-03.17.33:367][  0]LogCollectionManager: Loaded 0 collections in 0.002630 seconds
[2025.08.11-03.17.33:371][  0]LogFileCache: Scanning file cache for directory 'F:/TAMO_Streaming/Projects/TAMO/Saved/Collections/' took 0.00s
[2025.08.11-03.17.33:376][  0]LogFileCache: Scanning file cache for directory 'F:/TAMO_Streaming/Projects/TAMO/Content/Developers/zengyanjia/Collections/' took 0.01s
[2025.08.11-03.17.33:382][  0]LogFileCache: Scanning file cache for directory 'F:/TAMO_Streaming/Projects/TAMO/Content/Collections/' took 0.01s
[2025.08.11-03.17.33:961][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.08.11-03.17.33:961][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.08.11-03.17.34:052][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.08.11-03.17.34:052][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.08.11-03.17.34:084][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Version 1.16.4-36651368 booting at 2025-08-11T03:17:34.084Z using C
[2025.08.11-03.17.34:085][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Platform Properties [OS=Windows/10.0.19041.5915.64bit, ClientId=xyza7891REBVsEqSJRRNXmlS7EQHM459, ProductId=86f32f1151354e7cb39c12f8ab2c22a3, SandboxId=********************************, DeploymentId=a652a72ea1664dcab3a467891eea5f30, ProductName=TAMO, ProductVersion=UE5-CL-0, IsServer=false, Flags=DisableOverlay]
[2025.08.11-03.17.34:085][  0]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.08.11-03.17.34:086][  0]LogEOSSDK: LogEOSOverlay: Overlay will not load, because it was explicitly disabled when creating the platform
[2025.08.11-03.17.34:093][  0]LogEOSSDK: LogEOSAntiCheat: [AntiCheatClient] Anti-cheat client not available. Verify that the game was started using the anti-cheat bootstrapper if you intend to use it.
[2025.08.11-03.17.34:094][  0]LogEOSSDK: LogEOS: SetApplicationStatus - OldStatus: EOS_AS_Foreground, NewStatus: EOS_AS_Foreground, Current Time: 0001.01.01-00.00.00
[2025.08.11-03.17.34:094][  0]LogEOSSDK: LogEOS: SetNetworkStatus - OldStatus: EOS_NS_Online, NewStatus: EOS_NS_Online
[2025.08.11-03.17.34:094][  0]LogEOSSDK: LogEOS: Updating Platform SDK Config, Time: 0.000118
[2025.08.11-03.17.34:094][  0]LogFab: Display: Logging in using persist
[2025.08.11-03.17.34:095][  0]LogEOSSDK: Warning: LogEOSAuth: No existing persistent auth credentials were found for automatic login.
[2025.08.11-03.17.34:178][  0]LogUObjectArray: 46531 objects as part of root set at end of initial load.
[2025.08.11-03.17.34:179][  0]LogUObjectArray: CloseDisregardForGC: 0/0 objects in disregard for GC pool
[2025.08.11-03.17.34:198][  0]LogStreaming: Display: AsyncLoading2 - NotifyRegistrationComplete: Registered 38645 public script object entries (1039.31 KB)
[2025.08.11-03.17.34:198][  0]LogStreaming: Display: AsyncLoading2 - Thread Started: false, IsInitialLoad: false
[2025.08.11-03.17.34:366][  0]LogClass: Error: BoolProperty FFabAssetMetadata::IsQuixel is not initialized properly. Module:Fab File:Private/FabBrowserApi.h
[2025.08.11-03.17.34:366][  0]LogClass: Display: 1 Uninitialized script struct members found including 0 object properties
[2025.08.11-03.17.34:436][  0]LogAutomationTest: Error: LogClass: BoolProperty FFabAssetMetadata::IsQuixel is not initialized properly. Module:Fab File:Private/FabBrowserApi.h
[2025.08.11-03.17.34:437][  0]LogEngine: Initializing Engine...
[2025.08.11-03.17.34:444][  0]LogStylusInput: Initializing StylusInput subsystem.
[2025.08.11-03.17.34:444][  0]LogStats: UGameplayTagsManager::InitializeManager -  0.000 s
[2025.08.11-03.17.34:625][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.11.dll
[2025.08.11-03.17.34:689][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled
[2025.08.11-03.17.34:738][  0]LogNetVersion: Set ProjectVersion to *******. Version Checksum will be recalculated on next use.
[2025.08.11-03.17.34:738][  0]LogInit: Texture streaming: Enabled
[2025.08.11-03.17.34:832][  0]LogAnalytics: Display: [UEEditor.Perforce.Release] APIServer = https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data. AppVersion = 5.5.1-0+UE5
[2025.08.11-03.17.34:874][  0]LogAudio: Display: Initializing Audio Device Manager...
[2025.08.11-03.17.34:937][  0]LogAudio: Display: Loading Default Audio Settings Objects...
[2025.08.11-03.17.34:938][  0]LogAudio: Display: No default SoundConcurrencyObject specified (or failed to load).
[2025.08.11-03.17.34:939][  0]LogAudio: Display: Audio Device Manager Initialized
[2025.08.11-03.17.34:939][  0]LogAudio: Display: Creating Audio Device:                 Id: 1, Scope: Shared, Realtime: True
[2025.08.11-03.17.34:939][  0]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.08.11-03.17.34:939][  0]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.08.11-03.17.34:939][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.08.11-03.17.34:939][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.08.11-03.17.34:939][  0]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.08.11-03.17.34:939][  0]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.08.11-03.17.34:939][  0]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.08.11-03.17.34:939][  0]LogAudio: Display: AudioDevice MaxSources: 32
[2025.08.11-03.17.34:939][  0]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.08.11-03.17.34:939][  0]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.08.11-03.17.34:939][  0]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.08.11-03.17.35:007][  0]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.08.11-03.17.35:349][  0]LogAudioMixer: Display: Using Audio Hardware Device Speakers/Headphones (Realtek(R) Audio)
[2025.08.11-03.17.35:350][  0]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.08.11-03.17.35:353][  0]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.08.11-03.17.35:353][  0]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.08.11-03.17.35:354][  0]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.08.11-03.17.35:354][  0]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.08.11-03.17.35:357][  0]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.08.11-03.17.35:357][  0]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=1
[2025.08.11-03.17.35:357][  0]LogInit: FAudioDevice initialized with ID 1.
[2025.08.11-03.17.35:357][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled'.
[2025.08.11-03.17.35:358][  0]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 1
[2025.08.11-03.17.35:400][  0]LogCsvProfiler: Display: Metadata set : largeworldcoordinates="1"
[2025.08.11-03.17.35:407][  0]LogInit: Undo buffer set to 256 MB
[2025.08.11-03.17.35:407][  0]LogInit: Transaction tracking system initialized
[2025.08.11-03.17.35:496][  0]LogSourceControl: Display: Uncontrolled Changelist persistency file loaded ../../../Projects/TAMO/Saved/SourceControl/UncontrolledChangelists.json
[2025.08.11-03.17.36:088][  0]LogConfig: Branch 'LocalizationServiceSettings' had been unloaded. Reloading on-demand took 1.53ms
[2025.08.11-03.17.36:094][  0]LocalizationService: Localization service is disabled
[2025.08.11-03.17.36:197][  0]LogTimingProfiler: Initialize
[2025.08.11-03.17.36:197][  0]LogTimingProfiler: OnSessionChanged
[2025.08.11-03.17.36:197][  0]LoadingProfiler: Initialize
[2025.08.11-03.17.36:197][  0]LoadingProfiler: OnSessionChanged
[2025.08.11-03.17.36:197][  0]LogNetworkingProfiler: Initialize
[2025.08.11-03.17.36:198][  0]LogNetworkingProfiler: OnSessionChanged
[2025.08.11-03.17.36:198][  0]LogMemoryProfiler: Initialize
[2025.08.11-03.17.36:198][  0]LogMemoryProfiler: OnSessionChanged
[2025.08.11-03.17.37:011][  0]LogFileCache: Scanning file cache for directory 'F:/TAMO_Streaming/Projects/TAMO/Content/' took 0.01s
[2025.08.11-03.17.37:194][  0]LogPython: Using Python 3.11.8
[2025.08.11-03.17.39:111][  0]LogPython: Display: No enabled plugins with python dependencies found, skipping
[2025.08.11-03.17.39:166][  0]LogRenderer: Requested compilation of Path Tracing RTPSOs (1 permutations).
[2025.08.11-03.17.39:303][  0]LogLevelSequenceEditor: LevelSequenceEditor subsystem initialized.
[2025.08.11-03.17.40:546][  0]LogStreaming: Warning: Failed to read file 'Common/Selector.png' error.
[2025.08.11-03.17.40:546][  0]LogSlate: Could not find file for Slate resource: Common/Selector.png
[2025.08.11-03.17.40:986][  0]LogD3D12RHI: Compiled PathTracingMainRG for RTPSO in 1820.16 ms.
[2025.08.11-03.17.41:119][  0]LogEditorDataStorage: Initializing
[2025.08.11-03.17.41:123][  0]LogEditorDataStorage: Initialized
[2025.08.11-03.17.41:126][  0]LogAssetRegistry: Display: Triggering cache save on discovery complete
[2025.08.11-03.17.41:211][  0]LogInit: Display: Engine is initialized. Leaving FEngineLoop::Init()
[2025.08.11-03.17.41:215][  0]SourceControl: Revision control is disabled
[2025.08.11-03.17.41:215][  0]LogUnrealEdMisc: Loading editor; pre map load, took 47.194
[2025.08.11-03.17.41:217][  0]Cmd: MAP LOAD FILE="../../../Engine/Content/Maps/Templates/OpenWorld.umap" TEMPLATE=1 SHOWPROGRESS=1 FEATURELEVEL=4
[2025.08.11-03.17.41:221][  0]LogWorld: UWorld::CleanupWorld for Untitled, bSessionEnded=true, bCleanupResources=true
[2025.08.11-03.17.41:222][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.08.11-03.17.41:245][  0]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.08.11-03.17.41:248][  0]LogUObjectHash: Compacting FUObjectHashTables data took   1.00ms
[2025.08.11-03.17.41:264][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled_1'.
[2025.08.11-03.17.41:264][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled_1
[2025.08.11-03.17.41:267][  0]LogWorldPartition: ULevel::OnLevelLoaded(Untitled_1)(bIsOwningWorldGameWorld=0, bIsOwningWorldPartitioned=1, InitializeForMainWorld=1, InitializeForEditor=1, InitializeForGame=0)
[2025.08.11-03.17.41:267][  0]LogWorldPartition: Display: WorldPartition initialize started...
[2025.08.11-03.17.41:267][  0]LogWorldPartition: UWorldPartition::Initialize : World = /Temp/Untitled_1.Untitled_1, World Type = Editor, IsMainWorldPartition = 1, Location = V(0), Rotation = R(0), IsEditor = 1, IsGame = 0, IsPIEWorldTravel = 0, IsCooking = 0
[2025.08.11-03.17.41:339][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.11-03.17.41:384][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.11-03.17.41:388][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.11-03.17.41:391][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [AppleTV]
[2025.08.11-03.17.41:391][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [IOS]
[2025.08.11-03.17.41:391][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.08.11-03.17.41:400][  0]LogDeviceProfileManager: Display: Deviceprofile None not found.
[2025.08.11-03.17.41:910][  0]LogWorldPartition: Display: WorldPartition initialize took 643.223 ms
[2025.08.11-03.17.42:253][  0]LogAssetRegistry: Display: Asset registry cache written as 61.7 MiB to ../../../Projects/TAMO/Intermediate/CachedAssetRegistry_*.bin
[2025.08.11-03.17.42:729][  0]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.08.11-03.17.42:750][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.69ms
[2025.08.11-03.17.42:751][  0]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.08.11-03.17.42:752][  0]MapCheck: Map check complete: 0 Error(s), 0 Warning(s), took 1.539ms to complete.
[2025.08.11-03.17.42:763][  0]LogUnrealEdMisc: Total Editor Startup Time, took 48.741
[2025.08.11-03.17.42:966][  0]LogActorFactory: Loading ActorFactory Class /Script/Engine.LevelInstance
[2025.08.11-03.17.43:026][  0]LogSlate: The tab "TopLeftModeTab" attempted to spawn in layout 'LevelEditor_Layout_v1.8' but failed for some reason. It will not be displayed.
[2025.08.11-03.17.43:042][  0]RenderDocPlugin: Attaching toolbar extension...
[2025.08.11-03.17.43:102][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.08.11-03.17.43:115][  0]RenderDocPlugin: Attaching toolbar extension...
[2025.08.11-03.17.43:175][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.08.11-03.17.43:188][  0]RenderDocPlugin: Attaching toolbar extension...
[2025.08.11-03.17.43:234][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.08.11-03.17.43:248][  0]RenderDocPlugin: Attaching toolbar extension...
[2025.08.11-03.17.43:310][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.08.11-03.17.43:376][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.11-03.17.43:377][  0]LogPakFile: Display: Mounted Pak file 'F:/TAMO_Streaming/FeaturePacks/StarterContent.upack', mount point: 'root:/'
[2025.08.11-03.17.43:379][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.11-03.17.43:380][  0]LogPakFile: Display: Mounted Pak file 'F:/TAMO_Streaming/FeaturePacks/TP_FirstPerson.upack', mount point: 'root:/'
[2025.08.11-03.17.43:381][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.11-03.17.43:382][  0]LogPakFile: Display: Mounted Pak file 'F:/TAMO_Streaming/FeaturePacks/TP_FirstPersonBP.upack', mount point: 'root:/'
[2025.08.11-03.17.43:383][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.11-03.17.43:384][  0]LogPakFile: Display: Mounted Pak file 'F:/TAMO_Streaming/FeaturePacks/TP_Puzzle.upack', mount point: 'root:/'
[2025.08.11-03.17.43:384][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.11-03.17.43:385][  0]LogPakFile: Display: Mounted Pak file 'F:/TAMO_Streaming/FeaturePacks/TP_PuzzleBP.upack', mount point: 'root:/'
[2025.08.11-03.17.43:386][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.11-03.17.43:387][  0]LogPakFile: Display: Mounted Pak file 'F:/TAMO_Streaming/FeaturePacks/TP_ThirdPerson.upack', mount point: 'root:/'
[2025.08.11-03.17.43:388][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.11-03.17.43:389][  0]LogPakFile: Display: Mounted Pak file 'F:/TAMO_Streaming/FeaturePacks/TP_ThirdPersonBP.upack', mount point: 'root:/'
[2025.08.11-03.17.43:390][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.11-03.17.43:391][  0]LogPakFile: Display: Mounted Pak file 'F:/TAMO_Streaming/FeaturePacks/TP_TopDown.upack', mount point: 'root:/'
[2025.08.11-03.17.43:392][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.11-03.17.43:393][  0]LogPakFile: Display: Mounted Pak file 'F:/TAMO_Streaming/FeaturePacks/TP_TopDownBP.upack', mount point: 'root:/'
[2025.08.11-03.17.43:395][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.11-03.17.43:396][  0]LogPakFile: Display: Mounted Pak file 'F:/TAMO_Streaming/FeaturePacks/TP_VehicleAdv.upack', mount point: 'root:/'
[2025.08.11-03.17.43:398][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.11-03.17.43:398][  0]LogPakFile: Display: Mounted Pak file 'F:/TAMO_Streaming/FeaturePacks/TP_VehicleAdvBP.upack', mount point: 'root:/'
[2025.08.11-03.17.43:400][  0]LogPakFile: Initializing PakPlatformFile
[2025.08.11-03.17.43:401][  0]LogPakFile: Display: Mounted Pak file 'F:/TAMO_Streaming/FeaturePacks/TP_VirtualRealityBP.upack', mount point: 'root:/'
[2025.08.11-03.17.43:674][  0]LogSlate: Took 0.000988 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansMono.ttf' (77K)
[2025.08.11-03.17.43:703][  0]LogSlate: Took 0.001527 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.08.11-03.17.43:715][  0]LogSlate: Took 0.011723 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansFallback.ttf' (3848K)
[2025.08.11-03.17.43:792][  0]LogSlate: The tab "AvalancheOutliner1" attempted to spawn in layout 'LevelEditor_Layout_v1.8' but failed for some reason. It will not be displayed.
[2025.08.11-03.17.43:836][  0]LogSceneOutliner: Outliner Column Data Layer Visibility does not have a localizable name, please specify one to FSceneOutlinerColumnInfo
[2025.08.11-03.17.43:836][  0]LogSceneOutliner: Outliner Column Data Layer Loaded In Editor does not have a localizable name, please specify one to FSceneOutlinerColumnInfo
[2025.08.11-03.17.43:836][  0]LogSceneOutliner: Outliner Column Item Label does not have a localizable name, please specify one to FSceneOutlinerColumnInfo
[2025.08.11-03.17.43:837][  0]LogSceneOutliner: Outliner Column Remove Actor does not have a localizable name, please specify one to FSceneOutlinerColumnInfo
[2025.08.11-03.17.43:838][  0]LogSceneOutliner: Outliner Column Initial State does not have a localizable name, please specify one to FSceneOutlinerColumnInfo
[2025.08.11-03.17.43:838][  0]LogSceneOutliner: Outliner Column ID Name does not have a localizable name, please specify one to FSceneOutlinerColumnInfo
[2025.08.11-03.17.43:839][  0]LogSceneOutliner: Outliner Column Debug Color does not have a localizable name, please specify one to FSceneOutlinerColumnInfo
[2025.08.11-03.17.43:840][  0]LogSceneOutliner: Outliner Column Data Layer Has Errors does not have a localizable name, please specify one to FSceneOutlinerColumnInfo
[2025.08.11-03.17.44:090][  0]LogSlate: Took 0.001503 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Bold.ttf' (160K)
[2025.08.11-03.17.44:095][  0]LogSlate: Took 0.001588 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Italic.ttf' (157K)
[2025.08.11-03.17.44:194][  0]LogNNEDenoiser: ApplySettings: bDenoiserEnabled 1
[2025.08.11-03.17.44:210][  0]LogNNEDenoiser: Loaded input mapping from NNEDIM_ColorAlbedoNormal_Alpha
[2025.08.11-03.17.44:210][  0]LogNNEDenoiser: Loaded output mapping from NNEDOM_Output_Alpha
[2025.08.11-03.17.44:210][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.08.11-03.17.44:218][  0]LogNNERuntimeORT: Error: Failed to add DirectML execution provider to OnnxRuntime session options: D:\a\_work\1\s\onnxruntime\core\providers\dml\dml_provider_factory.cc(71)\onnxruntime.dll!000001F1F7571B44: (caller: 000001F1F75737FF) Exception(1) tid(fe7c) 80004002 No such interface supported

[2025.08.11-03.17.44:218][  0]LogNNERuntimeORT: Error: Failed to configure session options for DirectML Execution Provider.
[2025.08.11-03.17.44:218][  0]LogNNEDenoiser: Could not create model instance using NNERuntimeORTDml on RDG
[2025.08.11-03.17.44:218][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeRDGHlsl on RDG...
[2025.08.11-03.17.44:218][  0]LogNNEDenoiser: Could not create model instance. No RDG runtime 'NNERuntimeRDGHlsl' found. Valid RDG runtimes are: 
[2025.08.11-03.17.44:218][  0]LogNNEDenoiser: - NNERuntimeORTDml
[2025.08.11-03.17.44:218][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on GPU...
[2025.08.11-03.17.44:218][  0]LogNNERuntimeORT: Error: Failed to add DirectML execution provider to OnnxRuntime session options: D:\a\_work\1\s\onnxruntime\core\providers\dml\dml_provider_factory.cc(71)\onnxruntime.dll!000001F1F7571B44: (caller: 000001F1F75737FF) Exception(2) tid(fe7c) 80004002 No such interface supported

[2025.08.11-03.17.44:218][  0]LogNNERuntimeORT: Error: InitializedAndConfigureMembers failed.
[2025.08.11-03.17.44:218][  0]LogNNEDenoiser: Could not create model instance using NNERuntimeORTDml on GPU
[2025.08.11-03.17.44:218][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTCpu on CPU...
[2025.08.11-03.17.44:263][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTCpu on CPU
[2025.08.11-03.17.44:263][  0]LogNNEDenoiser: Create denoiser from asset /NNEDenoiser/NNED_Oidn2-3_Balanced_Alpha.NNED_Oidn2-3_Balanced_Alpha...
[2025.08.11-03.17.44:265][  0]LogNNEDenoiser: Loaded input mapping from NNEDTIM_ColorAlbedoNormal_Alpha
[2025.08.11-03.17.44:266][  0]LogNNEDenoiser: Loaded output mapping from NNEDTOM_Output_Alpha
[2025.08.11-03.17.44:266][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.08.11-03.17.44:267][  0]LogNNERuntimeORT: Error: Failed to add DirectML execution provider to OnnxRuntime session options: D:\a\_work\1\s\onnxruntime\core\providers\dml\dml_provider_factory.cc(71)\onnxruntime.dll!000001F1F7571B44: (caller: 000001F1F75737FF) Exception(3) tid(fe7c) 80004002 No such interface supported

[2025.08.11-03.17.44:267][  0]LogNNERuntimeORT: Error: Failed to configure session options for DirectML Execution Provider.
[2025.08.11-03.17.44:267][  0]LogNNEDenoiser: Could not create model instance using NNERuntimeORTDml on RDG
[2025.08.11-03.17.44:267][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeRDGHlsl on RDG...
[2025.08.11-03.17.44:267][  0]LogNNEDenoiser: Could not create model instance. No RDG runtime 'NNERuntimeRDGHlsl' found. Valid RDG runtimes are: 
[2025.08.11-03.17.44:267][  0]LogNNEDenoiser: - NNERuntimeORTDml
[2025.08.11-03.17.44:267][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on GPU...
[2025.08.11-03.17.44:267][  0]LogNNERuntimeORT: Error: Failed to add DirectML execution provider to OnnxRuntime session options: D:\a\_work\1\s\onnxruntime\core\providers\dml\dml_provider_factory.cc(71)\onnxruntime.dll!000001F1F7571B44: (caller: 000001F1F75737FF) Exception(4) tid(fe7c) 80004002 No such interface supported

[2025.08.11-03.17.44:267][  0]LogNNERuntimeORT: Error: InitializedAndConfigureMembers failed.
[2025.08.11-03.17.44:267][  0]LogNNEDenoiser: Could not create model instance using NNERuntimeORTDml on GPU
[2025.08.11-03.17.44:267][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTCpu on CPU...
[2025.08.11-03.17.44:315][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTCpu on CPU
[2025.08.11-03.17.44:315][  0]LogNNEDenoiser: Create temporal denoiser from asset /NNEDenoiser/NNEDT_Oidn2-3_Balanced_Alpha.NNEDT_Oidn2-3_Balanced_Alpha...
[2025.08.11-03.17.44:376][  0]LogSlate: Took 0.001830 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.08.11-03.17.44:544][  0]LogD3D12RHI: Creating RTPSO with 21 shaders (0 cached, 21 new) took 146.83 ms. Compile time 58.11 ms, link time 88.52 ms.
[2025.08.11-03.17.44:812][  0]LogStall: Startup...
[2025.08.11-03.17.44:816][  0]LogStall: Startup complete.
[2025.08.11-03.17.44:864][  0]LogLoad: (Engine Initialization) Total time: 50.84 seconds
[2025.08.11-03.17.44:874][  0]RenderDocPlugin: Creating dummy input device (for intercepting engine ticks)
[2025.08.11-03.17.45:655][  0]LogContentStreaming: Texture pool size now 1000 MB
[2025.08.11-03.17.45:655][  0]LogCsvProfiler: Display: Metadata set : streamingpoolsizemb="1000"
[2025.08.11-03.17.45:775][  0]LogAutomationController: Ignoring very large delta of 17192008.58 seconds in calls to FAutomationControllerManager::Tick() and not penalizing unresponsive tests
[2025.08.11-03.17.45:775][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.08.11-03.17.45:779][  0]LogFab: Error: Login failed - error code: EOS_InvalidAuth
[2025.08.11-03.17.45:780][  0]LogFab: Display: Logging in using exchange code
[2025.08.11-03.17.45:780][  0]LogFab: Display: Reading exchange code from commandline
[2025.08.11-03.17.45:780][  0]LogEOSSDK: Error: LogEOSAuth: Invalid parameter EOS_Auth_Credentials.Token reason: must not be null or empty
[2025.08.11-03.17.45:780][  0]LogPython: Display: Running start-up script F:/TAMO_Streaming/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... started...
[2025.08.11-03.17.45:893][  0]LogPython: registering <class 'ControlRigWorkflows.workflow_deformation_rig_preset.provider'>

[2025.08.11-03.17.45:899][  0]LogPython: Display: Running start-up script F:/TAMO_Streaming/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... took 118.914 ms
[2025.08.11-03.17.45:998][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: TAMOEditor Win64 Development
[2025.08.11-03.17.46:274][  1]LogAssetRegistry: AssetRegistryGather time 0.3107s: AssetDataDiscovery 0.0847s, AssetDataGather 0.0548s, StoreResults 0.1712s. Wall time 34.3500s.
	NumCachedDirectories 0. NumUncachedDirectories 1779. NumCachedFiles 8430. NumUncachedFiles 0.
	BackgroundTickInterruptions 2.
[2025.08.11-03.17.46:318][  1]LogCollectionManager: Fixed up redirectors for 0 collections in 0.000000 seconds (updated 0 objects)
[2025.08.11-03.17.46:378][  1]LogSourceControl: Uncontrolled asset enumeration started...
[2025.08.11-03.17.46:389][  1]LogFab: Error: Login failed - error code: EOS_InvalidParameters
[2025.08.11-03.17.46:941][  9]LogSourceControl: Uncontrolled asset enumeration finished in 0.56382 seconds (Found 74 uncontrolled assets)
[2025.08.11-03.17.48:083][ 16]LogEOSSDK: LogEOS: SDK Config Platform Update Request Successful, Time: 13.648943
[2025.08.11-03.17.48:085][ 16]LogEOSSDK: LogEOSAnalytics: EOS SDK Analytics disabled for route [1].
[2025.08.11-03.17.48:088][ 17]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 13.988892
[2025.08.11-03.17.49:414][ 20]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.08.11-03.17.50:080][ 22]LogEOSSDK: LogEOS: SDK Config Product Update Request Successful, Time: 15.653167
[2025.08.11-03.17.50:084][ 22]LogEOSSDK: LogEOS: SDK Config Data - Watermark: 1327463752
[2025.08.11-03.17.50:084][ 22]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 15.653167, Update Interval: 329.059723
[2025.08.11-03.17.59:113][207]LogChaosDD: Creating Chaos Debug Draw Scene for world World_0
[2025.08.11-03.17.59:198][208]LogActorFactory: Actor Factory attempting to spawn StaticMesh /Engine/BasicShapes/Cube.Cube
[2025.08.11-03.17.59:198][208]LogActorFactory: Actor Factory attempting to spawn StaticMesh /Engine/BasicShapes/Cube.Cube
[2025.08.11-03.17.59:199][208]LogActorFactory: Actor Factory spawned StaticMesh /Engine/BasicShapes/Cube.Cube as actor: StaticMeshActor /Temp/Untitled_1.Untitled_1:PersistentLevel.StaticMeshActor_0
[2025.08.11-03.17.59:200][208]LogActorFactory: Actor Factory spawned StaticMesh /Engine/BasicShapes/Cube.Cube as actor: StaticMeshActor /Temp/Untitled_1.Untitled_1:PersistentLevel.StaticMeshActor_0
[2025.08.11-03.17.59:621][214]LogUObjectHash: Compacting FUObjectHashTables data took   1.09ms
[2025.08.11-03.17.59:624][214]LogEditorActor: Deleted 0 Actors (0.105 secs)
[2025.08.11-03.17.59:635][214]LogActorFactory: Actor Factory attempting to spawn StaticMesh /Engine/BasicShapes/Cube.Cube
[2025.08.11-03.17.59:635][214]LogActorFactory: Actor Factory attempting to spawn StaticMesh /Engine/BasicShapes/Cube.Cube
[2025.08.11-03.17.59:640][214]LogActorFactory: Actor Factory spawned StaticMesh /Engine/BasicShapes/Cube.Cube as actor: StaticMeshActor /Temp/Untitled_1.Untitled_1:PersistentLevel.StaticMeshActor_UAID_B07B250C4A98318402_1367956511
[2025.08.11-03.17.59:641][214]LogActorFactory: Actor Factory spawned StaticMesh /Engine/BasicShapes/Cube.Cube as actor: StaticMeshActor /Temp/Untitled_1.Untitled_1:PersistentLevel.StaticMeshActor_UAID_B07B250C4A98318402_1367956511
[2025.08.11-03.17.59:911][216]LogChaosDD: Creating Chaos Debug Draw Scene for world World_1
[2025.08.11-03.18.05:597][453]LogTemp: Reference Check Complete: 1 actors, 2 total references, 0 issues
[2025.08.11-03.18.14:144][854]LogTemp: Reference Check Complete: 1 actors, 9 total references, 0 issues
[2025.08.11-03.18.27:495][503]LogTemp: Reference Check Complete: 1 actors, 1 total references, 0 issues
[2025.08.11-03.18.33:265][788]LogTurnkeySupport: Completed SDK detection: ExitCode = 0
[2025.08.11-03.18.33:265][788]LogTurnkeySupport: Turnkey Platform: Android: (Status=Valid, MinAllowed_Sdk=r25b, MaxAllowed_Sdk=r27, Current_Sdk=r25b, Allowed_AutoSdk=r25b, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists, Support_FullSdk, Sdk_HasBestVersion")
[2025.08.11-03.18.33:265][788]LogTurnkeySupport: Turnkey Platform: IOS: (Status=Invalid, MinAllowed_Sdk=1100.0.0.0, MaxAllowed_Sdk=8999.0, Current_Sdk=, Allowed_AutoSdk=15.2, Current_AutoSdk=, Flags="Platform_ValidHostPrerequisites")
[2025.08.11-03.18.33:265][788]LogTurnkeySupport: Turnkey Platform: Linux: (Status=Invalid, Allowed_Sdk=v23_clang-18.1.0-rockylinux8, Current_Sdk=, Allowed_AutoSdk=v23_clang-18.1.0-rockylinux8, Current_AutoSdk=, Flags="Platform_ValidHostPrerequisites")
[2025.08.11-03.18.33:265][788]LogTurnkeySupport: Turnkey Platform: LinuxArm64: (Status=Invalid, Allowed_Sdk=v23_clang-18.1.0-rockylinux8, Current_Sdk=, Allowed_AutoSdk=v23_clang-18.1.0-rockylinux8, Current_AutoSdk=, Flags="Platform_ValidHostPrerequisites")
[2025.08.11-03.18.33:265][788]LogTurnkeySupport: Turnkey Platform: Mac: (Status=Invalid, MinAllowed_Sdk=1100.0.0.0, MaxAllowed_Sdk=8999.0, Current_Sdk=, Allowed_AutoSdk=15.2, Current_AutoSdk=, Flags="Platform_ValidHostPrerequisites")
[2025.08.11-03.18.33:265][788]LogTurnkeySupport: Turnkey Platform: PS4: (Status=Invalid, MinAllowed_Sdk=12.008.011, MaxAllowed_Sdk=12.999.999, Current_Sdk=9.508.001, Allowed_AutoSdk=12.008.011, Current_AutoSdk=, Flags="InstalledSdk_InvalidVersionExists, Platform_ValidHostPrerequisites")
[2025.08.11-03.18.33:265][788]LogTurnkeySupport: Turnkey Platform: PS5: (Status=Invalid, MinAllowed_Sdk=***********, MaxAllowed_Sdk=***********, Current_Sdk=**********, Allowed_AutoSdk=***********, Current_AutoSdk=, Flags="InstalledSdk_InvalidVersionExists, Platform_ValidHostPrerequisites")
[2025.08.11-03.18.33:265][788]LogTurnkeySupport: Turnkey Platform: Switch: (Status=Invalid, MinAllowed_Sdk=16.2.3, MaxAllowed_Sdk=18.3.1, Current_Sdk=, Allowed_AutoSdk=18.3.1, Current_AutoSdk=, Flags="Platform_ValidHostPrerequisites")
[2025.08.11-03.18.33:266][788]LogTurnkeySupport: Turnkey Platform: TVOS: (Status=Invalid, MinAllowed_Sdk=1100.0.0.0, MaxAllowed_Sdk=8999.0, Current_Sdk=, Allowed_AutoSdk=15.2, Current_AutoSdk=, Flags="Platform_ValidHostPrerequisites")
[2025.08.11-03.18.33:266][788]LogTurnkeySupport: Turnkey Platform: VisionOS: (Status=Invalid, , Flags="Host_Unsupported")
[2025.08.11-03.18.33:266][788]LogTurnkeySupport: Turnkey Platform: Win64: (Status=Valid, MinAllowed_Sdk=10.0.19041.0, MaxAllowed_Sdk=10.9.99999.0, Current_Sdk=10.0.22621.0, Allowed_AutoSdk=10.0.22621.0, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists, Sdk_HasBestVersion")
[2025.08.11-03.18.33:266][788]LogTurnkeySupport: Turnkey Platform: WinGDK: (Status=Invalid, MinAllowed_Sdk=240302, MaxAllowed_Sdk=240602, Current_Sdk=, Allowed_AutoSdk=240602, Current_AutoSdk=, Flags="Platform_ValidHostPrerequisites")
[2025.08.11-03.18.33:266][788]LogTurnkeySupport: Turnkey Platform: XB1: (Status=Invalid, MinAllowed_Sdk=240302, MaxAllowed_Sdk=240602, Current_Sdk=, Allowed_AutoSdk=240602, Current_AutoSdk=, Flags="Platform_ValidHostPrerequisites")
[2025.08.11-03.18.33:266][788]LogTurnkeySupport: Turnkey Platform: XSX: (Status=Invalid, MinAllowed_Sdk=240302, MaxAllowed_Sdk=240602, Current_Sdk=, Allowed_AutoSdk=240602, Current_AutoSdk=, Flags="Platform_ValidHostPrerequisites")
[2025.08.11-03.18.33:266][788]LogTurnkeySupport: Running Turnkey device detection: ' -ScriptsForProject="F:/TAMO_Streaming/Projects/TAMO/TAMO.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="F:/TAMO_Streaming/Projects/TAMO/Intermediate/TurnkeyReport_1.log" -log="F:/TAMO_Streaming/Projects/TAMO/Intermediate/TurnkeyLog_1.log" -project="F:/TAMO_Streaming/Projects/TAMO/TAMO.uproject"  -Device=Win64@CNCDUW1174'
[2025.08.11-03.18.33:266][788]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""F:/TAMO_Streaming/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="F:/TAMO_Streaming/Projects/TAMO/TAMO.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="F:/TAMO_Streaming/Projects/TAMO/Intermediate/TurnkeyReport_1.log" -log="F:/TAMO_Streaming/Projects/TAMO/Intermediate/TurnkeyLog_1.log" -project="F:/TAMO_Streaming/Projects/TAMO/TAMO.uproject"  -Device=Win64@CNCDUW1174" -nocompile -nocompileuat ]
[2025.08.11-03.18.35:977][919]LogTurnkeySupport: Completed device detection: Code = 0
[2025.08.11-03.18.35:986][919]LogTurnkeySupport: Turnkey Device: Win64@CNCDUW1174: (Name=CNCDUW1174, Type=Computer, Status=Valid, MinAllowed=10.0.19041.0, MaxAllowed=, Current=10.0.19045.0, Flags="Device_InstallSoftwareValid")
[2025.08.11-03.18.40:411][149]LogTemp: Reference Check Complete: 1 actors, 1 total references, 0 issues
[2025.08.11-03.19.06:888][284]LogDerivedDataCache: ../../../Engine/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.08.11-03.23.19:261][ 40]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 345.165649
[2025.08.11-03.23.20:262][ 43]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.08.11-03.23.20:262][ 43]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 345.832672, Update Interval: 339.883423
[2025.08.11-03.23.57:647][156]LogUdpMessaging: Warning: FUdpMessageProcessor::UpdateReassemblers Discarding 8/9 of stale message segements from Sender=10.72.90.33:51279, Sequence=0
[2025.08.11-03.27.46:114][840]LogUObjectHash: Compacting FUObjectHashTables data took   0.64ms
[2025.08.11-03.27.46:118][840]Cmd: OBJ SAVEPACKAGE PACKAGE="/Temp/Untitled_1" FILE="../../../Projects/TAMO/Saved/Autosaves/Temp/Untitled_1_Auto1.umap" SILENT=true AUTOSAVING=true KEEPDIRTY=false
[2025.08.11-03.27.46:135][840]LogSavePackage: Moving output files for package: /Temp/Autosaves/Temp/Untitled_1_Auto1
[2025.08.11-03.27.46:136][840]LogSavePackage: Moving '../../../Projects/TAMO/Saved/Untitled_1_Auto1D91838FD4E35565F52423AA20071D3F8.tmp' to '../../../Projects/TAMO/Saved/Autosaves/Temp/Untitled_1_Auto1.umap'
[2025.08.11-03.27.46:139][840]LogFileHelpers: Editor autosave (incl. external actors) for '/Temp/Untitled_1' took 0.053
[2025.08.11-03.27.46:139][840]LogFileHelpers: Editor autosave (incl. sublevels & external actors) for all levels took 0.053
[2025.08.11-03.28.02:454][918]LogUObjectHash: Compacting FUObjectHashTables data took   0.37ms
[2025.08.11-03.28.03:626][918]LogSlate: Window 'Save Content' being destroyed
[2025.08.11-03.28.03:725][918]LogStall: Shutdown...
[2025.08.11-03.28.03:725][918]LogStall: Shutdown complete.
[2025.08.11-03.28.03:814][918]LogSlate: Window 'Reference Checker' being destroyed
[2025.08.11-03.28.03:818][918]LogSlate: Window 'TAMO - Unreal Editor' being destroyed
[2025.08.11-03.28.03:919][918]Cmd: QUIT_EDITOR
[2025.08.11-03.28.03:920][919]LogCore: Engine exit requested (reason: UUnrealEdEngine::CloseEditor())
[2025.08.11-03.28.03:930][919]LogCore: Engine exit requested (reason: EngineExit() was called; note: exit was already requested)
[2025.08.11-03.28.03:931][919]LogStaticMesh: Abandoning remaining async distance field tasks for shutdown
[2025.08.11-03.28.03:931][919]LogStaticMesh: Abandoning remaining async card representation tasks for shutdown
[2025.08.11-03.28.03:943][919]LogWorld: UWorld::CleanupWorld for Untitled_1, bSessionEnded=true, bCleanupResources=true
[2025.08.11-03.28.03:943][919]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.08.11-03.28.03:943][919]LogWorldPartition: UWorldPartition::Uninitialize : World = /Temp/Untitled_1.Untitled_1
[2025.08.11-03.28.03:962][919]LogStylusInput: Shutting down StylusInput subsystem.
[2025.08.11-03.28.03:962][919]LogLevelSequenceEditor: LevelSequenceEditor subsystem deinitialized.
[2025.08.11-03.28.03:966][919]LogWorld: UWorld::CleanupWorld for World_0, bSessionEnded=true, bCleanupResources=true
[2025.08.11-03.28.03:966][919]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.08.11-03.28.03:966][919]LogWorld: UWorld::CleanupWorld for World_1, bSessionEnded=true, bCleanupResources=true
[2025.08.11-03.28.03:967][919]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.08.11-03.28.03:967][919]LogStudioTelemetry: Ended StudioTelemetry Session
[2025.08.11-03.28.03:973][919]LogAnalyticsSessionSummarySender: Sent summary session report for: AppId=UEEditor.Perforce.Release SessionId={9139CD01-4A29-67DD-474C-FC81042714F6}
[2025.08.11-03.28.03:977][919]LogAnalytics: Display: [UEEditor.Perforce.Release] Destroying ET Analytics provider
[2025.08.11-03.28.03:977][919]LogAnalytics: Display: [UEEditor.Perforce.Release] Ended ET Analytics provider session
[2025.08.11-03.28.03:977][919]LogAnalytics: Display: [UEEditor.Perforce.Release] Destroyed ET Analytics provider
[2025.08.11-03.28.03:978][919]LogAudio: Display: Beginning Audio Device Manager Shutdown (Module: AudioMixerXAudio2)...
[2025.08.11-03.28.03:978][919]LogAudio: Display: Destroying 1 Remaining Audio Device(s)...
[2025.08.11-03.28.03:978][919]LogAudio: Display: Audio Device unregistered from world 'Untitled_1'.
[2025.08.11-03.28.03:978][919]LogAudio: Display: Shutting down audio device while 1 references to it are still alive. For more information, compile with INSTRUMENT_AUDIODEVICE_HANDLES.
[2025.08.11-03.28.03:978][919]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=1
[2025.08.11-03.28.03:981][919]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=1
[2025.08.11-03.28.03:993][919]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID -1
[2025.08.11-03.28.03:993][919]LogAudio: Display: Audio Device Manager Shutdown
[2025.08.11-03.28.03:994][919]LogWindowsTextInputMethodSystem: Activated input method: English (United States) - (Keyboard).
[2025.08.11-03.28.03:997][919]LogSlate: Slate User Destroyed.  User Index 0, Is Virtual User: 0
[2025.08.11-03.28.03:997][919]LogExit: Preparing to exit.
[2025.08.11-03.28.04:026][919]LogUObjectHash: Compacting FUObjectHashTables data took   0.64ms
[2025.08.11-03.28.04:790][919]LogEditorDataStorage: Deinitializing
[2025.08.11-03.28.04:880][919]LogDemo: Cleaned up 0 splitscreen connections, owner deletion: enabled
[2025.08.11-03.28.04:902][919]LogExit: Editor shut down
[2025.08.11-03.28.04:903][919]LogExit: Transaction tracking system shut down
[2025.08.11-03.28.05:016][919]LogExit: Object subsystem successfully closed.
[2025.08.11-03.28.05:032][919]LogShaderCompilers: Display: Shaders left to compile 0
[2025.08.11-03.28.05:062][919]LogShaderCompilers: Display: Shaders left to compile 0
[2025.08.11-03.28.05:337][919]LogMemoryProfiler: Shutdown
[2025.08.11-03.28.05:337][919]LogNetworkingProfiler: Shutdown
[2025.08.11-03.28.05:337][919]LoadingProfiler: Shutdown
[2025.08.11-03.28.05:337][919]LogTimingProfiler: Shutdown
[2025.08.11-03.28.05:790][919]LogChaosDD: Chaos Debug Draw Shutdown
[2025.08.11-03.28.05:797][919]LogStudioTelemetry: Display: Shutdown StudioTelemetry Module
[2025.08.11-03.28.05:797][919]LogNFORDenoise: NFORDenoise function shutting down
[2025.08.11-03.28.05:797][919]RenderDocPlugin: plugin has been unloaded.
[2025.08.11-03.28.05:800][919]LogHttp: Warning: [FHttpManager::Shutdown] Unbinding delegates for 1 outstanding Http Requests:
[2025.08.11-03.28.05:800][919]LogHttp: Warning: 	verb=[POST] url=[https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data?SessionID=%7B9139CD01-4A29-67DD-474C-FC81042714F6%7D&AppID=UEEditor.Perforce.Release&AppVersion=5.5.1-0%2BUE5&UserID=0cf912ee4b52d61c1327948a4429657f%7Cbb5346b2832945159927080db1712c15%7C77aa645b-f6b2-4851-b85d-2c89bbf636d4&AppEnvironment=datacollector-binary&UploadType=eteventstream] refs=[2] status=Processing
