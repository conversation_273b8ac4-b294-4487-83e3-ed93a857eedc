{"IsGameTarget": false, "RootLocalPath": "F:\\TAMO_Streaming", "TargetName": "ShaderCompileWorker", "ExternalDependenciesFile": "F:\\TAMO_Streaming\\Projects\\TAMO\\Intermediate\\Build\\Win64\\ShaderCompileWorker\\Development\\ShaderCompileWorker.deps", "Modules": [{"Name": "CoreUObject", "ModuleType": "EngineRuntime", "OverrideModuleType": "None", "BaseDirectory": "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\CoreUObject", "IncludeBase": "F:\\TAMO_Streaming\\Engine\\Source", "OutputDirectory": "F:\\TAMO_Streaming\\Engine\\Intermediate\\Build\\Win64\\ShaderCompileWorker\\Inc\\CoreUObject\\UHT", "ClassesHeaders": [], "PublicHeaders": ["F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\CoreUObject\\Public\\Misc\\DataValidation.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\CoreUObject\\Public\\Misc\\EditorPathObjectInterface.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\CoreUObject\\Public\\StructUtils\\InstancedStruct.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\CoreUObject\\Public\\StructUtils\\InstancedStructContainer.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\CoreUObject\\Public\\StructUtils\\SharedStruct.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\CoreUObject\\Public\\StructUtils\\UserDefinedStructEditorUtils.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\CoreUObject\\Public\\StructUtils\\UserDefinedStruct.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\CoreUObject\\Public\\StructUtils\\PropertyBag.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\CoreUObject\\Public\\UObject\\CookedMetaData.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\CoreUObject\\Public\\UObject\\CoreNetTypes.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\CoreUObject\\Public\\UObject\\NoExportTypes.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\CoreUObject\\Public\\UObject\\PerPlatformProperties.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\CoreUObject\\Public\\UObject\\OverriddenPropertySet.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\CoreUObject\\Public\\UObject\\PropertyText.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\CoreUObject\\Public\\VerseVM\\VVMPackageTypes.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\CoreUObject\\Public\\VerseVM\\VVMVerseClass.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\CoreUObject\\Public\\VerseVM\\VVMVerseEnum.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\CoreUObject\\Public\\VerseVM\\VVMVerseEffectSet.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\CoreUObject\\Public\\VerseVM\\VVMVerseStruct.h"], "InternalHeaders": [], "PrivateHeaders": ["F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\CoreUObject\\Private\\UObject\\PropertyHelper.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\CoreUObject\\Tests\\OptionalPropertyTestObject.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\CoreUObject\\Tests\\UObject\\InstanceDataObjectUtilsTest.h"], "PublicDefines": ["WITH_VERSE_COMPILER=1"], "GeneratedCPPFilenameBase": "F:\\TAMO_Streaming\\Engine\\Intermediate\\Build\\Win64\\ShaderCompileWorker\\Inc\\CoreUObject\\UHT\\CoreUObject.gen", "SaveExportedHeaders": true, "UHTGeneratedCodeVersion": "None", "VersePath": "", "VerseScope": "PublicUser", "HasVerse": false, "VersePluginName": ""}, {"Name": "AssetRegistry", "ModuleType": "EngineRuntime", "OverrideModuleType": "None", "BaseDirectory": "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\AssetRegistry", "IncludeBase": "F:\\TAMO_Streaming\\Engine\\Source", "OutputDirectory": "F:\\TAMO_Streaming\\Engine\\Intermediate\\Build\\Win64\\ShaderCompileWorker\\Inc\\AssetRegistry\\UHT", "ClassesHeaders": [], "PublicHeaders": ["F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\AssetRegistry\\Public\\AssetRegistry\\AssetRegistryHelpers.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\AssetRegistry\\Public\\AssetRegistry\\IAssetRegistry.h"], "InternalHeaders": [], "PrivateHeaders": ["F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\AssetRegistry\\Private\\AssetRegistry.h"], "PublicDefines": [], "GeneratedCPPFilenameBase": "F:\\TAMO_Streaming\\Engine\\Intermediate\\Build\\Win64\\ShaderCompileWorker\\Inc\\AssetRegistry\\UHT\\AssetRegistry.gen", "SaveExportedHeaders": true, "UHTGeneratedCodeVersion": "None", "VersePath": "", "VerseScope": "PublicUser", "HasVerse": false, "VersePluginName": ""}, {"Name": "SessionMessages", "ModuleType": "EngineRuntime", "OverrideModuleType": "None", "BaseDirectory": "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\SessionMessages", "IncludeBase": "F:\\TAMO_Streaming\\Engine\\Source", "OutputDirectory": "F:\\TAMO_Streaming\\Engine\\Intermediate\\Build\\Win64\\ShaderCompileWorker\\Inc\\SessionMessages\\UHT", "ClassesHeaders": [], "PublicHeaders": ["F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\SessionMessages\\Public\\SessionServiceMessages.h"], "InternalHeaders": [], "PrivateHeaders": [], "PublicDefines": [], "GeneratedCPPFilenameBase": "F:\\TAMO_Streaming\\Engine\\Intermediate\\Build\\Win64\\ShaderCompileWorker\\Inc\\SessionMessages\\UHT\\SessionMessages.gen", "SaveExportedHeaders": true, "UHTGeneratedCodeVersion": "None", "VersePath": "", "VerseScope": "PublicUser", "HasVerse": false, "VersePluginName": ""}, {"Name": "AudioPlatformConfiguration", "ModuleType": "EngineRuntime", "OverrideModuleType": "None", "BaseDirectory": "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\AudioPlatformConfiguration", "IncludeBase": "F:\\TAMO_Streaming\\Engine\\Source", "OutputDirectory": "F:\\TAMO_Streaming\\Engine\\Intermediate\\Build\\Win64\\ShaderCompileWorker\\Inc\\AudioPlatformConfiguration\\UHT", "ClassesHeaders": [], "PublicHeaders": ["F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\AudioPlatformConfiguration\\Public\\AudioCompressionSettings.h"], "InternalHeaders": [], "PrivateHeaders": [], "PublicDefines": [], "GeneratedCPPFilenameBase": "F:\\TAMO_Streaming\\Engine\\Intermediate\\Build\\Win64\\ShaderCompileWorker\\Inc\\AudioPlatformConfiguration\\UHT\\AudioPlatformConfiguration.gen", "SaveExportedHeaders": true, "UHTGeneratedCodeVersion": "None", "VersePath": "", "VerseScope": "PublicUser", "HasVerse": false, "VersePluginName": ""}, {"Name": "InputCore", "ModuleType": "EngineRuntime", "OverrideModuleType": "None", "BaseDirectory": "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\InputCore", "IncludeBase": "F:\\TAMO_Streaming\\Engine\\Source", "OutputDirectory": "F:\\TAMO_Streaming\\Engine\\Intermediate\\Build\\Win64\\ShaderCompileWorker\\Inc\\InputCore\\UHT", "ClassesHeaders": ["F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\InputCore\\Classes\\InputCoreTypes.h"], "PublicHeaders": [], "InternalHeaders": [], "PrivateHeaders": [], "PublicDefines": [], "GeneratedCPPFilenameBase": "F:\\TAMO_Streaming\\Engine\\Intermediate\\Build\\Win64\\ShaderCompileWorker\\Inc\\InputCore\\UHT\\InputCore.gen", "SaveExportedHeaders": true, "UHTGeneratedCodeVersion": "None", "VersePath": "", "VerseScope": "PublicUser", "HasVerse": false, "VersePluginName": ""}, {"Name": "DeveloperSettings", "ModuleType": "EngineRuntime", "OverrideModuleType": "None", "BaseDirectory": "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\DeveloperSettings", "IncludeBase": "F:\\TAMO_Streaming\\Engine\\Source", "OutputDirectory": "F:\\TAMO_Streaming\\Engine\\Intermediate\\Build\\Win64\\ShaderCompileWorker\\Inc\\DeveloperSettings\\UHT", "ClassesHeaders": [], "PublicHeaders": ["F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\DeveloperSettings\\Public\\Engine\\DeveloperSettings.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\DeveloperSettings\\Public\\Engine\\DeveloperSettingsBackedByCVars.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\DeveloperSettings\\Public\\Engine\\PlatformSettingsManager.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\DeveloperSettings\\Public\\Engine\\PlatformSettings.h"], "InternalHeaders": [], "PrivateHeaders": [], "PublicDefines": [], "GeneratedCPPFilenameBase": "F:\\TAMO_Streaming\\Engine\\Intermediate\\Build\\Win64\\ShaderCompileWorker\\Inc\\DeveloperSettings\\UHT\\DeveloperSettings.gen", "SaveExportedHeaders": true, "UHTGeneratedCodeVersion": "None", "VersePath": "", "VerseScope": "PublicUser", "HasVerse": false, "VersePluginName": ""}, {"Name": "SlateCore", "ModuleType": "EngineRuntime", "OverrideModuleType": "None", "BaseDirectory": "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\SlateCore", "IncludeBase": "F:\\TAMO_Streaming\\Engine\\Source", "OutputDirectory": "F:\\TAMO_Streaming\\Engine\\Intermediate\\Build\\Win64\\ShaderCompileWorker\\Inc\\SlateCore\\UHT", "ClassesHeaders": [], "PublicHeaders": ["F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\SlateCore\\Public\\Debugging\\SlateDebugging.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\SlateCore\\Public\\Fonts\\CompositeFont.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\SlateCore\\Public\\Fonts\\FontBulkData.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\SlateCore\\Public\\Fonts\\FontProviderInterface.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\SlateCore\\Public\\Fonts\\FontCache.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\SlateCore\\Public\\Fonts\\FontRasterizationMode.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\SlateCore\\Public\\Fonts\\SlateFontInfo.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\SlateCore\\Public\\Fonts\\FontFaceInterface.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\SlateCore\\Public\\Fonts\\FontSdfSettings.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\SlateCore\\Public\\Input\\Events.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\SlateCore\\Public\\Input\\NavigationReply.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\SlateCore\\Public\\Layout\\Clipping.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\SlateCore\\Public\\Layout\\FlowDirection.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\SlateCore\\Public\\Layout\\Geometry.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\SlateCore\\Public\\Layout\\Margin.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\SlateCore\\Public\\Rendering\\RenderingCommon.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\SlateCore\\Public\\Rendering\\SlateRendererTypes.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\SlateCore\\Public\\Sound\\SlateSound.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\SlateCore\\Public\\Styling\\SegmentedControlStyle.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\SlateCore\\Public\\Styling\\SlateBrush.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\SlateCore\\Public\\Styling\\SlateColor.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\SlateCore\\Public\\Styling\\SlateTypes.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\SlateCore\\Public\\Styling\\SlateWidgetStyle.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\SlateCore\\Public\\Styling\\SlateWidgetStyleAsset.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\SlateCore\\Public\\Styling\\SlateWidgetStyleContainerInterface.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\SlateCore\\Public\\Styling\\SlateWidgetStyleContainerBase.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\SlateCore\\Public\\Styling\\StyleColors.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\SlateCore\\Public\\Styling\\ToolBarStyle.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\SlateCore\\Public\\Types\\SlateEnums.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\SlateCore\\Public\\Types\\SlateVector2.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\SlateCore\\Public\\Widgets\\WidgetPixelSnapping.h"], "InternalHeaders": [], "PrivateHeaders": [], "PublicDefines": ["WITH_FREETYPE=1"], "GeneratedCPPFilenameBase": "F:\\TAMO_Streaming\\Engine\\Intermediate\\Build\\Win64\\ShaderCompileWorker\\Inc\\SlateCore\\UHT\\SlateCore.gen", "SaveExportedHeaders": true, "UHTGeneratedCodeVersion": "None", "VersePath": "", "VerseScope": "PublicUser", "HasVerse": false, "VersePluginName": ""}, {"Name": "ImageCore", "ModuleType": "EngineRuntime", "OverrideModuleType": "None", "BaseDirectory": "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\ImageCore", "IncludeBase": "F:\\TAMO_Streaming\\Engine\\Source", "OutputDirectory": "F:\\TAMO_Streaming\\Engine\\Intermediate\\Build\\Win64\\ShaderCompileWorker\\Inc\\ImageCore\\UHT", "ClassesHeaders": [], "PublicHeaders": ["F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\ImageCore\\Public\\ImageCoreBP.h"], "InternalHeaders": [], "PrivateHeaders": [], "PublicDefines": [], "GeneratedCPPFilenameBase": "F:\\TAMO_Streaming\\Engine\\Intermediate\\Build\\Win64\\ShaderCompileWorker\\Inc\\ImageCore\\UHT\\ImageCore.gen", "SaveExportedHeaders": true, "UHTGeneratedCodeVersion": "None", "VersePath": "", "VerseScope": "PublicUser", "HasVerse": false, "VersePluginName": ""}, {"Name": "Slate", "ModuleType": "EngineRuntime", "OverrideModuleType": "None", "BaseDirectory": "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\Slate", "IncludeBase": "F:\\TAMO_Streaming\\Engine\\Source", "OutputDirectory": "F:\\TAMO_Streaming\\Engine\\Intermediate\\Build\\Win64\\ShaderCompileWorker\\Inc\\Slate\\UHT", "ClassesHeaders": [], "PublicHeaders": ["F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\Slate\\Public\\SlateSettings.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\Slate\\Public\\Framework\\Application\\SlateApplication.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\Slate\\Public\\Framework\\Commands\\InputChord.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\Slate\\Public\\Framework\\Commands\\UICommandInfo.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\Slate\\Public\\Framework\\MultiBox\\MultiBoxDefs.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\Slate\\Public\\Framework\\MultiBox\\ToolMenuBase.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\Slate\\Public\\Framework\\Styling\\CheckBoxWidgetStyle.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\Slate\\Public\\Framework\\Styling\\ButtonWidgetStyle.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\Slate\\Public\\Framework\\Styling\\ComboBoxWidgetStyle.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\Slate\\Public\\Framework\\Styling\\EditableTextWidgetStyle.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\Slate\\Public\\Framework\\Styling\\ScrollBoxWidgetStyle.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\Slate\\Public\\Framework\\Styling\\ComboButtonWidgetStyle.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\Slate\\Public\\Framework\\Styling\\ProgressWidgetStyle.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\Slate\\Public\\Framework\\Styling\\TextBlockWidgetStyle.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\Slate\\Public\\Framework\\Styling\\ScrollBarWidgetStyle.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\Slate\\Public\\Framework\\Styling\\EditableTextBoxWidgetStyle.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\Slate\\Public\\Framework\\Text\\CharRangeList.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\Slate\\Public\\Framework\\Styling\\SpinBoxWidgetStyle.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\Slate\\Public\\Framework\\Text\\TextLayout.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\Slate\\Public\\Framework\\Views\\ITypedTableView.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\Slate\\Public\\Widgets\\Input\\IVirtualKeyboardEntry.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\Slate\\Public\\Widgets\\Layout\\Anchors.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\Slate\\Public\\Widgets\\Layout\\SScaleBox.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\Slate\\Public\\Widgets\\Layout\\SScrollBox.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\Slate\\Public\\Widgets\\Notifications\\SProgressBar.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\Slate\\Public\\Widgets\\Text\\ISlateEditableTextWidget.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\Slate\\Public\\Widgets\\Views\\STableViewBase.h"], "InternalHeaders": [], "PrivateHeaders": [], "PublicDefines": [], "GeneratedCPPFilenameBase": "F:\\TAMO_Streaming\\Engine\\Intermediate\\Build\\Win64\\ShaderCompileWorker\\Inc\\Slate\\UHT\\Slate.gen", "SaveExportedHeaders": true, "UHTGeneratedCodeVersion": "None", "VersePath": "", "VerseScope": "PublicUser", "HasVerse": false, "VersePluginName": ""}, {"Name": "EngineSettings", "ModuleType": "EngineRuntime", "OverrideModuleType": "None", "BaseDirectory": "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\EngineSettings", "IncludeBase": "F:\\TAMO_Streaming\\Engine\\Source", "OutputDirectory": "F:\\TAMO_Streaming\\Engine\\Intermediate\\Build\\Win64\\ShaderCompileWorker\\Inc\\EngineSettings\\UHT", "ClassesHeaders": ["F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\EngineSettings\\Classes\\ConsoleSettings.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\EngineSettings\\Classes\\GameMapsSettings.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\EngineSettings\\Classes\\GameNetworkManagerSettings.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\EngineSettings\\Classes\\GameSessionSettings.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\EngineSettings\\Classes\\GeneralEngineSettings.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\EngineSettings\\Classes\\GeneralProjectSettings.h", "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\EngineSettings\\Classes\\HudSettings.h"], "PublicHeaders": [], "InternalHeaders": [], "PrivateHeaders": [], "PublicDefines": [], "GeneratedCPPFilenameBase": "F:\\TAMO_Streaming\\Engine\\Intermediate\\Build\\Win64\\ShaderCompileWorker\\Inc\\EngineSettings\\UHT\\EngineSettings.gen", "SaveExportedHeaders": true, "UHTGeneratedCodeVersion": "None", "VersePath": "", "VerseScope": "PublicUser", "HasVerse": false, "VersePluginName": ""}, {"Name": "VectorVM", "ModuleType": "EngineRuntime", "OverrideModuleType": "None", "BaseDirectory": "F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\VectorVM", "IncludeBase": "F:\\TAMO_Streaming\\Engine\\Source", "OutputDirectory": "F:\\TAMO_Streaming\\Engine\\Intermediate\\Build\\Win64\\ShaderCompileWorker\\Inc\\VectorVM\\UHT", "ClassesHeaders": [], "PublicHeaders": ["F:\\TAMO_Streaming\\Engine\\Source\\Runtime\\VectorVM\\Public\\VectorVMCommon.h"], "InternalHeaders": [], "PrivateHeaders": [], "PublicDefines": ["VECTORVM_SUPPORTS_EXPERIMENTAL=1", "VECTORVM_SUPPORTS_LEGACY=1", "VECTORVM_SUPPORTS_SERIALIZATION=0", "VECTORVM_DEBUG_PRINTF=0"], "GeneratedCPPFilenameBase": "F:\\TAMO_Streaming\\Engine\\Intermediate\\Build\\Win64\\ShaderCompileWorker\\Inc\\VectorVM\\UHT\\VectorVM.gen", "SaveExportedHeaders": true, "UHTGeneratedCodeVersion": "None", "VersePath": "", "VerseScope": "PublicUser", "HasVerse": false, "VersePluginName": ""}, {"Name": "SourceControl", "ModuleType": "EngineDeveloper", "OverrideModuleType": "None", "BaseDirectory": "F:\\TAMO_Streaming\\Engine\\Source\\Developer\\SourceControl", "IncludeBase": "F:\\TAMO_Streaming\\Engine\\Source", "OutputDirectory": "F:\\TAMO_Streaming\\Engine\\Intermediate\\Build\\Win64\\ShaderCompileWorker\\Inc\\SourceControl\\UHT", "ClassesHeaders": [], "PublicHeaders": ["F:\\TAMO_Streaming\\Engine\\Source\\Developer\\SourceControl\\Public\\SourceControlHelpers.h", "F:\\TAMO_Streaming\\Engine\\Source\\Developer\\SourceControl\\Public\\SourceControlPreferences.h"], "InternalHeaders": [], "PrivateHeaders": [], "PublicDefines": ["SOURCE_CONTROL_WITH_SLATE=1"], "GeneratedCPPFilenameBase": "F:\\TAMO_Streaming\\Engine\\Intermediate\\Build\\Win64\\ShaderCompileWorker\\Inc\\SourceControl\\UHT\\SourceControl.gen", "SaveExportedHeaders": true, "UHTGeneratedCodeVersion": "None", "VersePath": "", "VerseScope": "PublicUser", "HasVerse": false, "VersePluginName": ""}, {"Name": "SourceCodeAccess", "ModuleType": "EngineDeveloper", "OverrideModuleType": "None", "BaseDirectory": "F:\\TAMO_Streaming\\Engine\\Source\\Developer\\SourceCodeAccess", "IncludeBase": "F:\\TAMO_Streaming\\Engine\\Source", "OutputDirectory": "F:\\TAMO_Streaming\\Engine\\Intermediate\\Build\\Win64\\ShaderCompileWorker\\Inc\\SourceCodeAccess\\UHT", "ClassesHeaders": [], "PublicHeaders": [], "InternalHeaders": [], "PrivateHeaders": ["F:\\TAMO_Streaming\\Engine\\Source\\Developer\\SourceCodeAccess\\Private\\SourceCodeAccessSettings.h"], "PublicDefines": [], "GeneratedCPPFilenameBase": "F:\\TAMO_Streaming\\Engine\\Intermediate\\Build\\Win64\\ShaderCompileWorker\\Inc\\SourceCodeAccess\\UHT\\SourceCodeAccess.gen", "SaveExportedHeaders": true, "UHTGeneratedCodeVersion": "None", "VersePath": "", "VerseScope": "PublicUser", "HasVerse": false, "VersePluginName": ""}, {"Name": "ToolMenus", "ModuleType": "EngineDeveloper", "OverrideModuleType": "None", "BaseDirectory": "F:\\TAMO_Streaming\\Engine\\Source\\Developer\\ToolMenus", "IncludeBase": "F:\\TAMO_Streaming\\Engine\\Source", "OutputDirectory": "F:\\TAMO_Streaming\\Engine\\Intermediate\\Build\\Win64\\ShaderCompileWorker\\Inc\\ToolMenus\\UHT", "ClassesHeaders": [], "PublicHeaders": ["F:\\TAMO_Streaming\\Engine\\Source\\Developer\\ToolMenus\\Public\\ToolMenuContext.h", "F:\\TAMO_Streaming\\Engine\\Source\\Developer\\ToolMenus\\Public\\ToolMenuEntry.h", "F:\\TAMO_Streaming\\Engine\\Source\\Developer\\ToolMenus\\Public\\ToolMenu.h", "F:\\TAMO_Streaming\\Engine\\Source\\Developer\\ToolMenus\\Public\\ToolMenuEntryScript.h", "F:\\TAMO_Streaming\\Engine\\Source\\Developer\\ToolMenus\\Public\\ToolMenuMisc.h", "F:\\TAMO_Streaming\\Engine\\Source\\Developer\\ToolMenus\\Public\\ToolMenuDelegates.h", "F:\\TAMO_Streaming\\Engine\\Source\\Developer\\ToolMenus\\Public\\ToolMenuOwner.h", "F:\\TAMO_Streaming\\Engine\\Source\\Developer\\ToolMenus\\Public\\ToolMenus.h", "F:\\TAMO_Streaming\\Engine\\Source\\Developer\\ToolMenus\\Public\\ToolMenuSection.h", "F:\\TAMO_Streaming\\Engine\\Source\\Developer\\ToolMenus\\Public\\ToolMenuWidgetCollectionContext.h"], "InternalHeaders": [], "PrivateHeaders": ["F:\\TAMO_Streaming\\Engine\\Source\\Developer\\ToolMenus\\Private\\ToolMenusBlueprintLibrary.h"], "PublicDefines": [], "GeneratedCPPFilenameBase": "F:\\TAMO_Streaming\\Engine\\Intermediate\\Build\\Win64\\ShaderCompileWorker\\Inc\\ToolMenus\\UHT\\ToolMenus.gen", "SaveExportedHeaders": true, "UHTGeneratedCodeVersion": "None", "VersePath": "", "VerseScope": "PublicUser", "HasVerse": false, "VersePluginName": ""}, {"Name": "DeveloperToolSettings", "ModuleType": "EngineDeveloper", "OverrideModuleType": "None", "BaseDirectory": "F:\\TAMO_Streaming\\Engine\\Source\\Developer\\DeveloperToolSettings", "IncludeBase": "F:\\TAMO_Streaming\\Engine\\Source", "OutputDirectory": "F:\\TAMO_Streaming\\Engine\\Intermediate\\Build\\Win64\\ShaderCompileWorker\\Inc\\DeveloperToolSettings\\UHT", "ClassesHeaders": ["F:\\TAMO_Streaming\\Engine\\Source\\Developer\\DeveloperToolSettings\\Classes\\CookerSettings.h", "F:\\TAMO_Streaming\\Engine\\Source\\Developer\\DeveloperToolSettings\\Classes\\Settings\\PlatformsMenuSettings.h", "F:\\TAMO_Streaming\\Engine\\Source\\Developer\\DeveloperToolSettings\\Classes\\Settings\\ProjectPackagingSettings.h"], "PublicHeaders": [], "InternalHeaders": [], "PrivateHeaders": [], "PublicDefines": [], "GeneratedCPPFilenameBase": "F:\\TAMO_Streaming\\Engine\\Intermediate\\Build\\Win64\\ShaderCompileWorker\\Inc\\DeveloperToolSettings\\UHT\\DeveloperToolSettings.gen", "SaveExportedHeaders": true, "UHTGeneratedCodeVersion": "None", "VersePath": "", "VerseScope": "PublicUser", "HasVerse": false, "VersePluginName": ""}, {"Name": "UnrealEdMessages", "ModuleType": "EngineEditor", "OverrideModuleType": "None", "BaseDirectory": "F:\\TAMO_Streaming\\Engine\\Source\\Editor\\UnrealEdMessages", "IncludeBase": "F:\\TAMO_Streaming\\Engine\\Source", "OutputDirectory": "F:\\TAMO_Streaming\\Engine\\Intermediate\\Build\\Win64\\ShaderCompileWorker\\Inc\\UnrealEdMessages\\UHT", "ClassesHeaders": ["F:\\TAMO_Streaming\\Engine\\Source\\Editor\\UnrealEdMessages\\Classes\\AssetEditorMessages.h", "F:\\TAMO_Streaming\\Engine\\Source\\Editor\\UnrealEdMessages\\Classes\\FileServerMessages.h"], "PublicHeaders": [], "InternalHeaders": [], "PrivateHeaders": [], "PublicDefines": [], "GeneratedCPPFilenameBase": "F:\\TAMO_Streaming\\Engine\\Intermediate\\Build\\Win64\\ShaderCompileWorker\\Inc\\UnrealEdMessages\\UHT\\UnrealEdMessages.gen", "SaveExportedHeaders": true, "UHTGeneratedCodeVersion": "None", "VersePath": "", "VerseScope": "PublicUser", "HasVerse": false, "VersePluginName": ""}], "UhtPlugins": []}