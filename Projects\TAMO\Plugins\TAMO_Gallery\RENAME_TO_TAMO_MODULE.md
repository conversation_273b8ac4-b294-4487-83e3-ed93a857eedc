# VTSTools → TAMO_Module 重命名指南

## 🎯 **重命名目标**

将整个插件从VTSTools重命名为TAMO_Module，包括：
- 文件夹名称
- 文件名称  
- 类名和模块名
- 所有内部引用

## ✅ **已完成的代码修改**

### 1. 插件描述文件修改
**VTSTools.uplugin**:
```json
// 修改前
{"Name": "VTSTools", "Type": "Editor", "LoadingPhase": "Default"}

// 修改后  
{"Name": "TAMO_Module", "Type": "Editor", "LoadingPhase": "Default"}
```

### 2. Build文件修改
**VTSTools.Build.cs**:
```csharp
// 修改前
public class VTSTools : ModuleRules
{
    public VTSTools(ReadOnlyTargetRules Target) : base(Target)

// 修改后
public class TAMO_Module : ModuleRules  
{
    public TAMO_Module(ReadOnlyTargetRules Target) : base(Target)
```

### 3. 模块头文件修改
**VTSToolsModule.h**:
```cpp
// 修改前
class FVTSToolsModule : public IModuleInterface
void CreateVTSToolsMenu();

// 修改后
class FTAMO_ModuleModule : public IModuleInterface
void CreateTAMOModuleMenu();
```

### 4. 模块实现文件修改
**VTSToolsModule.cpp**:
```cpp
// 修改前
#include "VTSToolsModule.h"
#define LOCTEXT_NAMESPACE "FVTSToolsModule"
const FName FVTSToolsModule::CPDTabId(TEXT("CPDTab"));
void FVTSToolsModule::StartupModule()
IMPLEMENT_MODULE(FVTSToolsModule, VTSTools)

// 修改后
#include "TAMO_ModuleModule.h"
#define LOCTEXT_NAMESPACE "FTAMO_ModuleModule"  
const FName FTAMO_ModuleModule::CPDTabId(TEXT("CPDTab"));
void FTAMO_ModuleModule::StartupModule()
IMPLEMENT_MODULE(FTAMO_ModuleModule, TAMO_Module)
```

## 🚀 **需要手动完成的文件重命名**

### 步骤1: 关闭所有程序
```
1. 关闭UE编辑器
2. 关闭Visual Studio
3. 关闭所有相关程序
```

### 步骤2: 重命名文件夹结构
```
原路径: Projects/TAMO/Plugins/VTSTools/
新路径: Projects/TAMO/Plugins/TAMO_Module/

原路径: Projects/TAMO/Plugins/TAMO_Module/Source/VTSTools/
新路径: Projects/TAMO/Plugins/TAMO_Module/Source/TAMO_Module/
```

### 步骤3: 重命名文件
```
VTSTools.uplugin → TAMO_Module.uplugin
VTSTools.Build.cs → TAMO_Module.Build.cs
VTSToolsModule.h → TAMO_ModuleModule.h
VTSToolsModule.cpp → TAMO_ModuleModule.cpp
```

### 步骤4: 更新SCPDWindow.cpp中的include
```cpp
// 需要修改
#include "VTSToolsModule.h"  
// 改为
#include "TAMO_ModuleModule.h"
```

## 📋 **完整的重命名清单**

### 文件夹重命名
- [ ] `VTSTools/` → `TAMO_Module/`
- [ ] `Source/VTSTools/` → `Source/TAMO_Module/`

### 文件重命名  
- [ ] `VTSTools.uplugin` → `TAMO_Module.uplugin`
- [ ] `VTSTools.Build.cs` → `TAMO_Module.Build.cs`
- [ ] `VTSToolsModule.h` → `TAMO_ModuleModule.h`
- [ ] `VTSToolsModule.cpp` → `TAMO_ModuleModule.cpp`

### 代码引用更新
- [x] 插件描述文件中的模块名
- [x] Build.cs中的类名
- [x] 模块头文件中的类名和方法名
- [x] 模块实现文件中的所有引用
- [ ] SCPDWindow.cpp中的include引用

## ⚠️ **重要注意事项**

### 重命名顺序
1. **先完成代码修改**（已完成）
2. **再进行文件重命名**（需要手动完成）
3. **最后更新项目引用**

### 可能的问题和解决方案

#### 问题1: 编译错误
```
解决方案: 确保所有include路径正确更新
检查: #include "TAMO_ModuleModule.h"
```

#### 问题2: 模块加载失败
```
解决方案: 检查.uplugin文件中的模块名是否正确
确认: "Name": "TAMO_Module"
```

#### 问题3: 菜单不显示
```
解决方案: 重新生成项目文件
操作: 右键.uproject → Generate Visual Studio project files
```

## 🚀 **重命名后的验证步骤**

### 1. 编译测试
```bash
Build -> Rebuild Solution
```

### 2. 功能测试
```
1. 启动UE编辑器
2. 检查菜单: TA Toolbar -> CPD Manager
3. 测试CPD功能是否正常
4. 检查插件管理器中的显示名称
```

### 3. 插件信息验证
```
Edit -> Plugins -> 搜索 "TAMO_Module"
确认显示: "TA Toolbar"
```

## 📁 **最终的文件结构**

```
Projects/TAMO/Plugins/TAMO_Module/
├── TAMO_Module.uplugin
├── Source/
│   └── TAMO_Module/
│       ├── TAMO_Module.Build.cs
│       ├── Public/
│       │   ├── TAMO_ModuleModule.h
│       │   └── SCPDWindow.h
│       └── Private/
│           ├── TAMO_ModuleModule.cpp
│           └── SCPDWindow.cpp
```

## 状态: 🔄 代码修改完成，等待文件重命名

- ✅ **代码修改**: 所有类名、方法名、引用已更新
- ⏳ **文件重命名**: 需要手动重命名文件和文件夹
- ⏳ **最终测试**: 重命名完成后需要编译测试

完成文件重命名后，TAMO_Module插件就可以正常使用了！🎉
