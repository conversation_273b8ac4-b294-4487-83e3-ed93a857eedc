# Reference Checker 使用指南

## 🎯 **简化的工作流程**

Reference Checker工具现在采用简单直接的操作方式：

### **步骤1: 选择Actor**
```
在场景中选择一个或多个你想检查的Actor
```

### **步骤2: 打开工具**
```
主菜单 -> TAMO -> Reference Checker
```

### **步骤3: 执行检查**
```
点击 "Check References" 按钮
工具会检查当前选中的所有Actor
```

### **步骤4: 查看结果**
```
在结果列表中查看所有引用信息
- 绿色 = 引用正常
- 红色 = 引用缺失
- 黄色 = 循环依赖
```

## 🔧 **简化的功能特性**

### **✅ 保留的功能**
- ✅ **按需检查**: 只在点击按钮时检查引用
- ✅ **当前选择**: 检查当前选中的Actor（不缓存选择）
- ✅ **完整分析**: 检查网格、材质、纹理等所有引用
- ✅ **问题检测**: 检测缺失引用和循环依赖
- ✅ **颜色高亮**: 直观的问题标识
- ✅ **详细信息**: 显示Actor、组件、资源路径等详细信息

### **❌ 移除的功能**
- ❌ **实时更新**: 不再监听选择变化
- ❌ **自动刷新**: 不再自动更新界面
- ❌ **定时器**: 没有任何后台定时器
- ❌ **选择监听**: 不监听Actor选择事件

## 📋 **界面说明**

### **窗口布局**
```
┌─────────────────────────────────────────┐
│              Reference Checker           │
├─────────────────────────────────────────┤
│ Selected Actors: 3                      │  ← 显示当前选中数量
├─────────────────────────────────────────┤
│ [Check References] [Clear Results]      │  ← 手动操作按钮
├─────────────────────────────────────────┤
│ Check Results - Total: 15 | Valid: 12...│  ← 检查结果统计
├─────────────────────────────────────────┤
│ Actor │ Component │ Type │ Asset │ Status│  ← 详细结果列表
└─────────────────────────────────────────┘
```

### **操作说明**
- **Selected Actors**: 显示当前在场景中选中的Actor数量
- **Check References**: 点击检查当前选中Actor的所有引用
- **Clear Results**: 清除检查结果列表
- **结果统计**: 显示检查完成后的统计信息

## 🎨 **检查结果示例**

### **正常场景**
```
选中: StaticMeshActor_1, StaticMeshActor_2

点击 "Check References" 后:

Check Results - Total: 8 | Valid: 7 | Issues: 1

Actor           | Component    | Type     | Asset Path              | Status | Issue
----------------|--------------|----------|------------------------|--------|--------
StaticMeshActor1| StaticMesh   | Mesh     | /Game/Meshes/Cube      | Valid  |
StaticMeshActor1| StaticMesh   | Material | /Game/Materials/M_Base | Valid  |
StaticMeshActor1| StaticMesh   | Texture  | /Game/Textures/T_Diff  | Valid  |
StaticMeshActor2| StaticMesh   | Mesh     | /Game/Meshes/Sphere    | Valid  |
StaticMeshActor2| StaticMesh   | Material | None                   | Missing| Material slot 0 is empty
```

### **问题检测**
- 🔴 **Missing**: 资源引用缺失或为None
- 🟡 **Circular**: 检测到循环依赖
- 🟢 **Valid**: 引用正常，资源存在

## 🚀 **使用技巧**

### **1. 批量检查**
```
选择多个Actor → 一次性检查所有引用
适合检查整个场景区域的资源问题
```

### **2. 单个分析**
```
选择单个Actor → 详细分析其所有组件引用
适合深入分析特定对象的问题
```

### **3. 问题定位**
```
查看Issue列的具体描述
根据Asset Path定位问题资源
使用Status颜色快速识别问题类型
```

### **4. 结果管理**
```
使用Clear Results清除旧结果
重新选择Actor后再次检查
每次检查都是基于当前选择
```

## 📊 **日志输出**

检查完成后，工具会在Output Log中输出摘要信息：
```
LogTemp: Reference Check Complete: 2 actors, 8 total references, 1 issues
```

## ⚡ **性能特点**

- **按需执行**: 只在点击按钮时执行检查
- **无后台任务**: 没有定时器或监听器
- **即时结果**: 检查完成立即显示结果
- **内存友好**: 不缓存选择状态或监听事件

## 🎯 **适用场景**

### **开发阶段**
- 检查新添加Actor的引用完整性
- 验证材质和纹理分配是否正确
- 发现缺失的资源引用

### **优化阶段**
- 检测循环依赖问题
- 清理无效引用
- 验证资源引用的有效性

### **发布前检查**
- 批量检查场景中所有重要Actor
- 确保没有缺失的资源引用
- 验证所有引用都指向有效资源

## 状态: ✅ 简化版Reference Checker完成

现在Reference Checker工具具有：
- ✅ **简单操作**: 选择 → 点击 → 查看结果
- ✅ **按需检查**: 只在需要时执行检查
- ✅ **完整功能**: 保留所有核心检查功能
- ✅ **清晰界面**: 直观的结果显示和问题标识
- ✅ **高性能**: 无后台任务，按需执行

Reference Checker现在是一个简单高效的引用检查工具！🎉
