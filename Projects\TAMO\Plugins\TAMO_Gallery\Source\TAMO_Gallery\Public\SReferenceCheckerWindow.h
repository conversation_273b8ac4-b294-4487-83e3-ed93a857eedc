#pragma once

#include "CoreMinimal.h"
#include "Widgets/SCompoundWidget.h"
#include "Widgets/Views/SListView.h"
#include "Widgets/Text/STextBlock.h"
#include "Widgets/Input/SButton.h"
#include "Widgets/Layout/SScrollBox.h"
#include "Engine/World.h"
#include "Engine/Selection.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "TAMO_Gallery.h"

// 引用信息结构 - 普通结构体
struct FReferenceInfo
{
	// 引用的资源路径
	FString AssetPath;

	// 引用类型 (Material, Texture, Mesh, etc.)
	FString ReferenceType;

	// 引用状态 (Valid, Missing, Circular, etc.)
	FString Status;

	// 是否有问题
	bool bHasIssue;

	// 问题描述
	FString IssueDescription;

	// 引用的Actor名称
	FString ActorName;

	// 组件名称
	FString ComponentName;

	// 默认构造函数
	FReferenceInfo()
		: bHasIssue(false)
	{
	}

	// 带参数的构造函数
	FReferenceInfo(const FString& InAssetPath, const FString& InReferenceType, const FString& InStatus,
		bool InHasIssue = false, const FString& InIssueDescription = TEXT(""),
		const FString& InActorName = TEXT(""), const FString& InComponentName = TEXT(""))
		: AssetPath(InAssetPath)
		, ReferenceType(InReferenceType)
		, Status(InStatus)
		, bHasIssue(InHasIssue)
		, IssueDescription(InIssueDescription)
		, ActorName(InActorName)
		, ComponentName(InComponentName)
	{
	}
};

/**
 * Reference Checker Window - 检查选中Actor的所有引用信息
 */
class TAMO_GALLERY_API SReferenceCheckerWindow : public SCompoundWidget
{
public:
	SLATE_BEGIN_ARGS(SReferenceCheckerWindow) {}
	SLATE_END_ARGS()

	/** Constructs this widget with InArgs */
	void Construct(const FArguments& InArgs);

private:
	/** 检查引用按钮点击事件 */
	FReply OnCheckReferencesClicked();
	
	/** 清除结果按钮点击事件 */
	FReply OnClearResultsClicked();
	
	/** 获取当前选中的Actor数量 */
	int32 GetCurrentSelectedActorCount() const;
	
	/** 检查单个Actor的引用 */
	void CheckActorReferences(AActor* Actor, TArray<TSharedPtr<FReferenceInfo>>& OutReferences);
	
	/** 检查静态网格组件的引用 */
	void CheckStaticMeshComponentReferences(class UStaticMeshComponent* MeshComp, const FString& ActorName, TArray<TSharedPtr<FReferenceInfo>>& OutReferences);
	
	/** 检查骨骼网格组件的引用 */
	void CheckSkeletalMeshComponentReferences(class USkeletalMeshComponent* SkelMeshComp, const FString& ActorName, TArray<TSharedPtr<FReferenceInfo>>& OutReferences);

	/** 检查Landscape组件的引用 */
	void CheckLandscapeComponentReferences(void* LandscapeComp, const FString& ActorName, TArray<TSharedPtr<FReferenceInfo>>& OutReferences);
	
	/** 检查材质引用 */
	void CheckMaterialReferences(class UMaterialInterface* Material, const FString& ActorName, const FString& ComponentName, TArray<TSharedPtr<FReferenceInfo>>& OutReferences);
	
	/** 检查纹理引用 */
	void CheckTextureReferences(class UTexture* Texture, const FString& ActorName, const FString& ComponentName, TArray<TSharedPtr<FReferenceInfo>>& OutReferences);
	
	/** 验证资源是否存在 */
	bool IsAssetValid(const FString& AssetPath);
	
	/** 检测循环依赖 */
	bool HasCircularDependency(const FString& AssetPath, TSet<FString>& VisitedAssets);
	
	/** 生成引用列表项的Widget */
	TSharedRef<ITableRow> OnGenerateReferenceRow(TSharedPtr<FReferenceInfo> Item, const TSharedRef<STableViewBase>& OwnerTable);
	
	/** 获取引用状态的颜色 */
	FSlateColor GetReferenceStatusColor(TSharedPtr<FReferenceInfo> Item) const;
	
	/** 获取当前选中的Actor数量文本 */
	FText GetSelectedActorCountText() const;
	
	/** 获取检查结果统计文本 */
	FText GetCheckResultsText() const;

private:
	/** 引用信息列表 */
	TArray<TSharedPtr<FReferenceInfo>> ReferenceInfoList;
	
	/** 引用列表视图 */
	TSharedPtr<SListView<TSharedPtr<FReferenceInfo>>> ReferenceListView;
	
	/** 选中的Actor数量 */
	int32 SelectedActorCount;
	
	/** 检查结果统计 */
	int32 TotalReferences;
	int32 ValidReferences;
	int32 IssueReferences;
	
	/** 滚动框 */
	TSharedPtr<SScrollBox> ResultsScrollBox;
	
	/** 检查按钮 */
	TSharedPtr<SButton> CheckButton;
	
	/** 清除按钮 */
	TSharedPtr<SButton> ClearButton;
};
