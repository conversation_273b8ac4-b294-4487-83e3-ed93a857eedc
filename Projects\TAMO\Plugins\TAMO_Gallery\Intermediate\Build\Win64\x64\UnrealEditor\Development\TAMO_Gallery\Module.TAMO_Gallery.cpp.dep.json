{"Version": "1.2", "Data": {"Source": "f:\\tamo_streaming\\projects\\tamo\\plugins\\tamo_gallery\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\tamo_gallery\\module.tamo_gallery.cpp", "ProvidedModule": "", "PCH": "f:\\tamo_streaming\\engine\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.cpp20.h.pch", "Includes": ["f:\\tamo_streaming\\projects\\tamo\\plugins\\tamo_gallery\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\tamo_gallery\\definitions.tamo_gallery.h", "f:\\tamo_streaming\\projects\\tamo\\plugins\\tamo_gallery\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\tamo_gallery\\permoduleinline.gen.cpp", "f:\\tamo_streaming\\engine\\source\\runtime\\core\\public\\hal\\permoduleinline.inl", "f:\\tamo_streaming\\projects\\tamo\\plugins\\tamo_gallery\\source\\tamo_gallery\\private\\scpdwindow.cpp", "f:\\tamo_streaming\\projects\\tamo\\plugins\\tamo_gallery\\source\\tamo_gallery\\public\\scpdwindow.h", "f:\\tamo_streaming\\engine\\source\\runtime\\engine\\classes\\engine\\selection.h", "f:\\tamo_streaming\\engine\\source\\editor\\unrealed\\public\\selection.h", "f:\\tamo_streaming\\engine\\source\\runtime\\typedelementframework\\public\\elements\\framework\\typedelementlist.h", "f:\\tamo_streaming\\engine\\source\\runtime\\typedelementframework\\public\\elements\\framework\\typedelementcounter.h", "f:\\tamo_streaming\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\typedelementframework\\uht\\typedelementcounter.generated.h", "f:\\tamo_streaming\\engine\\source\\runtime\\typedelementruntime\\public\\elements\\framework\\typedelementselectionset.h", "f:\\tamo_streaming\\engine\\source\\runtime\\typedelementruntime\\public\\elements\\framework\\typedelementinterfacecustomization.h", "f:\\tamo_streaming\\engine\\source\\runtime\\typedelementruntime\\public\\elements\\framework\\typedelementlistobjectutil.h", "f:\\tamo_streaming\\engine\\source\\runtime\\typedelementruntime\\public\\elements\\interfaces\\typedelementselectioninterface.h", "f:\\tamo_streaming\\engine\\source\\runtime\\typedelementframework\\public\\elements\\framework\\typedelementlistproxy.h", "f:\\tamo_streaming\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\typedelementframework\\uht\\typedelementlistproxy.generated.h", "f:\\tamo_streaming\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\typedelementruntime\\uht\\typedelementselectioninterface.generated.h", "f:\\tamo_streaming\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\typedelementruntime\\uht\\typedelementselectionset.generated.h", "f:\\tamo_streaming\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealed\\uht\\selection.generated.h", "f:\\tamo_streaming\\engine\\source\\runtime\\slate\\public\\widgets\\input\\sspinbox.h", "f:\\tamo_streaming\\projects\\tamo\\plugins\\tamo_gallery\\source\\tamo_gallery\\private\\tamo_gallery.cpp", "f:\\tamo_streaming\\projects\\tamo\\plugins\\tamo_gallery\\source\\tamo_gallery\\public\\tamo_gallery.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}